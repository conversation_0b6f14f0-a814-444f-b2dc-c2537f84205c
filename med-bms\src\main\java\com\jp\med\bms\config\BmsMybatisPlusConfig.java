package com.jp.med.bms.config;

import com.github.pagehelper.autoconfigure.PageHelperAutoConfiguration;
import com.jp.med.bms.interceptors.DataAuthIntercoptor;
import com.jp.med.common.interceptors.JSQLInterceptorProxy;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/25 12:00
 * @description:
 */
@Configuration
public class BmsMybatisPlusConfig {

    @Autowired
    private List<SqlSessionFactory> sqlSessionFactoryList;

    @Autowired
    PageHelperAutoConfiguration pageHelperAutoConfiguration;

    @PostConstruct
    public void registerInterceptor() {
        JSQLInterceptorProxy authInterceptor = new JSQLInterceptorProxy(new DataAuthIntercoptor());
        for (SqlSessionFactory sqlSessionFactory : sqlSessionFactoryList) {
            org.apache.ibatis.session.Configuration configuration = sqlSessionFactory.getConfiguration();
            if (!containsInterceptor(configuration, authInterceptor)) {
                configuration.addInterceptor(authInterceptor);
            }
        }
    }

    private boolean containsInterceptor(org.apache.ibatis.session.Configuration configuration, Interceptor interceptor) {
        try {
            return configuration.getInterceptors().stream().anyMatch((config) -> interceptor.getClass().isAssignableFrom(config.getClass()));
        } catch (Exception var4) {
            return false;
        }
    }
}
