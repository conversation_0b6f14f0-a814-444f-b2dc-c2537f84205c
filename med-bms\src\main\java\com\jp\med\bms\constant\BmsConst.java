package com.jp.med.bms.constant;

public interface BmsConst {

    /**
     * 编制科室进行填报
     */
    String PTR_1 = "1";
    /**
     * 预算科室进行填报
     */
    String PTR_2 = "2";
    /**
     * 归口科室进行填报
     */
    String PTR_3 = "3";
    /**
     * 管理委员进行填报
     */
    String PTR_4 = "4";

    /**
     * 预算填报状态-填报中
     */
    String FILLING_0 = "0";
    /**
     * 预算填报状态-已提交
     */
    String FILLING_1 = "1";
    /**
     * 预算填报状态-已驳回
     */
    String FILLING_2 = "2";
    /**
     * 预算填报状态-填报完成
     */
    String FILLING_3 = "3";


    //-------------------查询支出类预算执行情况-固定的会计科目-start--------------------
    /**
     * 固定资产折旧
     **/
    String ACTIG_GDZCZJ_CODE = "51010403";

    /**
     * 无形资产摊销
     **/
    String ACTIG_WXZCTX_CODE = "50010404";

    /**
     * 财政项目支出
     **/
    String ACTIG_CZXMZC_CODE = "330101";

    /**
     * 会计科目 固定资产折旧对应预算编码
     **/
    String ACTIG_GDZCZJ_BUDGET_CODE = "GDZCZJF";

    /**
     * 会计科目 无形资产摊销对应预算编码
     **/
    String ACTIG_WXZCTX_BUDGET_CODE = "WXZCTXF";

    /**
     * 会计科目 财政支出对应预算编码
     **/
    String ACTIG_CZXMZC_BUDGET_CODE = "CZXMZC";
    //-------------------查询支出类预算执行情况-固定的会计科目-end----------------------

    //-------------------查询支出类预算执行情况-需要展示明细的经济科目项-start--------------------

    /**
     * 差旅费
     **/
    String ECON_SHOW_DETAILS_TRAVEL = "30211";

    /**
     * 培训费
     **/
    String ECON_SHOW_DETAILS_TRAINING = "30216";
    //-------------------查询支出类预算执行情况-需要展示明细的经济科目项-end----------------------


    //----------------- 用友支出类预算执行情况查询类型-start----------------
    //查询预算项年度实际总发生值（差旅费、培训费为明细）
    public static String YY_BUDGET_TOTAL_INFO = "1";
    //查询某经济科目明细
    String YY_BUDGET_ECON_DETAIL_INFO = "2";
    //查询某会计科目明细
    String YY_BUDGET_ACTIG_DETAIL_INFO = "3";
    //----------------- 用友支出类预算执行情况查询类型-end----------------

    //-------------------预算编制项目类型-start--------------------
    /**
     * 支出类
     **/
    String BUDGET_TYPE_ZCL = "ZCL";

    /**
     * 收入类
     **/
    String BUDGET_TYPE_SR = "SR";
    //-------------------预算编制项目类型-end----------------------

    //-------------------预算编制项目单独计算药品费用-start--------------------
    /**
     * 门诊人次
     **/
    String BUDGET_CODE_MZRC = "MZRC";

    /**
     * 出院人次
     **/
    String BUDGET_CODE_CYRC = "CYRC";

    /**
     * 门诊均次药品费用
     **/
    String BUDGET_CODE_AVGDRUGFEE_YZB = "YZB";

    /**
     * 住院均次药品费用
     **/
    String BUDGET_CODE_AVGDRUGFEE_YZB1 = "YZB1";

    /**
     * 住院均次药品费用（中医）
     **/
    String BUDGET_CODE_AVGDRUGFEE_YZB2 = "YZB2";

    /**
     * 西药费编制代码
     */
    String BUDGET_TOTALDRUGFEE_WESTERN = "XY";

    /**
     * 中草药费编制代码
     */
    String BUDGET_TOTALDRUGFEE_CHINESE = "ZCY";

    /**
     * 临床药学科代码
     */
    String PHARMACY_DEPT_ORGCODE = "312";

    /**
     * 中医科住院编码
     */
    String TRAD_CHINESE_DEPT_CODE = "232001";

    /**
     * 商品及服务类合计
     */
    String BUDGET_TOTAL_SPFW_NAME = "商品和服务支出";

    /**
     * 二级归口类型
     */
    String BUDGET_STATISTICS_TYPE_LV2 = "2";

    /**
     * 一级归口类型
     */
    String BUDGET_STATISTICS_TYPE_LV1 = "1";

    //-------------------预算编制项目单独计算药品费用-end----------------------

    /**
     * 培训费预算通用前缀
     */
    String BUDGET_BASE_CODE_PXF = "PXF";

    /**
     * 差旅费预算通用前缀
     */
    String BUDGET_BASE_CODE_CLF = "CLF";

    //-------------------预算工作量常量-start--------------------
    /**
     * 年份范围类型常量
     */
    interface YearRangeType {
        /** 2025年之前 */
        String BEFORE_2025 = "BEFORE_2025";
        /** 2025年 */
        String YEAR_2025 = "YEAR_2025";
        /** 2025年之后 */
        String AFTER_2025 = "AFTER_2025";
    }

    /**
     * 部门类型常量
     */
    interface DeptType {
        /** 职能部门 */
        String FUNCTIONAL = "1";
        /** 临床部门 */
        String CLINICAL = "2";
    }

    /**
     * 预算代码常量
     */
    interface BudgetCode {
        /** 培训费 */
        String PXF = "PXF";
        /** 差旅费 */
        String CLF = "CLF";
        /** 培训费(职能) */
        String PXF_ZN = "PXF_ZN";
        /** 学术会议 */
        String XSHYDQPX = "XSHYDQPX";
        /** 进修经费 */
        String JXJF = "JXJF";
        /** 医院指令性任务 */
        String YYZLXRWPXJF = "YYZLXRWPXJF";
        /** 继续教育 */
        String JXYXJYXMJF = "JXYXJYXMJF";
        /** 科研项目 */
        String KYXMPXJF = "KYXMPXJF";
    }

    /**
     * 表格列键常量
     */
    interface TableColumnKey {
        /** 组织名称列键 */
        String ORG_NAME = "orgName";
        /** 组织ID列键 */
        String ORG_ID = "orgId";
        /** 项目列索引(2025年前) */
        String PROJECT_COLUMN_INDEX_BEFORE_2025 = "col_7";
        /** 项目列索引(2025年后) */
        String PROJECT_COLUMN_INDEX_AFTER_2025 = "col_22";
        /** 右侧项目列索引(2025年前) */
        String RIGHT_PROJECT_COLUMN_INDEX_BEFORE_2025 = "col_11";
        /** 右侧项目列索引(2025年后) */
        String RIGHT_PROJECT_COLUMN_INDEX_AFTER_2025 = "col_26";
    }

    /**
     * 表格渲染器常量
     */
    interface TableRenderKey {
        /** 左侧领导部门键 */
        String LEFT_LEAD_DEPT = "left_leadDept";
        /** 左侧类型键 */
        String LEFT_TYPE = "left_type";
        /** 左侧预算代码键 */
        String LEFT_BUDGET_CODE = "left_budgetCode";
        /** 左侧汇总键 */
        String LEFT_SUMMARY = "left_summary";
        /** 右侧领导部门键 */
        String RIGHT_LEAD_DEPT = "right_leadDept";
        /** 右侧类型键 */
        String RIGHT_TYPE = "right_type";
        /** 右侧预算代码键 */
        String RIGHT_BUDGET_CODE = "right_budgetCode";
        /** 右侧汇总键 */
        String RIGHT_SUMMARY = "right_summary";
    }

    /**
     * 表格样式常量
     */
    interface TableStyle {
        /** 固定在左侧 */
        String FIXED_LEFT = "left";
        /** 表格列默认宽度 */
        String DEFAULT_WIDTH = "150";
        /** 表格列项目宽度 */
        String PROJECT_WIDTH = "100";
        /** 默认排序方式 */
        String DEFAULT_SORTER = "default";
    }

    /**
     * 渲染器类型常量
     */
    interface RenderType {
        /** 项目类型 */
        String PROJECT = "project";
    }

    /**
     * 年份常量
     */
    interface Year {
        /** 2025年 */
        int YEAR_2025 = 2025;
    }
    //-------------------预算工作量常量-end----------------------
}
