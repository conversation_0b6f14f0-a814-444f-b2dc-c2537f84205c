package com.jp.med.bms.interceptors;

import cn.hutool.core.util.ObjectUtil;
import com.jp.med.common.dto.common.CommonQueryDto;
import com.jp.med.common.interceptors.JSQLInterceptor;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.plugin.Invocation;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/25 11:58
 * @description:
 */
public class DataAuthIntercoptor implements JSQLInterceptor {
    @Override
    public String getNewSql(CommonQueryDto commonQueryDto, BoundSql boundSql) {
        String newSql = boundSql.getSql();
        if (ObjectUtil.isNotNull(commonQueryDto)) {
            if (StringUtils.isNotEmpty(commonQueryDto.getCurSysOrgId()) && StringUtils.isNotEmpty(commonQueryDto.getSysUser().getBmsUser().getDeptType())) {
//                newSql = "SELECT * FROM (" + newSql + ") tmp WHERE orgId='" + commonQueryDto.getCurSysOrgId() + "'";
            }
        }
        return newSql;
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        return invocation.proceed();
    }
}
