package com.jp.med.bms.modules.analysis.controller;

import com.jp.med.bms.modules.analysis.dto.BmsAnalysisDto;
import com.jp.med.bms.modules.analysis.service.read.BmsAnalysisReadService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @ClassName BmsAnalysisController
 * @Description 预算分析
 * <AUTHOR>
 * @Date 2024/1/18 14:44
 * @Version 1.0
 */
@Api(value = "预算分析", tags = "预算分析")
@RestController
@RequestMapping("bmsAnalysis")
public class BmsAnalysisController {

    @Resource
    private BmsAnalysisReadService bmsAnalysisReadService;

    @ApiOperation("服务质量分析")
    @PostMapping("/queryQosAnalysis")
    public CommonResult<?> queryQosAnalysis(@RequestBody BmsAnalysisDto dto){
        return CommonResult.success(bmsAnalysisReadService.queryQosAnalysis(dto));
    }

    @ApiOperation("服务质量分析(门诊)")
    @PostMapping("/queryMZQosAnalysis")
    public CommonResult<?> queryMZQosAnalysis(@RequestBody BmsAnalysisDto dto){
        return CommonResult.success(bmsAnalysisReadService.queryMZQosAnalysis(dto));
    }

    @ApiOperation("收入分析")
    @PostMapping("/queryIncomeAnalysis")
    public CommonResult<?> queryIncomeAnalysis(@RequestBody BmsAnalysisDto dto){
        return CommonResult.success(bmsAnalysisReadService.queryIncomeAnalysis(dto));
    }

}
