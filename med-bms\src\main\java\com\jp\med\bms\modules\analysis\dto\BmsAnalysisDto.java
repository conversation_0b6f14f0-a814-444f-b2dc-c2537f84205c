package com.jp.med.bms.modules.analysis.dto;

import com.jp.med.common.dto.common.CommonQueryDto;
import com.jp.med.common.exception.AppException;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * @ClassName BmsAnalysisDto
 * @Description 预算分析
 * <AUTHOR>
 * @Date 2024/1/18 14:45
 * @Version 1.0
 */

public class BmsAnalysisDto extends CommonQueryDto {

    /** 时间范围 */
    private String[] dateRage;

    /** 上年度时间范围 */
    private String[] dateRageYoy;

    public void setDateRage(String[] dateRage) {
        this.dateRage = dateRage;
        if (Objects.isNull(dateRage)){
            throw new AppException("请选择要查询的时间");
        }
        String[] dynamicArray = new String[2];

        for (int i = 0; i < dateRage.length; i++) {
            String inputDateString = dateRage[i];
            // 创建日期格式化器
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("uuuuMMdd");

            // 解析输入的日期字符串
            LocalDate specificDate = LocalDate.parse(inputDateString, formatter);
            // 获取去年的年份，并保持月份和日期不变
            LocalDate dateOfLastYear = specificDate.minusYears(1);

            if (dateOfLastYear.getMonthValue() == 2 && dateOfLastYear.getDayOfMonth() == 28 &&
                    dateOfLastYear.isLeapYear()) {
                dateOfLastYear = dateOfLastYear.withDayOfMonth(29);
            }
            dynamicArray[i] = formatter.format(dateOfLastYear);
        }
        this.setDateRageYoy(dynamicArray);
    }

    public String[] getDateRage() {
        return dateRage;
    }

    public String[] getDateRageYoy() {
        return dateRageYoy;
    }

    public void setDateRageYoy(String[] dateRageYoy) {
        this.dateRageYoy = dateRageYoy;
    }
}
