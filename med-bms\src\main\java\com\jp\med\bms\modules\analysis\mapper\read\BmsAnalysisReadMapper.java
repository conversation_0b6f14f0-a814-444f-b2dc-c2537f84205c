package com.jp.med.bms.modules.analysis.mapper.read;

import com.jp.med.bms.modules.analysis.dto.BmsAnalysisDto;
import com.jp.med.bms.modules.analysis.vo.BmsAnalysisVo;

import java.util.List;

public interface BmsAnalysisReadMapper {

    /**
     * 查询服务质量分析
     * @param dto
     * @return
     */
    List<BmsAnalysisVo> queryQosAnalysis(BmsAnalysisDto dto);

    /**
     * 查询收入分析
     * @param dto
     * @return
     */
    List<BmsAnalysisVo> queryIncomeAnalysis(BmsAnalysisDto dto);

    /**
     * 查询服务质量分析（门诊）
     * @param dto
     * @return
     */
    List<BmsAnalysisVo> queryMZQosAnalysis(BmsAnalysisDto dto);
}
