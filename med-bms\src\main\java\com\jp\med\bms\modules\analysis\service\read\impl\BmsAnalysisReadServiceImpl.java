package com.jp.med.bms.modules.analysis.service.read.impl;

import com.jp.med.bms.modules.analysis.dto.BmsAnalysisDto;
import com.jp.med.bms.modules.analysis.mapper.read.BmsAnalysisReadMapper;
import com.jp.med.bms.modules.analysis.service.read.BmsAnalysisReadService;
import com.jp.med.bms.modules.analysis.vo.BmsAnalysisVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @ClassName BmsAnalysisReadServiceImpl
 * @Description 预算分析
 * <AUTHOR>
 * @Date 2024/1/18 14:51
 * @Version 1.0
 */
@Service
public class BmsAnalysisReadServiceImpl implements BmsAnalysisReadService {

    @Resource
    private BmsAnalysisReadMapper bmsAnalysisReadMapper;


    @Override
    public List<BmsAnalysisVo> queryQosAnalysis(BmsAnalysisDto dto) {
        dto.setDateRage(dto.getDateRage());
        return bmsAnalysisReadMapper.queryQosAnalysis(dto);
    }

    @Override
    public List<BmsAnalysisVo> queryMZQosAnalysis(BmsAnalysisDto dto) {
        dto.setDateRage(dto.getDateRage());
        return bmsAnalysisReadMapper.queryMZQosAnalysis(dto);
    }

    @Override
    public List<BmsAnalysisVo> queryIncomeAnalysis(BmsAnalysisDto dto) {
        dto.setDateRage(dto.getDateRage());
        return bmsAnalysisReadMapper.queryIncomeAnalysis(dto);
    }
}
