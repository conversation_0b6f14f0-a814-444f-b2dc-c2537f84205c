package com.jp.med.bms.modules.analysis.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName BmsAnalysisVo
 * @Description 预算分析
 * <AUTHOR>
 * @Date 2024/1/18 14:45
 * @Version 1.0
 */

@Getter
@Setter
public class BmsAnalysisVo{

    /** 科室编码 */
    private String orgId;
    /** 科室名称 */
    private String orgName;
    /** 总费用 */
    private String totalZje;
    /** 总人次 */
    private String totalZrc;
    /** 总人次同比 */
    private String totalZrcYoy;
    /** 平均住院日 */
    private String avgZyts;
    /** 日均费用 */
    private String avgRjfy;
    /** 预算平均住院日 */
    private String pjzyr;
    /** 预算出院人次 */
    private String cyrc;
    /** 预算日均费用 */
    private String mcrpjfy;
}
