package com.jp.med.bms.modules.bmsConfig.controller;

import com.jp.med.common.util.ULIDUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.bms.modules.bmsConfig.dto.BmsEngineCfgDto;
import com.jp.med.bms.modules.bmsConfig.service.read.BmsEngineCfgReadService;
import com.jp.med.bms.modules.bmsConfig.service.write.BmsEngineCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 信息化系统预算配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-15 10:39:28
 */
@Api(value = "信息化系统预算配置", tags = "信息化系统预算配置")
@RestController
@RequestMapping("bmsEngineCfg")
public class BmsEngineCfgController {

    @Autowired
    private BmsEngineCfgReadService bmsEngineCfgReadService;

    @Autowired
    private BmsEngineCfgWriteService bmsEngineCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询信息化系统预算配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsEngineCfgDto dto){
        return CommonResult.paging(bmsEngineCfgReadService.queryList(dto));
    }

    @ApiOperation("查询信息化系统预算配置")
    @PostMapping("/checkOnly")
    public CommonResult<?> checkOnly(@RequestBody BmsEngineCfgDto dto){
        bmsEngineCfgReadService.checkOnly(dto);
        return CommonResult.success();
    }

    /**
     * 保存
     */
    @ApiOperation("新增信息化系统预算配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsEngineCfgDto dto){
        bmsEngineCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改信息化系统预算配置")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsEngineCfgDto dto){
        bmsEngineCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除信息化系统预算配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsEngineCfgDto dto){
        bmsEngineCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

}
