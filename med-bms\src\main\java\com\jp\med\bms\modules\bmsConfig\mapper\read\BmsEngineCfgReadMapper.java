package com.jp.med.bms.modules.bmsConfig.mapper.read;

import com.jp.med.bms.modules.bmsConfig.dto.BmsEngineCfgDto;
import com.jp.med.bms.modules.bmsConfig.vo.BmsEngineCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 信息化系统预算配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-15 10:39:28
 */
@Mapper
public interface BmsEngineCfgReadMapper extends BaseMapper<BmsEngineCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsEngineCfgVo> queryList(BmsEngineCfgDto dto);
}
