package com.jp.med.bms.modules.bmsConfig.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsConfig.dto.BmsEngineCfgDto;
import com.jp.med.bms.modules.bmsConfig.vo.BmsEngineCfgVo;

import java.util.List;

/**
 * 信息化系统预算配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-15 10:39:28
 */
public interface BmsEngineCfgReadService extends IService<BmsEngineCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsEngineCfgVo> queryList(BmsEngineCfgDto dto);

    /**
     * 校验是否唯一
     * @param dto
     */
    void checkOnly(BmsEngineCfgDto dto);
}

