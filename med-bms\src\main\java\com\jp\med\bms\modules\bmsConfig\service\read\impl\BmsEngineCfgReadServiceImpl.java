package com.jp.med.bms.modules.bmsConfig.service.read.impl;

import com.github.pagehelper.PageHelper;
import com.jp.med.common.exception.AppException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bms.modules.bmsConfig.mapper.read.BmsEngineCfgReadMapper;
import com.jp.med.bms.modules.bmsConfig.dto.BmsEngineCfgDto;
import com.jp.med.bms.modules.bmsConfig.vo.BmsEngineCfgVo;
import com.jp.med.bms.modules.bmsConfig.service.read.BmsEngineCfgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class BmsEngineCfgReadServiceImpl extends ServiceImpl<BmsEngineCfgReadMapper, BmsEngineCfgDto> implements BmsEngineCfgReadService {

    @Autowired
    private BmsEngineCfgReadMapper bmsEngineCfgReadMapper;

    @Override
    public List<BmsEngineCfgVo> queryList(BmsEngineCfgDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return bmsEngineCfgReadMapper.queryList(dto);
    }

    @Override
    public void checkOnly(BmsEngineCfgDto dto) {
        List<BmsEngineCfgVo> engineCfgVos = bmsEngineCfgReadMapper.queryList(dto);
        if (!engineCfgVos.isEmpty()){
            throw new AppException("信息设备编码已存在");
        }
    }

}
