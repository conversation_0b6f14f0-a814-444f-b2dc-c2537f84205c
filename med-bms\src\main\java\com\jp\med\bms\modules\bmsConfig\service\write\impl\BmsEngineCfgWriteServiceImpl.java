package com.jp.med.bms.modules.bmsConfig.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.bms.modules.bmsConfig.mapper.write.BmsEngineCfgWriteMapper;
import com.jp.med.bms.modules.bmsConfig.dto.BmsEngineCfgDto;
import com.jp.med.bms.modules.bmsConfig.service.write.BmsEngineCfgWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 信息化系统预算配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-15 10:39:28
 */
@Service
@Transactional(readOnly = false)
public class BmsEngineCfgWriteServiceImpl extends ServiceImpl<BmsEngineCfgWriteMapper, BmsEngineCfgDto> implements BmsEngineCfgWriteService {
}
