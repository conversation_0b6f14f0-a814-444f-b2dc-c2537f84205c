package com.jp.med.bms.modules.bmsExecute.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetAssetDto;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsBudgetAssetWriteMapper;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsBudgetAssetReadService;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsBudgetAssetWriteService;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.entity.sys.SysDict;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.DictUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 医疗设备购置预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:19:45
 */
@Api(value = "医疗设备购置预算", tags = "医疗设备购置预算")
@RestController
@RequestMapping("bmsBudgetAsset")
public class BmsBudgetAssetController {

    @Autowired
    private BmsBudgetAssetReadService bmsBudgetAssetReadService;

    @Autowired
    private BmsBudgetAssetWriteService bmsBudgetAssetWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询医疗设备购置预算")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsBudgetAssetDto dto){
        return CommonResult.success(bmsBudgetAssetReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增医疗设备购置预算")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsBudgetAssetDto dto){
        if (!Objects.isNull(dto.getCurSysOrgId())){
            dto.setDept(dto.getSysUser().getSysOrgId());
        }
        if ("1".equals(dto.getType())){
            dto.setBudgetAmount(dto.getPrice().multiply(new BigDecimal(dto.getCnt())));
        }
        bmsBudgetAssetWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改医疗设备购置预算")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsBudgetAssetDto dto){
        if (!Objects.isNull(dto.getCurSysOrgId())){
            dto.setDept(dto.getSysUser().getSysOrgId());
        }
        if ("1".equals(dto.getType())){
            dto.setBudgetAmount(dto.getPrice().multiply(new BigDecimal(dto.getCnt())));
        }
        bmsBudgetAssetWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除医疗设备购置预算")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsBudgetAssetDto dto){
        bmsBudgetAssetWriteService.removeById(dto);
        return CommonResult.success();
    }

    @ApiOperation("文件上传")
    @PostMapping("/upload")
    public CommonResult<?> upload(@RequestParam("file") MultipartFile file, BmsBudgetAssetDto dto){
        //类型    ASSET_MNT_TYPE
        List<SysDict> assetMntTypes = DictUtil.getDictByType("ASSET_MNT_TYPE");
        Map<String, List<SysDict>> types = assetMntTypes.stream().collect(Collectors.groupingBy(SysDict::getLabel));

        //维保类型  MNT
        List<SysDict> mnts = DictUtil.getDictByType("MNT");
        Map<String, List<SysDict>> mntTypes = mnts.stream().collect(Collectors.groupingBy(SysDict::getLabel));


        //计量单位  MTR_TYPE
        List<SysDict> units = DictUtil.getDictByType("MTR_TYPE");
        Map<String, List<SysDict>> mtrTypes = units.stream().collect(Collectors.groupingBy(SysDict::getLabel));


        //设备类型  ASSET_TYPE
        List<SysDict> assetTypeDicts = DictUtil.getDictByType("ASSET_TYPE");
        Map<String, List<SysDict>> assetTypes = assetTypeDicts.stream().collect(Collectors.groupingBy(SysDict::getLabel));


        //是否耗材  YES_OR_NO
        //是否单一来源    YES_OR_NO
        List<SysDict> yesOrNoDicts = DictUtil.getDictByType("YES_OR_NO");
        Map<String, List<SysDict>> yesOrNos = yesOrNoDicts.stream().collect(Collectors.groupingBy(SysDict::getLabel));

        //耗材、试剂 MCS_TYPE
        List<SysDict> mcsTypeDicts = DictUtil.getDictByType("MCS_TYPE");
        Map<String, List<SysDict>> mcsTypes = mcsTypeDicts.stream().collect(Collectors.groupingBy(SysDict::getLabel));


        try {
            EasyExcel.read(file.getInputStream(), BmsBudgetAssetDto.class, new AnalysisEventListener<BmsBudgetAssetDto>() {
                private final List<BmsBudgetAssetDto> list = new ArrayList<>();
                @Override
                public void invoke(BmsBudgetAssetDto assetDto, AnalysisContext analysisContext) {
                    assetDto.setTaskCode(dto.getTaskCode());
                    if (StringUtils.isNotEmpty(assetDto.getDept()) && (StringUtils.isEmpty(dto.getCurSysOrgId()) ||
                            assetDto.getDept().equals(dto.getCurSysOrgId()))) {
                        if (StringUtils.isNotBlank(assetDto.getType())) assetDto.setType(types.get(assetDto.getType()).get(0).getValue());
                        if (StringUtils.isNotBlank(assetDto.getMntType())) assetDto.setMntType(mntTypes.get(assetDto.getMntType()).get(0).getValue());
                        if (StringUtils.isNotBlank(assetDto.getUnit())) assetDto.setUnit(mtrTypes.get(assetDto.getUnit()).get(0).getValue());
                        if (StringUtils.isNotBlank(assetDto.getAssetType())) assetDto.setAssetType(assetTypes.get(assetDto.getAssetType()).get(0).getValue());
                        if (StringUtils.isNotBlank(assetDto.getIsMcs())) assetDto.setIsMcs(yesOrNos.get(assetDto.getIsMcs()).get(0).getValue());
                        if (StringUtils.isNotBlank(assetDto.getSingleSrc())) assetDto.setSingleSrc(yesOrNos.get(assetDto.getSingleSrc()).get(0).getValue());
                        if (StringUtils.isNotBlank(assetDto.getMcsType())) assetDto.setMcsType(mcsTypes.get(assetDto.getMcsType()).get(0).getValue());
                        list.add(assetDto);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    if (CollectionUtil.isNotEmpty(list)) {
                        BatchUtil.batch(list, BmsBudgetAssetWriteMapper.class);
                    }
                }
            }).sheet().doRead();
        } catch (IOException e) {
            throw new AppException("上传文件失败");
        }
        return CommonResult.success();
    }

}
