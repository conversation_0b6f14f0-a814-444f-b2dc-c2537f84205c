package com.jp.med.bms.modules.bmsExecute.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsBudgetEngineWriteMapper;
import com.jp.med.common.entity.sys.SysDict;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.DictUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetEngineDto;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsBudgetEngineReadService;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsBudgetEngineWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 信息化建设项目预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:28:01
 */
@Api(value = "信息化建设项目预算", tags = "信息化建设项目预算")
@RestController
@RequestMapping("bmsBudgetEngine")
public class BmsBudgetEngineController {

    @Autowired
    private BmsBudgetEngineReadService bmsBudgetEngineReadService;

    @Autowired
    private BmsBudgetEngineWriteService bmsBudgetEngineWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询信息化建设项目预算")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsBudgetEngineDto dto){
        return CommonResult.success(bmsBudgetEngineReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增信息化建设项目预算")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsBudgetEngineDto dto){
        if (!Objects.isNull(dto.getCurSysOrgId())){
            dto.setDept(dto.getSysUser().getSysOrgId());
        }
        if ("1".equals(dto.getType())){
            dto.setBudgetAmount(dto.getPrice().multiply(new BigDecimal(dto.getCnt())));
            dto.setReqDscr(null);
            dto.setImplEfft(null);
        }
        if ("2".equals(dto.getType())){
            dto.setMemo(null);
            dto.setPrice(null);
            dto.setCnt(null);
        }
        bmsBudgetEngineWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改信息化建设项目预算")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsBudgetEngineDto dto){
        if (!Objects.isNull(dto.getCurSysOrgId())){
            dto.setDept(dto.getSysUser().getSysOrgId());
        }
        if ("1".equals(dto.getType())){
            dto.setBudgetAmount(dto.getPrice().multiply(new BigDecimal(dto.getCnt())));
            dto.setReqDscr(null);
            dto.setImplEfft(null);
        }
        if ("2".equals(dto.getType())){
            dto.setMemo(null);
            dto.setPrice(null);
            dto.setCnt(null);
        }
        bmsBudgetEngineWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除信息化建设项目预算")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsBudgetEngineDto dto){
        bmsBudgetEngineWriteService.removeById(dto);
        return CommonResult.success();
    }

    @ApiOperation("文件上传")
    @PostMapping("/upload")
    public CommonResult<?> upload(@RequestParam("file") MultipartFile file, BmsBudgetEngineDto dto){
        //信息化采购类型 ENGINE_TYPE
        List<SysDict> engineTypeDicts = DictUtil.getDictByType("ENGINE_TYPE");
        Map<String, List<SysDict>> enginTypes = engineTypeDicts.stream().collect(Collectors.groupingBy(SysDict::getLabel));
        //设备项目名称    EQUIP_ITEM
        List<SysDict> equipItemDicts = DictUtil.getDictByType("EQUIP_ITEM");
        Map<String, List<SysDict>> equipItems = equipItemDicts.stream().collect(Collectors.groupingBy(SysDict::getLabel));
        try {
            EasyExcel.read(file.getInputStream(), BmsBudgetEngineDto.class, new AnalysisEventListener<BmsBudgetEngineDto>() {
                private final List<BmsBudgetEngineDto> list = new ArrayList<>();
                @Override
                public void invoke(BmsBudgetEngineDto engineDto, AnalysisContext analysisContext) {
                    engineDto.setTaskCode(dto.getTaskCode());
                    if (StringUtils.isNotEmpty(engineDto.getDept()) && (StringUtils.isEmpty(dto.getCurSysOrgId()) ||
                            engineDto.getDept().equals(dto.getCurSysOrgId()))) {
                        if (StringUtils.isNotBlank(engineDto.getType())) engineDto.setType(enginTypes.get(engineDto.getType()).get(0).getValue());
                        if (StringUtils.isNotBlank(engineDto.getItemname())) engineDto.setItemname(equipItems.get(engineDto.getItemname()).get(0).getValue());
                        list.add(engineDto);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    if (CollectionUtil.isNotEmpty(list)) {
                        BatchUtil.batch(list, BmsBudgetEngineWriteMapper.class);
                    }
                }
            }).sheet().doRead();
        } catch (IOException e) {
            throw new AppException("上传文件失败");
        }
        return CommonResult.success();
    }

}
