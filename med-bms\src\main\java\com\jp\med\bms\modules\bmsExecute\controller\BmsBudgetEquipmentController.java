package com.jp.med.bms.modules.bmsExecute.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsBudgetEquipmentWriteMapper;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetEquipmentDto;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsBudgetEquipmentReadService;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsBudgetEquipmentWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * 通用设备预算
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 14:06:20
 */
@Api(value = "通用设备预算", tags = "通用设备预算")
@RestController
@RequestMapping("bmsBudgetEquipment")
public class BmsBudgetEquipmentController {

    @Autowired
    private BmsBudgetEquipmentReadService bmsBudgetEquipmentReadService;

    @Autowired
    private BmsBudgetEquipmentWriteService bmsBudgetEquipmentWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询通用设备预算")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsBudgetEquipmentDto dto){
        return CommonResult.success(bmsBudgetEquipmentReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增通用设备预算")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsBudgetEquipmentDto dto){
        if (!Objects.isNull(dto.getCurSysOrgId())){
            dto.setDept(dto.getSysUser().getSysOrgId());
        }
        dto.setBudgetAmount(dto.getPrice().multiply(new BigDecimal(dto.getCnt())));
        bmsBudgetEquipmentWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改通用设备预算")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsBudgetEquipmentDto dto){
        if (!Objects.isNull(dto.getCurSysOrgId())){
            dto.setDept(dto.getSysUser().getSysOrgId());
        }
        dto.setBudgetAmount(dto.getPrice().multiply(new BigDecimal(dto.getCnt())));
        bmsBudgetEquipmentWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除通用设备预算")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsBudgetEquipmentDto dto){
        bmsBudgetEquipmentWriteService.removeById(dto);
        return CommonResult.success();
    }

    @ApiOperation("文件上传")
    @PostMapping("/upload")
    public CommonResult<?> upload(@RequestParam("file") MultipartFile file, BmsBudgetEquipmentDto dto){
        try {
            EasyExcel.read(file.getInputStream(), BmsBudgetEquipmentDto.class, new AnalysisEventListener<BmsBudgetEquipmentDto>() {
                private final List<BmsBudgetEquipmentDto> list = new ArrayList<>();
                @Override
                public void invoke(BmsBudgetEquipmentDto budgetEquipmentDto, AnalysisContext analysisContext) {
                    budgetEquipmentDto.setTaskCode(dto.getTaskCode());
                    if (StringUtils.isNotEmpty(budgetEquipmentDto.getDept()) && (StringUtils.isEmpty(dto.getCurSysOrgId()) ||
                            budgetEquipmentDto.getDept().equals(dto.getCurSysOrgId()))) {
                        list.add(budgetEquipmentDto);
                    }
                }
                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    if (CollectionUtil.isNotEmpty(list)) {
                        BatchUtil.batch(list, BmsBudgetEquipmentWriteMapper.class);
                    }
                }
            }).sheet().doRead();
        } catch (IOException e) {
            throw new AppException("上传文件失败");
        }
        return CommonResult.success();
    }
}
