package com.jp.med.bms.modules.bmsExecute.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsBudgetGoodsWriteMapper;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetGoodsDto;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsBudgetGoodsReadService;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsBudgetGoodsWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * 库房物资采购预算
 * <AUTHOR>
 * @email -
 * @date 2023-11-16 11:22:49
 */
@Api(value = "库房物资采购预算", tags = "库房物资采购预算")
@RestController
@RequestMapping("bmsBudgetGoods")
public class BmsBudgetGoodsController {

    @Autowired
    private BmsBudgetGoodsReadService bmsBudgetGoodsReadService;

    @Autowired
    private BmsBudgetGoodsWriteService bmsBudgetGoodsWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询库房物资采购预算")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsBudgetGoodsDto dto){
        return CommonResult.success(bmsBudgetGoodsReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增库房物资采购预算")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsBudgetGoodsDto dto){
        if (!Objects.isNull(dto.getCurSysOrgId())){
            dto.setDept(dto.getSysUser().getSysOrgId());
        }
        dto.setBudgetAmount(dto.getPrice().multiply(new BigDecimal(dto.getCnt())));
        bmsBudgetGoodsWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改库房物资采购预算")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsBudgetGoodsDto dto){
        if (!Objects.isNull(dto.getCurSysOrgId())){
            dto.setDept(dto.getSysUser().getSysOrgId());
        }
        dto.setBudgetAmount(dto.getPrice().multiply(new BigDecimal(dto.getCnt())));
        bmsBudgetGoodsWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除库房物资采购预算")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsBudgetGoodsDto dto){
        bmsBudgetGoodsWriteService.removeById(dto);
        return CommonResult.success();
    }

    @ApiOperation("文件上传")
    @PostMapping("/upload")
    public CommonResult<?> upload(@RequestParam("file") MultipartFile file, BmsBudgetGoodsDto dto){
        try {
            EasyExcel.read(file.getInputStream(), BmsBudgetGoodsDto.class, new AnalysisEventListener<BmsBudgetGoodsDto>() {
                private final List<BmsBudgetGoodsDto> list = new ArrayList<>();
                @Override
                public void invoke(BmsBudgetGoodsDto keyDto, AnalysisContext analysisContext) {
                    keyDto.setTaskCode(dto.getTaskCode());
                    if (StringUtils.isNotEmpty(keyDto.getDept()) && (StringUtils.isEmpty(dto.getCurSysOrgId()) ||
                            keyDto.getDept().equals(dto.getCurSysOrgId()))) {
                        list.add(keyDto);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    if (CollectionUtil.isNotEmpty(list)) {
                        BatchUtil.batch(list, BmsBudgetGoodsWriteMapper.class);
                    }
                }
            }).sheet().doRead();
        } catch (IOException e) {
            throw new AppException("上传文件失败");
        }
        return CommonResult.success();
    }

}
