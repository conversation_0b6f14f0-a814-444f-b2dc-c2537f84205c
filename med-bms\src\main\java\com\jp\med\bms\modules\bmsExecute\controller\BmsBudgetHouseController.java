package com.jp.med.bms.modules.bmsExecute.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetHouseDto;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsBudgetHouseWriteMapper;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsBudgetHouseReadService;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsBudgetHouseWriteService;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.entity.sys.SysDict;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.DictUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 工程类政府采购预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:34:43
 */
@Api(value = "工程类政府采购预算", tags = "工程类政府采购预算")
@RestController
@RequestMapping("bmsBudgetHouse")
public class BmsBudgetHouseController {

    @Autowired
    private BmsBudgetHouseReadService bmsBudgetHouseReadService;

    @Autowired
    private BmsBudgetHouseWriteService bmsBudgetHouseWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询工程类政府采购预算")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsBudgetHouseDto dto){
        return CommonResult.success(bmsBudgetHouseReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增工程类政府采购预算")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsBudgetHouseDto dto){
        if (!Objects.isNull(dto.getCurSysOrgId())){
            dto.setDept(dto.getSysUser().getSysOrgId());
        }
        dto.setBudgetAmount(dto.getPrice().multiply(new BigDecimal(dto.getCnt())));
        bmsBudgetHouseWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改工程类政府采购预算")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsBudgetHouseDto dto){
        if (!Objects.isNull(dto.getCurSysOrgId())){
            dto.setDept(dto.getSysUser().getSysOrgId());
        }
        dto.setBudgetAmount(dto.getPrice().multiply(new BigDecimal(dto.getCnt())));
        bmsBudgetHouseWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除工程类政府采购预算")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsBudgetHouseDto dto){
        bmsBudgetHouseWriteService.removeById(dto);
        return CommonResult.success();
    }

    @ApiOperation("文件上传")
    @PostMapping("/upload")
    public CommonResult<?> upload(@RequestParam("file") MultipartFile file, BmsBudgetHouseDto dto){
        //计量单位  MTR_TYPE
        List<SysDict> units = DictUtil.getDictByType("MTR_TYPE");
        Map<String, List<SysDict>> mtrTypes = units.stream().collect(Collectors.groupingBy(SysDict::getLabel));
        try {
            EasyExcel.read(file.getInputStream(), BmsBudgetHouseDto.class, new AnalysisEventListener<BmsBudgetHouseDto>() {
                private final List<BmsBudgetHouseDto> list = new ArrayList<>();
                @Override
                public void invoke(BmsBudgetHouseDto houseDto, AnalysisContext analysisContext) {
                    houseDto.setTaskCode(dto.getTaskCode());
                    if (StringUtils.isNotEmpty(houseDto.getDept()) && (StringUtils.isEmpty(dto.getCurSysOrgId()) ||
                            houseDto.getDept().equals(dto.getCurSysOrgId()))) {
                        if (StringUtils.isNotBlank(houseDto.getUnit())) houseDto.setUnit(mtrTypes.get(houseDto.getUnit()).get(0).getValue());
                        list.add(houseDto);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    if (CollectionUtil.isNotEmpty(list)) {
                        BatchUtil.batch(list, BmsBudgetHouseWriteMapper.class);
                    }
                }
            }).sheet().doRead();
        } catch (IOException e) {
            throw new AppException("上传文件失败");
        }
        return CommonResult.success();
    }
}
