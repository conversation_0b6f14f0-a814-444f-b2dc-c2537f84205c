package com.jp.med.bms.modules.bmsExecute.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetKeyDto;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsBudgetKeyWriteMapper;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsBudgetKeyReadService;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsBudgetKeyWriteService;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.entity.sys.SysDict;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.DictUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 重点学专科预算表
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:15:52
 */
@Api(value = "重点学专科预算表", tags = "重点学专科预算表")
@RestController
@RequestMapping("bmsBudgetKey")
public class BmsBudgetKeyController {

    @Autowired
    private BmsBudgetKeyReadService bmsBudgetKeyReadService;

    @Autowired
    private BmsBudgetKeyWriteService bmsBudgetKeyWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询重点学专科预算表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsBudgetKeyDto dto){
        return CommonResult.success(bmsBudgetKeyReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增重点学专科预算表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsBudgetKeyDto dto){
        if (!Objects.isNull(dto.getCurSysOrgId())){
            dto.setDept(dto.getSysUser().getSysOrgId());
        }
        bmsBudgetKeyWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改重点学专科预算表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsBudgetKeyDto dto){
        if (!Objects.isNull(dto.getCurSysOrgId())){
            dto.setDept(dto.getSysUser().getSysOrgId());
        }
        bmsBudgetKeyWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除重点学专科预算表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsBudgetKeyDto dto){
        bmsBudgetKeyWriteService.removeById(dto);
        return CommonResult.success();
    }

    @ApiOperation("文件上传")
    @PostMapping("/upload")
    public CommonResult<?> upload(@RequestParam("file") MultipartFile file, BmsBudgetKeyDto dto){
        //重点学专科字典
        List<SysDict> imps = DictUtil.getDictByType("IMP");
        Map<String, List<SysDict>> keyCodes = imps.stream().collect(Collectors.groupingBy(SysDict::getLabel));
        try {
            EasyExcel.read(file.getInputStream(), BmsBudgetKeyDto.class, new AnalysisEventListener<BmsBudgetKeyDto>() {
                private final List<BmsBudgetKeyDto> list = new ArrayList<>();
                @Override
                public void invoke(BmsBudgetKeyDto keyDto, AnalysisContext analysisContext) {
                    keyDto.setTaskCode(dto.getTaskCode());
                    if (StringUtils.isNotEmpty(keyDto.getDept()) && (StringUtils.isEmpty(dto.getCurSysOrgId()) ||
                            keyDto.getDept().equals(dto.getCurSysOrgId()))) {
                        //转换重点学科编码
                        if (StringUtils.isNotBlank(keyDto.getKeyCode())) keyDto.setKeyCode(keyCodes.get(keyDto.getKeyCode()).get(0).getValue());
                        list.add(keyDto);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    if (CollectionUtil.isNotEmpty(list)) {
                        BatchUtil.batch(list, BmsBudgetKeyWriteMapper.class);
                    }
                }
            }).sheet().doRead();
        } catch (IOException e) {
            throw new AppException("上传文件失败");
        }
        return CommonResult.success();
    }
}
