package com.jp.med.bms.modules.bmsExecute.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsBudgetServiceWriteMapper;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetServiceDto;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsBudgetServiceReadService;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsBudgetServiceWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * 服务类采购预算
 * <AUTHOR>
 * @email -
 * @date 2023-11-16 11:03:01
 */
@Api(value = "服务类采购预算", tags = "服务类采购预算")
@RestController
@RequestMapping("bmsBudgetService")
public class BmsBudgetServiceController {

    @Autowired
    private BmsBudgetServiceReadService bmsBudgetServiceReadService;

    @Autowired
    private BmsBudgetServiceWriteService bmsBudgetServiceWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询服务类采购预算")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsBudgetServiceDto dto){
        return CommonResult.success(bmsBudgetServiceReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增服务类采购预算")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsBudgetServiceDto dto){
        if (!Objects.isNull(dto.getCurSysOrgId())){
            dto.setDept(dto.getSysUser().getSysOrgId());
        }
        dto.setBudgetAmount(dto.getPrice().multiply(new BigDecimal(dto.getCnt())));
        bmsBudgetServiceWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改服务类采购预算")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsBudgetServiceDto dto){
        if (!Objects.isNull(dto.getCurSysOrgId())){
            dto.setDept(dto.getSysUser().getSysOrgId());
        }
        dto.setBudgetAmount(dto.getPrice().multiply(new BigDecimal(dto.getCnt())));
        bmsBudgetServiceWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除服务类采购预算")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsBudgetServiceDto dto){
        bmsBudgetServiceWriteService.removeById(dto);
        return CommonResult.success();
    }

    @ApiOperation("文件上传")
    @PostMapping("/upload")
    public CommonResult<?> upload(@RequestParam("file") MultipartFile file, BmsBudgetServiceDto dto){
        try {
            EasyExcel.read(file.getInputStream(), BmsBudgetServiceDto.class, new AnalysisEventListener<BmsBudgetServiceDto>() {
                private final List<BmsBudgetServiceDto> list = new ArrayList<>();
                @Override
                public void invoke(BmsBudgetServiceDto keyDto, AnalysisContext analysisContext) {
                    keyDto.setTaskCode(dto.getTaskCode());
                    if (StringUtils.isNotEmpty(keyDto.getDept()) && (StringUtils.isEmpty(dto.getCurSysOrgId()) ||
                            keyDto.getDept().equals(dto.getCurSysOrgId()))) {
                        list.add(keyDto);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    if (CollectionUtil.isNotEmpty(list)) {
                        BatchUtil.batch(list, BmsBudgetServiceWriteMapper.class);
                    }
                }
            }).sheet().doRead();
        } catch (IOException e) {
            throw new AppException("上传文件失败");
        }
        return CommonResult.success();
    }

}
