package com.jp.med.bms.modules.bmsExecute.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetWorkloadDto;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsBudgetWorkloadReadService;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsBudgetWorkloadWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 预算项目工作量情况
 * <AUTHOR>
 * @email -
 * @date 2024-07-27 15:16:25
 */
@Api(value = "预算项目工作量情况", tags = "预算项目工作量情况")
@RestController
@RequestMapping("bmsBudgetWorkload")
public class BmsBudgetWorkloadController {

    @Autowired
    private BmsBudgetWorkloadReadService bmsBudgetWorkloadReadService;

    @Autowired
    private BmsBudgetWorkloadWriteService bmsBudgetWorkloadWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询预算项目工作量情况")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody BmsBudgetWorkloadDto dto){
        return CommonResult.paging(bmsBudgetWorkloadReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询预算项目工作量情况")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsBudgetWorkloadDto dto){
        return CommonResult.success(bmsBudgetWorkloadReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增预算项目工作量情况")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsBudgetWorkloadDto dto){
        bmsBudgetWorkloadWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改预算项目工作量情况")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsBudgetWorkloadDto dto){
        bmsBudgetWorkloadWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除预算项目工作量情况")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsBudgetWorkloadDto dto){
        bmsBudgetWorkloadWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 查询业务工作量执行情况
     */
    @ApiOperation("查询业务工作量执行情况")
    @PostMapping("/querySRReport")
    public CommonResult<?> querySRReport(@RequestBody BmsBudgetWorkloadDto dto){
        return CommonResult.success(bmsBudgetWorkloadReadService.querySRReport(dto));
    }

    /**
     * 查询支出工作量预算执行情况
     * @param dto
     * @return
     */
    @ApiOperation("查询支出工作量预算执行情况")
    @PostMapping("/queryZCLReport")
    public CommonResult<?> queryZCLReport(@RequestBody BmsBudgetWorkloadDto dto) {
        return CommonResult.success(bmsBudgetWorkloadReadService.queryZCLReport(dto));
    }

    /**
     * 查询支出工作量预算执行情况明细
     * @param dto
     * @return
     */
    @ApiOperation("查询支出工作量预算执行情况明细")
    @PostMapping("/queryZCLReportDetail")
    public CommonResult<?> queryZCLReportDetail(@RequestBody BmsBudgetWorkloadDto dto) {
        return CommonResult.success(bmsBudgetWorkloadReadService.queryZCLReportDetail(dto));
    }

    /**
     * 查询支出工作量 某经济科目 预算执行情况
     * @param dto
     * @return
     */
    @ApiOperation("查询支出工作量预算执行情况")
    @PostMapping("/queryEconCodeDetail")
    public CommonResult<?> queryEconCodeDetail(@RequestBody BmsBudgetWorkloadDto dto) {
        return CommonResult.success(bmsBudgetWorkloadReadService.queryEconCodeDetail(dto));
    }

    /**
     * 查询支出工作量 某会计科目 预算执行情况
     * @param dto
     * @return
     */
    @ApiOperation("查询支出工作量预算执行情况")
    @PostMapping("/queryActigCodeDetail")
    public CommonResult<?> queryActigCodeDetail(@RequestBody BmsBudgetWorkloadDto dto) {
        return CommonResult.success(bmsBudgetWorkloadReadService.queryActigCodeDetail(dto));
    }

    /**
     * 上传业务工作量数据
     * @param dto
     * @return
     */
    @ApiOperation("上传业务工作量数据")
    @PostMapping("/uploadSRReport")
    public CommonResult<?> uploadSRReport(BmsBudgetWorkloadDto dto) {
        bmsBudgetWorkloadWriteService.uploadSRReport(dto);
        return CommonResult.success();
    }
}
