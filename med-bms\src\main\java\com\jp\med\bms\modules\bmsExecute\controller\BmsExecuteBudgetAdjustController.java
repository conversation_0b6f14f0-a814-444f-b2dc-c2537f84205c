package com.jp.med.bms.modules.bmsExecute.controller;

import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetAdjustDto;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetApplyDto;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsExecuteBudgetAdjustReadService;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsExecuteBudgetAdjustWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 预算调整表
 * <AUTHOR>
 * @email -
 * @date 2023-06-01 18:20:31
 */
@Api(value = "预算调整表", tags = "预算调整表")
@RestController
@RequestMapping("bmsExecute/budgetAdjust")
public class BmsExecuteBudgetAdjustController {

    @Autowired
    private BmsExecuteBudgetAdjustReadService bmsExecuteBudgetAdjustReadService;

    @Autowired
    private BmsExecuteBudgetAdjustWriteService bmsExecuteBudgetAdjustWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询预算调整表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsExecuteBudgetAdjustDto dto){
        return CommonResult.success(bmsExecuteBudgetAdjustReadService.queryList(dto));
    }
    /**
     * 列表
     */
    @ApiOperation("查询预算调整表")
    @PostMapping("/listNew")
    public CommonResult<?> listNew(@RequestBody BmsExecuteBudgetApplyDto dto){
        return CommonResult.success(bmsExecuteBudgetAdjustReadService.queryListNew(dto));
    }

    @ApiOperation("查询要调整的预算")
    @PostMapping("/queryBudget")
    public CommonResult<?> queryBudget(@RequestBody BmsExecuteBudgetAdjustDto dto){
        return CommonResult.success(bmsExecuteBudgetAdjustReadService.queryBudget(dto));
    }
    @ApiOperation("查询要调整的预算")
    @PostMapping("/queryBudgetNew")
    public CommonResult<?> queryBudgetNew(@RequestBody BmsExecuteBudgetAdjustDto dto){
        return CommonResult.success(bmsExecuteBudgetAdjustReadService.queryBudgetNew(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增预算调整表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsExecuteBudgetAdjustDto dto){
        bmsExecuteBudgetAdjustWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改预算调整表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsExecuteBudgetAdjustDto dto){
        bmsExecuteBudgetAdjustWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除预算调整表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsExecuteBudgetAdjustDto dto){
        bmsExecuteBudgetAdjustWriteService.removeById(dto);
        return CommonResult.success();
    }

}
