package com.jp.med.bms.modules.bmsExecute.controller;

import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetApplyDto;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsExecuteBudgetApplyReadService;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsExecuteBudgetApplyWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 预算调整申请表
 * <AUTHOR>
 * @email -
 * @date 2023-06-01 14:47:42
 */
@Api(value = "预算调整申请表", tags = "预算调整申请表")
@RestController
@RequestMapping("bmsExecute/budgetApply")
public class BmsExecuteBudgetApplyController {

    @Autowired
    private BmsExecuteBudgetApplyReadService bmsExecuteBudgetApplyReadService;

    @Autowired
    private BmsExecuteBudgetApplyWriteService bmsExecuteBudgetApplyWriteService;

    @ApiOperation("查询预算调整申请表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsExecuteBudgetApplyDto dto){
        return CommonResult.paging(bmsExecuteBudgetApplyReadService.queryList(dto));
    }

    @ApiOperation("查询预算审核")
    @PostMapping("/queryAdjustAudit")
    public CommonResult<?> queryAdjustAudit(@RequestBody BmsExecuteBudgetApplyDto dto){
        return CommonResult.success( bmsExecuteBudgetApplyReadService.queryAdjustAudit(dto));
    }

    @ApiOperation("预算调整审批")
    @PostMapping("/adjustAudit")
    public CommonResult<?> adjustAudit(@RequestBody BmsExecuteBudgetApplyDto dto){
        bmsExecuteBudgetApplyWriteService.adjustAudit(dto);
        return CommonResult.success();
    }

    @ApiOperation("预算调整申请页面初始化")
    @PostMapping("/queryModalInit")
    public CommonResult<?> queryModalInit(@RequestBody BmsExecuteBudgetApplyDto dto){
        return CommonResult.success(bmsExecuteBudgetApplyReadService.queryModalInit(dto));
    }


    @ApiOperation("新增预算调整申请表")
    @PostMapping("/save")
    public CommonResult<?> save(BmsExecuteBudgetApplyDto dto){
        bmsExecuteBudgetApplyWriteService.saveBudgetApply(dto);
        return CommonResult.success();
    }

    @ApiOperation("新增预算调整申请表")
    @PostMapping("/saveNew")
    public CommonResult<?> saveNew(BmsExecuteBudgetApplyDto dto){
        bmsExecuteBudgetApplyWriteService.saveBudgetApplyNew(dto);
        return CommonResult.success();
    }

    @ApiOperation("更新预算调整附件")
    @PostMapping("/uploadAdjustAtt")
    public CommonResult<?> uploadAdjustAtt(BmsExecuteBudgetApplyDto dto){
        bmsExecuteBudgetApplyWriteService.uploadAdjustAtt(dto);
        return CommonResult.success();
    }

    @ApiOperation("修改预算调整申请表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsExecuteBudgetApplyDto dto){
        bmsExecuteBudgetApplyWriteService.updateById(dto);
        return CommonResult.success();
    }

    @ApiOperation("删除预算调整申请表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsExecuteBudgetApplyDto dto){
        bmsExecuteBudgetApplyWriteService.removeById(dto);
        return CommonResult.success();
    }
    @ApiOperation("删除预算调整申请表")
    @PostMapping("/deleteNew")
    public CommonResult<?> deleteNew(@RequestBody BmsExecuteBudgetApplyDto dto){
        bmsExecuteBudgetApplyWriteService.removeByIdNew(dto);
        return CommonResult.success();
    }

}
