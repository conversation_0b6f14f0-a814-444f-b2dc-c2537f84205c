package com.jp.med.bms.modules.bmsExecute.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetFillingDto;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsExecuteBudgetFillingReadService;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsExecuteBudgetFillingWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 预算填报状态表
 * <AUTHOR>
 * @email -
 * @date 2023-04-28 10:35:42
 */
@Api(value = "预算填报状态表", tags = "预算填报状态表")
@RestController
@RequestMapping("bmsExecute/budgetFilling")
public class BmsExecuteBudgetFillingController {

    @Autowired
    private BmsExecuteBudgetFillingReadService bmsExecuteBudgetFillingReadService;

    @Autowired
    private BmsExecuteBudgetFillingWriteService bmsExecuteBudgetFillingWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询预算填报状态表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsExecuteBudgetFillingDto dto){
        return CommonResult.paging(bmsExecuteBudgetFillingReadService.queryList(dto));
    }

    @ApiOperation("页面初始化查询")
    @PostMapping("/initPage")
    public CommonResult<?> initPage(@RequestBody BmsExecuteBudgetFillingDto dto){
        return CommonResult.success(bmsExecuteBudgetFillingReadService.initPage(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增预算填报状态表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsExecuteBudgetFillingDto dto){
        bmsExecuteBudgetFillingWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改预算填报状态表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsExecuteBudgetFillingDto dto){
        bmsExecuteBudgetFillingWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除预算填报状态表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsExecuteBudgetFillingDto dto){
        bmsExecuteBudgetFillingWriteService.removeById(dto);
        return CommonResult.success();
    }

}
