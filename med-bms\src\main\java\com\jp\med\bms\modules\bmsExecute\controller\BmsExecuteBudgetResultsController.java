package com.jp.med.bms.modules.bmsExecute.controller;

import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetDataDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetResultsDto;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsExecuteBudgetResultsReadService;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsExecuteBudgetResultsWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 预算结果
 * <AUTHOR>
 * @email -
 * @date 2023-06-01 10:11:35
 */
@Api(value = "预算结果", tags = "预算结果")
@RestController
@RequestMapping("bmsExecute/budgetResults")
public class BmsExecuteBudgetResultsController {

    @Autowired
    private BmsExecuteBudgetResultsReadService bmsExecuteBudgetResultsReadService;

    @Autowired
    private BmsExecuteBudgetResultsWriteService bmsExecuteBudgetResultsWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询预算结果")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsExecuteBudgetResultsDto dto){
        return CommonResult.success(bmsExecuteBudgetResultsReadService.queryList(dto));
    }


    /**
     * 列表
     */
    @ApiOperation("查询预算结果(new)")
    @PostMapping("/listNew")
    public CommonResult<?> listNew(@RequestBody BmsExecuteBudgetResultsDto dto){
        return CommonResult.success(bmsExecuteBudgetResultsReadService.queryListNew(dto));
    }

    @ApiOperation("查询科室预算")
    @PostMapping("/queryDeptBudget")
    public CommonResult<?> queryDeptBudget(@RequestBody BmsExecuteBudgetResultsDto dto){
        return CommonResult.success(bmsExecuteBudgetResultsReadService.queryDeptBudget(dto));
    }

    /**
     * 查询预算报表推进进度
     */
    @ApiOperation("查询预算报表推进进度")
    @PostMapping("/queryBudgetReport")
    public CommonResult<?> queryBudgetReport(@RequestBody BmsExecuteBudgetDataDto dto){
        return CommonResult.success(bmsExecuteBudgetResultsReadService.queryBudgetReport(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增预算结果")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsExecuteBudgetResultsDto dto){
        bmsExecuteBudgetResultsWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改预算结果")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsExecuteBudgetResultsDto dto){
        bmsExecuteBudgetResultsWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除预算结果")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsExecuteBudgetResultsDto dto){
        bmsExecuteBudgetResultsWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 查询进修经费(JXJF)剩余预算
     */
    @ApiOperation("查询进修经费(JXJF)剩余预算")
    @PostMapping("/queryJXJFRemainingBudget")
    public CommonResult<?> queryJXJFRemainingBudget(@RequestBody BmsExecuteBudgetResultsDto dto) {
        return CommonResult.success(bmsExecuteBudgetResultsReadService.queryJXJFRemainingBudget(dto));
    }

}
