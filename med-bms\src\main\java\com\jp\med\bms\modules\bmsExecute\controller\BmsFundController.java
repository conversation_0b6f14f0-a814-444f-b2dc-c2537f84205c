package com.jp.med.bms.modules.bmsExecute.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetKeyDto;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsFundWriteMapper;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.bms.modules.bmsExecute.dto.BmsFundDto;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsFundReadService;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsFundWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * 科研教学经费预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 19:57:30
 */
@Api(value = "科研教学经费预算", tags = "科研教学经费预算")
@RestController
@RequestMapping("bmsFund")
public class BmsFundController {

    @Autowired
    private BmsFundReadService bmsFundReadService;

    @Autowired
    private BmsFundWriteService bmsFundWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询科研教学经费预算")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsFundDto dto){
        return CommonResult.success(bmsFundReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增科研教学经费预算")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsFundDto dto){
        if (!Objects.isNull(dto.getCurSysOrgId())){
            dto.setDept(dto.getSysUser().getSysOrgId());
        }
        dto.setBudgetAmount(dto.getStudyFund().add(dto.getTeachFund()));
        bmsFundWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改科研教学经费预算")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsFundDto dto){
        if (!Objects.isNull(dto.getCurSysOrgId())){
            dto.setDept(dto.getSysUser().getSysOrgId());
        }
        dto.setBudgetAmount(dto.getStudyFund().add(dto.getTeachFund()));
        bmsFundWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除科研教学经费预算")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsFundDto dto){
        bmsFundWriteService.removeById(dto);
        return CommonResult.success();
    }

    @ApiOperation("文件上传")
    @PostMapping("/upload")
    public CommonResult<?> upload(@RequestParam("file") MultipartFile file, BmsFundDto dto){
        try {
            EasyExcel.read(file.getInputStream(), BmsFundDto.class, new AnalysisEventListener<BmsFundDto>() {
                private final List<BmsFundDto> list = new ArrayList<>();
                @Override
                public void invoke(BmsFundDto fundDto, AnalysisContext analysisContext) {
                    fundDto.setTaskCode(dto.getTaskCode());
                    if (StringUtils.isNotEmpty(fundDto.getDept()) && (StringUtils.isEmpty(dto.getCurSysOrgId()) ||
                            fundDto.getDept().equals(dto.getCurSysOrgId()))) {
                        list.add(fundDto);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    if (CollectionUtil.isNotEmpty(list)) {
                        BatchUtil.batch(list, BmsFundWriteMapper.class);
                    }
                }
            }).sheet().doRead();
        } catch (IOException e) {
            throw new AppException("上传文件失败");
        }
        return CommonResult.success();
    }
}
