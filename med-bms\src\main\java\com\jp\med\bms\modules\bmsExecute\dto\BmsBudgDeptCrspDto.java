package com.jp.med.bms.modules.bmsExecute.dto;

import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

/**
 * class BmsBudgDeptCrspDto
 * desc   预算科室对照表 - bms_budg_dept_crsp
 *
 * <AUTHOR>
 */
@Data
public class BmsBudgDeptCrspDto extends CommonQueryDto {
    private static final long serialVersionUID = 1L;

    /**
     * 预算科室对照ID
     * SERIAL
     * isNullable false
     */
    private Integer budgdeptCrspId;
    /**
     * 预算报表科室名称
     * VARCHAR(100)
     * isNullable false
     */
    private String budgRptDeptName;
    /**
     * 科室类型
     * VARCHAR(6)
     * isNullable true
     */
    private String deptType;
    /**
     * HRP科室名称
     * VARCHAR(100)
     * isNullable false
     */
    private String deptName;
    /**
     * HRP科室编码
     * VARCHAR(50)
     * isNullable false
     */
    private String deptCodg;
    /**
     * 预算年度
     * VARCHAR(6)
     * isNullable true
     */
    private String budgYear;
    /**
     * 有效标志
     * VARCHAR(6)
     * isNullable false
     */
    private String valiFlag;
}