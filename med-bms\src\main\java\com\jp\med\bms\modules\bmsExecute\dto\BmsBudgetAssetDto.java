package com.jp.med.bms.modules.bmsExecute.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 医疗设备购置预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:19:45
 */
@Data
@TableName("bms_budget_asset" )
public class BmsBudgetAssetDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    @ExcelIgnore
    private Integer id;

    /** 设备名称 */
    @TableField(value = "asset_name",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("设备名称")
    private String assetName;

    /** 计量单位 */
    @TableField(value = "unit",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("计量单位")
    private String unit;

    /** 单价 */
    @TableField(value = "price",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("单价")
    private BigDecimal price;

    /** 数量 */
    @TableField(value = "cnt",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("数量")
    private Integer cnt;

    /** 预算数 */
    @TableField(value = "budget_amount",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("预算金额")
    private BigDecimal budgetAmount;

    /** 科室 */
    @TableField(value = "dept",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("申请科室")
    private String dept;

    /** 预算任务 */
    @TableField(value = "task_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelIgnore
    private String taskCode;

    /** 审核状态(0:未审核 1:已审核) */
    @TableField(value = "chk",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String chk;

    /** 耗材金额 */
    @TableField(value = "mcs_amount")
    @ExcelProperty("耗材金额")
    private BigDecimal mcsAmount;

    /** 耗材/试剂 */
    @ExcelProperty("耗材/试剂")
    @TableField(value = "mcs_type")
    private String mcsType;

    /** 是否耗材（1：是 0：否） */
    @ExcelProperty("是否耗材")
    @TableField(value = "is_mcs")
    private String isMcs;

    /** 设备类型（1：国产 2：进口） */
    @ExcelProperty("设备类型")
    @TableField(value = "asset_type")
    private String assetType;

    /** 备注 */
    @TableField(value = "memo",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("备注")
    private String memo;


    /** 设备维保类型 */
    @TableField(value = "mnt_type",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("设备维保类型")
    private String mntType;

    /** 类型 */
    @TableField(value = "type",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("类型")
    private String type;

    /** 是否单一来源 **/
    @ExcelProperty("是否单一来源")
    @TableField(value="single_src",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String singleSrc;


}
