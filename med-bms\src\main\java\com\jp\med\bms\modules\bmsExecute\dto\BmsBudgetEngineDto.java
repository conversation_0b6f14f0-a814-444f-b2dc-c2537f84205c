package com.jp.med.bms.modules.bmsExecute.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 信息化建设项目预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:28:01
 */
@Data
@TableName("bms_budget_engine" )
public class BmsBudgetEngineDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    @ExcelIgnore
    private Integer id;

    /** 项目名称 */
    @TableField(value = "itemname",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("项目名称")
    private String itemname;

    /** 采购方式 */
    @TableField(value = "purc_way",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("采购方式")
    private String purcWay;

    /** 预算数 */
    @TableField(value = "budget_amount",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("采购金额")
    private BigDecimal budgetAmount;

    /** 科室 */
    @TableField(value = "dept",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("申请科室")
    private String dept;

    /** 备注 */
    @TableField(value = "memo",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("备注")
    private String memo;

    /** 预算任务 */
    @TableField(value = "task_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelIgnore
    private String taskCode;

    /** 审核状态(0:未审核 1:已审核) */
    @TableField(value = "chk",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelIgnore
    private String chk;

    /** 需求说明 */
    @TableField(value = "req_dscr",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelIgnore
    private String reqDscr;

    /** 实现效果 */
    @TableField(value = "impl_efft",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelIgnore
    private String implEfft;

    /** 单价 */
    @TableField(value = "price",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelIgnore
    private BigDecimal price;

    /** 数量 */
    @TableField(value = "cnt",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelIgnore
    private Integer cnt;

    /** 类型 */
    @TableField(value = "type",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("信息化采购类型")
    private String type;

}
