package com.jp.med.bms.modules.bmsExecute.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;
import java.math.BigDecimal;

import lombok.Data;

/**
 * 库房物资采购预算
 * <AUTHOR>
 * @email -
 * @date 2023-11-16 11:22:49
 */
@Data
@TableName("bms_budget_goods" )
public class BmsBudgetGoodsDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    @ExcelIgnore
    private Integer id;

    /** 物资名称 */
    @TableField(value = "goods_name",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("物资名称")
    private String goodsName;

    /** 计量单位 */
    @TableField(value = "unit",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("计量单位")
    private String unit;

    /** 单价 */
    @TableField(value = "price",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("单价")
    private BigDecimal price;

    /** 数量 */
    @TableField(value = "cnt",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("数量")
    private Integer cnt;

    /** 预算数 */
    @TableField(value = "budget_amount",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("预算数")
    private BigDecimal budgetAmount;

    /** 科室 */
    @TableField(value = "dept",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("申请科室")
    private String dept;

    /** 预算任务 */
    @TableField(value = "task_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelIgnore
    private String taskCode;

    /** 审核状态(0:未审核 1:已审核) */
    @TableField(value = "chk",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelIgnore
    private String chk;

    @TableField(value = "memo",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("备注")
    private String memo;

}
