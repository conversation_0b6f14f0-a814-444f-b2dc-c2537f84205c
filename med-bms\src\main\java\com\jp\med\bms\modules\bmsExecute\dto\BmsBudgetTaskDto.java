package com.jp.med.bms.modules.bmsExecute.dto;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 预算任务表
 * <AUTHOR>
 * @email -
 * @date 2023-10-19 17:18:28
 */
@Data
@TableName("bms_budget_task" )
public class BmsBudgetTaskDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 预算任务编码 */
    @TableField(value = "budget_task_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String budgetTaskCode;

    /** 预算任务名称 */
    @TableField(value = "budget_task_name",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String budgetTaskName;

    /**  流程模板 */
    @TableField(value = "budget_flow_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String budgetFlowCode;

    /** 创建时间 */
    @TableField(value = "create_time",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String createTime;

    /** 有效标志 */
    @TableField(value = "active_flag",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String activeFlag;

    /** 执行状态 */
    @TableField(value = "status",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String status;

    /** 备注 */
    @TableField(value = "remarks",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String remarks;

    /**  医疗机构编码 */
    @TableField(value = "hospital_id",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String hospitalId;

    /**  预算编制表 */
    @TableField(value = "budget_table_id",insertStrategy = FieldStrategy.NOT_EMPTY)
    private Integer budgetTableId;

    /**  预算年度 */
    @TableField(value = "year",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String year;
}
