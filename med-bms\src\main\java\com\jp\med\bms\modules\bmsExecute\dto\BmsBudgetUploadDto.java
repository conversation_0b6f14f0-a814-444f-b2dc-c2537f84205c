package com.jp.med.bms.modules.bmsExecute.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class BmsBudgetUploadDto {

    @ExcelProperty("科室")
    private String dept;
    @ExcelProperty("预算准备金")
    private BigDecimal YSZBJ;
    @ExcelProperty("公务接待费")
    private BigDecimal GWJDF;
    @ExcelProperty("印刷费")
    private BigDecimal YSF;
    @ExcelProperty("咨询费")
    private BigDecimal ZXF;
    @ExcelProperty("门诊")
    private BigDecimal MZ;
    @ExcelProperty("工资福利支出")
    private BigDecimal GZFLZC;
    @ExcelProperty("其他商品和服务支出")
    private BigDecimal QTSPHFWZC;
    @ExcelProperty("债务利息及费用支出")
    private BigDecimal ZWLXJFYZC;
    @ExcelProperty("租赁费")
    private BigDecimal ZLF;
    @ExcelProperty("对个人和家庭的补助")
    private BigDecimal DGRHJTDBZ;
    @ExcelProperty("邮电费")
    private BigDecimal YDF;
    @ExcelProperty("物业管理费")
    private BigDecimal WYGLF;
    @ExcelProperty("培训费")
    private BigDecimal PXF;
    @ExcelProperty("劳务费")
    private BigDecimal LWF;
    @ExcelProperty("福利费")
    private BigDecimal FLF;
    @ExcelProperty("办公费")
    private BigDecimal BGF;
    @ExcelProperty("税金及附加费用")
    private BigDecimal SJJFJFY;
    @ExcelProperty("水费")
    private BigDecimal SF;
    @ExcelProperty("专用燃料费")
    private BigDecimal ZYRLF;
    @ExcelProperty("差旅费")
    private BigDecimal CLF;
    @ExcelProperty("其他支出")
    private BigDecimal QTZC;
    @ExcelProperty("委托业务费")
    private BigDecimal WTYWF;
    @ExcelProperty("专用材料费")
    private BigDecimal ZYCLF;
    @ExcelProperty("其他交通费用")
    private BigDecimal QTJTFY;
    @ExcelProperty("会议费")
    private BigDecimal HYF;
    @ExcelProperty("维修（护）费")
    private BigDecimal WXHF;
    @ExcelProperty("固定资产折旧费")
    private BigDecimal GDZCZJF;
    @ExcelProperty("手续费")
    private BigDecimal SXF;
    @ExcelProperty("提取坏账准备")
    private BigDecimal TQHZZB;
    @ExcelProperty("工会经费")
    private BigDecimal GHJF;
    @ExcelProperty("电费")
    private BigDecimal DF;
    @ExcelProperty("住院")
    private BigDecimal ZY1;
    @ExcelProperty("医技科室业务工作量")
    private BigDecimal YJKSYWGZL;
    @ExcelProperty("工资福利支出/伙食补助费")
    private BigDecimal HSBZF;
    @ExcelProperty("工资福利支出/奖金")
    private BigDecimal JJ;
    @ExcelProperty("工资福利支出/职工基本医疗保险缴费")
    private BigDecimal ZGJBYLBXJF;
    @ExcelProperty("工资福利支出/公务员医疗补助缴费")
    private BigDecimal GWYYLBZJF;
    @ExcelProperty("对个人和家庭的补助/生活补助")
    private BigDecimal SHBZ;
    @ExcelProperty("门诊/门诊人次控制费用")
    private BigDecimal MZRCKZFY;
    @ExcelProperty("对个人和家庭的补助/抚恤金")
    private BigDecimal FXJ;
    @ExcelProperty("对个人和家庭的补助/奖励金")
    private BigDecimal JLJ;
    @ExcelProperty("门诊/门诊人次")
    private BigDecimal MZRC;
    @ExcelProperty("工资福利支出/职工失业保险缴费")
    private BigDecimal ZGSYBXJF;
    @ExcelProperty("工资福利支出/职工工伤保险缴费")
    private BigDecimal ZGGSBXJF;
    @ExcelProperty("咨询费/咨询费(医务部)")
    private BigDecimal ZXF_YWB;
    @ExcelProperty("咨询费/咨询费(科教科)")
    private BigDecimal ZXF_KJK;
    @ExcelProperty("工资福利支出/绩效工资")
    private BigDecimal JXGZ;
    @ExcelProperty("其他商品和服务支出/会员费")
    private BigDecimal HYF1;
    @ExcelProperty("培训费/培训费(科教科)")
    private BigDecimal PXF_KJK;
    @ExcelProperty("劳务费/劳务费(医务部)")
    private BigDecimal LWF_YW;
    @ExcelProperty("福利费/福利费(人力资源部)")
    private BigDecimal FLF_RLZY;
    @ExcelProperty("福利费/福利费(预保科)")
    private BigDecimal FLF_YBK;
    @ExcelProperty("福利费/福利费(工会)")
    private BigDecimal FLF_GH;
    @ExcelProperty("物业管理费/物业管理费(人力资源部)")
    private BigDecimal WYGLF_RLZY;
    @ExcelProperty("物业管理费/物业管理费(总务科)")
    private BigDecimal WYGLF_ZW;
    @ExcelProperty("工资福利支出/其他工资福利支出")
    private BigDecimal QTGZFLZC;
    @ExcelProperty("住院/次均药品费用")
    private BigDecimal YZB1;
    @ExcelProperty("工资福利支出/津贴补贴")
    private BigDecimal JTBT;
    @ExcelProperty("其他支出/其他支出子项")
    private BigDecimal QTZCZX;
    @ExcelProperty("其他支出/赠与")
    private BigDecimal ZY;
    @ExcelProperty("对个人和家庭的补助/医疗费补助")
    private BigDecimal YLFBZ;
    @ExcelProperty("办公费/办公用品")
    private BigDecimal BGYP;
    @ExcelProperty("邮电费/电话费")
    private BigDecimal DHF;
    @ExcelProperty("专用材料费/医用卫生材料")
    private BigDecimal YYWSCL;
    @ExcelProperty("维修（护）费/一般设备维修费")
    private BigDecimal YBSBWXF;
    @ExcelProperty("维修（护）费/专用设备维修费")
    private BigDecimal ZYSBWXF;
    @ExcelProperty("其他商品和服务支出/医疗废物处置费")
    private BigDecimal YLFWCZF;
    @ExcelProperty("其他商品和服务支出/党建活动经费")
    private BigDecimal DJHDJF;
    @ExcelProperty("办公费/公杂费")
    private BigDecimal GZF;
    @ExcelProperty("邮电费/邮寄费")
    private BigDecimal YJF;
    @ExcelProperty("专用材料费/总务低值易耗品")
    private BigDecimal ZWDZYHP;
    @ExcelProperty("专用材料费/其他总务材料")
    private BigDecimal QTZWCL;
    @ExcelProperty("办公费/书报杂志费")
    private BigDecimal SBZZF;
    @ExcelProperty("专用材料费/医用高值材料")
    private BigDecimal YYGZCL;
    @ExcelProperty("其他交通费用/车辆保养维护费")
    private BigDecimal CLBYWHF;
    @ExcelProperty("其他交通费用/车辆燃料费")
    private BigDecimal CLRLF;
    @ExcelProperty("其他交通费用/过路过桥费")
    private BigDecimal GLGQF;
    @ExcelProperty("其他交通费用/汽车保险费")
    private BigDecimal QCBXF;
    @ExcelProperty("其他商品和服务支出/其他商品服务支出-其他")
    private BigDecimal QTSPFWZC_QT;
    @ExcelProperty("其他商品和服务支出/医疗纠纷处理费")
    private BigDecimal YLJFCLF;
    @ExcelProperty("其他商品和服务支出/文化建设费")
    private BigDecimal WHJSF;
    @ExcelProperty("其他商品和服务支出/广告宣传费")
    private BigDecimal GGXCF;
    @ExcelProperty("培训费/培训费(院办)")
    private BigDecimal PXF_YB;
    @ExcelProperty("劳务费/劳务费(院办)")
    private BigDecimal LWF_YB;
    @ExcelProperty("住院/出院人次")
    private BigDecimal CYRC;
    @ExcelProperty("住院/实际开放床日")
    private BigDecimal SJKFCR;
    @ExcelProperty("住院/实际占用床日")
    private BigDecimal SJZYCR;
    @ExcelProperty("住院/每床日平均费用")
    private BigDecimal MCRPJFY;
    @ExcelProperty("住院/床位使用率")
    private BigDecimal CWSYL;
    @ExcelProperty("住院/手术台次")
    private BigDecimal SSTC;
    @ExcelProperty("住院/四级手术台次")
    private BigDecimal SSTC_4;
    @ExcelProperty("住院/平均住院日")
    private BigDecimal PJZYR;
    @ExcelProperty("住院/次均费用")
    private BigDecimal CJFY;
    @ExcelProperty("医技科室业务工作量/检查人次")
    private BigDecimal JCRC;
    @ExcelProperty("医技科室业务工作量/人均检查费")
    private BigDecimal RJJCF;
    @ExcelProperty("住院/次均卫生材料费用")
    private BigDecimal WSCLSRZB;
    @ExcelProperty("其他交通费用/租车费")
    private BigDecimal ZCF;
    @ExcelProperty("门诊/次均药品费用")
    private BigDecimal YZB;
    @ExcelProperty("门诊/单位批量体检人次")
    private BigDecimal DWPLTJRC;
    @ExcelProperty("专用材料费/药品费")
    private BigDecimal YPF;
    @ExcelProperty("门诊/单位批量体检次均费用")
    private BigDecimal DWPLTJCJFY;
    @ExcelProperty("门诊/零星体检人次")
    private BigDecimal LXTJRC;
    @ExcelProperty("门诊/零星体检次均费用")
    private BigDecimal LXTJCJFY;
    @ExcelProperty("维修（护）费/房屋维修费")
    private BigDecimal FWWXF;
    @ExcelProperty("维修（护）费/网络信息系统运行维护费")
    private BigDecimal WLXXXTYHWHF;
    @ExcelProperty("工资福利支出/基本工资")
    private BigDecimal JBGZ;
    @ExcelProperty("对个人和家庭的补助/退休费")
    private BigDecimal TXF;
    @ExcelProperty("债务利息及费用支出/国内债务付息")
    private BigDecimal GNZWFX;
    @ExcelProperty("专用材料费/医用低值易耗品")
    private BigDecimal YYDZYHP;
    @ExcelProperty("工资福利支出/机关事业单位基本养老保险缴费")
    private BigDecimal JGSYDWJBYLBXJF;
    @ExcelProperty("工资福利支出/职业年金缴费")
    private BigDecimal ZYNJJF;
    @ExcelProperty("工资福利支出/医疗费")
    private BigDecimal YLF;
    @ExcelProperty("工资福利支出/职工企业基本养老保险缴费")
    private BigDecimal ZGQYJBYLBXJF;
    @ExcelProperty("工资福利支出/其他社会保障缴费")
    private BigDecimal QTSHBZJF;
    @ExcelProperty("工资福利支出/住房公积金")
    private BigDecimal ZFGJJ;
    @ExcelProperty("工资福利支出/基本工资/岗位工资")
    private BigDecimal GWGZ;
    @ExcelProperty("工资福利支出/绩效工资/医疗质量与安全")
    private BigDecimal YLZLYAQ;
    @ExcelProperty("工资福利支出/绩效工资/科研绩效")
    private BigDecimal KYJX;
    @ExcelProperty("维修（护）费/专用设备维修费/维保-DR")
    private BigDecimal WBDR;
    @ExcelProperty("专用材料费/医用卫生材料/放射材料费")
    private BigDecimal FSCLF;
    @ExcelProperty("办公费/办公用品/办公用品(总务科)")
    private BigDecimal BGYP_ZW;
    @ExcelProperty("维修（护）费/一般设备维修费/一般设备维修费(总务科)")
    private BigDecimal YBSBWXF_ZW;
    @ExcelProperty("维修（护）费/一般设备维修费/一般设备维修费(医学装备科)")
    private BigDecimal YBSBWXF_YXZB;
    @ExcelProperty("其他商品和服务支出/会员费/会员费(院办)")
    private BigDecimal HYF1_YB;
    @ExcelProperty("其他商品和服务支出/其他商品服务支出-其他/其他商品服务支出-其他(保卫科)")
    private BigDecimal QTSPFWZC_QT_BWK;
    @ExcelProperty("其他商品和服务支出/其他商品服务支出-其他/其他商品服务支出-其他(医务部)")
    private BigDecimal QTSPFWZC_QT_YWB;
    @ExcelProperty("其他交通费用/过路过桥费/过路过桥费(院办)")
    private BigDecimal GLGQF_YB;
    @ExcelProperty("办公费/办公用品/办公用品(信息科)")
    private BigDecimal BGYP_XX;
    @ExcelProperty("其他商品和服务支出/会员费/会员费(科教科)")
    private BigDecimal HYF1_KJK;
    @ExcelProperty("邮电费/电话费/电话费(总务科)")
    private BigDecimal DHF_ZW;
    @ExcelProperty("其他交通费用/车辆保养维护费/车辆保养维护费(院办)")
    private BigDecimal CLBYWHF_YB;
    @ExcelProperty("其他交通费用/车辆保养维护费/车辆保养维护费(总务科)")
    private BigDecimal CLBYWHF_ZW;
    @ExcelProperty("其他交通费用/车辆燃料费/车辆燃料费(院办)")
    private BigDecimal CLRLF_YB;
    @ExcelProperty("其他交通费用/过路过桥费/过路过桥费(总务科)")
    private BigDecimal GLGQF_ZW;
    @ExcelProperty("其他商品和服务支出/其他商品服务支出-其他/其他商品服务支出-其他(科教科)")
    private BigDecimal QTSPFWZC_QT_KJK;
    @ExcelProperty("对个人和家庭的补助/医疗费补助/退休人员医疗费")
    private BigDecimal TXRYYLF;
    @ExcelProperty("工资福利支出/绩效工资/完成公共医疗卫生服务")
    private BigDecimal WCGGYLWSFW;
    @ExcelProperty("工资福利支出/绩效工资/管理效能")
    private BigDecimal GLXN;
    @ExcelProperty("其他商品和服务支出/其他商品服务支出-其他/其他商品服务支出-其他(总务科)")
    private BigDecimal QTSPFWZC_QT_ZW;
    @ExcelProperty("其他商品和服务支出/其他商品服务支出-其他/其他商品服务支出-其他(财务)")
    private BigDecimal QTSPFWZC_QT_RLZY;
    @ExcelProperty("工资福利支出/津贴补贴/卫生津贴")
    private BigDecimal WSJT;
    @ExcelProperty("工资福利支出/伙食补助费/夜餐费")
    private BigDecimal YCF;
    @ExcelProperty("工资福利支出/伙食补助费/误餐费")
    private BigDecimal WCF;
    @ExcelProperty("工资福利支出/绩效工资/奖励性绩效")
    private BigDecimal JLXJX;
    @ExcelProperty("工资福利支出/绩效工资/目标管理奖")
    private BigDecimal MBGLJ;
    @ExcelProperty("工资福利支出/绩效工资/超时劳动补助")
    private BigDecimal CSLDBZ;
    @ExcelProperty("工资福利支出/绩效工资/收支结余奖励")
    private BigDecimal SZJYJL;
    @ExcelProperty("工资福利支出/绩效工资/基础绩效")
    private BigDecimal JCJX;
    @ExcelProperty("专用材料费/医用卫生材料/化验材料费")
    private BigDecimal HYCLF;
    @ExcelProperty("工资福利支出/其他工资福利支出/法定假日加班补助")
    private BigDecimal FDJRJBBZ;
    @ExcelProperty("工资福利支出/其他工资福利支出/加班工资")
    private BigDecimal JBGZ1;
    @ExcelProperty("专用材料费/医用卫生材料/血费")
    private BigDecimal XF;
    @ExcelProperty("专用材料费/医用卫生材料/氧气费")
    private BigDecimal YQF;
    @ExcelProperty("对个人和家庭的补助/退休费/退休人员生活补助")
    private BigDecimal TXRYSHBZ;
    @ExcelProperty("对个人和家庭的补助/退休费/退休人员其他费用")
    private BigDecimal TXRYQTFY;
    @ExcelProperty("对个人和家庭的补助/医疗费补助/职工医疗费")
    private BigDecimal ZGYLF;
    @ExcelProperty("维修（护）费/专用设备维修费/放射设备检测-辐射环境现状检测")
    private BigDecimal FSSBFSHJXZJC;
    @ExcelProperty("专用材料费/药品费/中草药")
    private BigDecimal ZCY;
    @ExcelProperty("邮电费/电话费/电话费(信息科)")
    private BigDecimal DHF_XX;
    @ExcelProperty("其他交通费用/车辆燃料费/车辆燃料费(总务科)")
    private BigDecimal CLRLF_ZW;
    @ExcelProperty("其他商品和服务支出/其他商品服务支出-其他/其他商品服务支出-其他(各科室误餐费)")
    private BigDecimal QTSPFWZC_QT_YB1;
    @ExcelProperty("其他商品和服务支出/其他商品服务支出-其他/其他商品服务支出-其他(审计科)")
    private BigDecimal QTSPFWZC_QT_SJ;
    @ExcelProperty("维修（护）费/专用设备维修费/维保-CT")
    private BigDecimal WBCT;
    @ExcelProperty("其他商品和服务支出/其他商品服务支出-其他/其他商品服务支出-其他(院办)")
    private BigDecimal QTSPFWZC_QT_YB;
    @ExcelProperty("维修（护）费/专用设备维修费/维保-MRI")
    private BigDecimal WBMRI;
    @ExcelProperty("维修（护）费/专用设备维修费/维保-DSA")
    private BigDecimal WBDSA;
    @ExcelProperty("维修（护）费/专用设备维修费/计量器具检测")
    private BigDecimal JLQJJC;
    @ExcelProperty("维修（护）费/专用设备维修费/放射设备检测-机器性能及机房防护检测")
    private BigDecimal FSJSBJQXNJJFFHJC;
    @ExcelProperty("维修（护）费/专用设备维修费/特种设备压力容器")
    private BigDecimal TZSBYLRQ;
    @ExcelProperty("维修（护）费/专用设备维修费/专用设备维修费-其他")
    private BigDecimal ZYSBWXF_QT;
    @ExcelProperty("专用材料费/药品费/西药")
    private BigDecimal XY;
    @ExcelProperty("工资福利支出/绩效工资/工作量/成本控制")
    private BigDecimal GZLCBKZ;
    @ExcelProperty("工资福利支出/基本工资/薪级工资")
    private BigDecimal XJGZ;
    @ExcelProperty("工资福利支出/基本工资/护士10%")
    private BigDecimal HS10;
    @ExcelProperty("工资福利支出/津贴补贴/地区附加津贴")
    private BigDecimal DQFJJT;
    @ExcelProperty("工资福利支出/津贴补贴/护龄津贴")
    private BigDecimal HLJT;
    @ExcelProperty("工资福利支出/其他工资福利支出/临聘人员工资")
    private BigDecimal LPRYGZ;
    @ExcelProperty("工资福利支出/其他工资福利支出/招聘人员工资")
    private BigDecimal ZPRYGZ;
    @ExcelProperty("其他交通费用/汽车保险费/汽车保险费(院办)")
    private BigDecimal QCBXF_YB;
    @ExcelProperty("其他交通费用/汽车保险费/汽车保险费(总务科)")
    private BigDecimal QCBXF_ZW;
    @ExcelProperty("维修（护）费/专用设备维修费/医疗设备保养")
    private BigDecimal YLSBBY;
    @ExcelProperty("专用材料费/医用卫生材料/其他卫生材料")
    private BigDecimal QTWSCL;
    @ExcelProperty("工资福利支出/绩效工资/服务效率")
    private BigDecimal FWXL;
    @ExcelProperty("维修（护）费/专用设备维修费/生物安全柜")
    private BigDecimal SWAQG;
    @ExcelProperty("工资福利支出/津贴补贴/其他补贴")
    private BigDecimal QTBT;
    @ExcelProperty("门诊/(次均)其他卫生材料")
    private BigDecimal MZCJQTWSCL;
    @ExcelProperty("门诊/(次均)医用高值材料")
    private BigDecimal MZCJYYGZCL;
    @ExcelProperty("门诊/(次均)医用低值易耗品")
    private BigDecimal MZCJYYDZYHP;
    @ExcelProperty("住院/(次均)其他卫生材料")
    private BigDecimal CJZYQTWSCL;
    @ExcelProperty("住院/(次均)医用低值易耗品")
    private BigDecimal CJZYYYDZYHP;
    @ExcelProperty("住院/(次均)医用高值材料")
    private BigDecimal CJZYYYGZCL;


    @ExcelProperty("培训费/培训费(科教科)/进修经费")
    private BigDecimal JXJF;
    @ExcelProperty("培训费/培训费(科教科)/学术会议及短期培训经费")
    private BigDecimal XSHYDQPX;
    @ExcelProperty("培训费/培训费(科教科)/继续医学教育项目经费")
    private BigDecimal JXYXJYXMJF;
    @ExcelProperty("培训费/培训费(科教科)/其他临时性培训经费")
    private BigDecimal QYLSPXJF;

    @ExcelProperty("维修（护）费/专用设备维修费/维保-直线加速器")
    private BigDecimal WBZXJSQ;
    @ExcelProperty("维修（护）费/专用设备维修费/维保-C臂")
    private BigDecimal WBCB;


    @ExcelProperty("住院/日间手术台次")
    private BigDecimal RJSSTC;
    @ExcelProperty("住院/微创手术台次")
    private BigDecimal WCSSTC;
    @ExcelProperty("财政收入/基本拨款收入")
    private BigDecimal JBBKSR;
    @ExcelProperty("财政收入/项目拨款收入")
    private BigDecimal XMBKSR;
    @ExcelProperty("其他收入")
    private BigDecimal QTSR;
    @ExcelProperty("医技科室业务工作量/(次均)放射材料费")
    private BigDecimal CJFSCLF;
    @ExcelProperty("医技科室业务工作量/(次均)化验材料费")
    private BigDecimal CJHYCLF;
    @ExcelProperty("住院/(次均)化验材料")
    private BigDecimal CJZYHYCLF;
}
