package com.jp.med.bms.modules.bmsExecute.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * 预算项目工作量情况
 *
 * <AUTHOR>
 * @email -
 * @date 2024-07-27 15:16:25
 */
@Data
@TableName("bms_budget_workload")
public class BmsBudgetWorkloadDto extends CommonQueryDto {

    /**
     * id
     */
    @TableId("id")
    private Integer id;

    /**
     * 预算任务编码
     */
    @TableField("budget_task_code")
    private String budgetTaskCode;

    /**
     * 月份
     */
    @TableField("month")
    private String month;

    /**
     * 预算项目编码
     */
    @TableField("budget_code")
    private String budgetCode;

    @TableField(exist = false)
    private List<String> budgetCodes;

    /**
     * 预算项目名称
     */
    @TableField("budget_name")
    private String budgetName;

    /**
     * 预算编制类别  SR/ZCL
     */
    @TableField("budget_type_code")
    private String budgetTypeCode;

    /**
     * 实际数量
     */
    @TableField("actual_amt")
    private BigDecimal actualAmt;

    /**
     * 部门编码
     **/
    @TableField("dept_code")
    private String deptCode;

    /**
     * 查询部门
     **/
    @TableField(exist = false)
    private List<String> deptQuery;

    /**
     * 上传文件
     **/
    @TableField(exist = false)
    private MultipartFile[] files;

    /**
     * 上传月份
     **/
    @TableField(exist = false)
    private String uploadMonth;

    /**
     * 年度
     **/
    @TableField(exist = false)
    private String year;

    /**
     * 经济科目 query集合
     **/
    @TableField(exist = false)
    private List<String> econCodeStr;

    /**
     * 会计科目 query集合
     **/
    @TableField(exist = false)
    private List<String> actigCodeStr;

    /**
     * 经济科目编码
     **/
    @TableField(exist = false)
    private String econSubCode;

    /**
     * 会计科目编码
     **/
    @TableField(exist = false)
    private String actigSubCode;

    /**
     * 人次
     */
    private BigDecimal budgetCodePersonSize;

    /**
     * 均次
     */
    private BigDecimal budgetCodeAverageSize;

    /**
     * 编制项目合计 人次*均费
     */
    private BigDecimal budgetCodeTotal;

    /**
     * 部门编码-原始
     **/
    private String originalDeptCode;

    /**
     * 开始日期
     **/
    private String startDate;

    /**
     * 结束日期
     **/
    private String endDate;

    @TableField(exist = false)
    private String type;

}
