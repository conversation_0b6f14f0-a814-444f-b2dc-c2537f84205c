package com.jp.med.bms.modules.bmsExecute.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * 预算调整表
 * <AUTHOR>
 * @email -
 * @date 2023-06-01 18:20:31
 */
@Data
@TableName("bms_budget_adjust" )
public class BmsExecuteBudgetAdjustDto extends CommonQueryDto {

    /** 预算调整表ID */
    @TableId("budget_adjust_id")
    private Long budgetAdjustId;

    /** 预算调整申请ID */
    @TableField("budget_apply_id")
    private Long budgetApplyId;

    /** 预算结果ID */
    @TableField("budget_results_id")
    private Long budgetResultsId;

    /** 附件 */
    @TableField("attachment")
    private String attachment;

    /** 说明 */
    @TableField("remark")
    private String remark;

    /** 预算数(调整后) */
    @TableField("budget_amount")
    private BigDecimal budgetAmount;

    /** 原始预算数 */
    @TableField("original_budget_amount")
    private BigDecimal originalBudgetAmount;

    /** 预算编制流程ID */
    @TableField(exist = false)
    private Long budgetFlowId;

    /** 机构编码 */
    @TableField(exist = false)
    private String orgId;

    /**附件*/
    @TableField(exist = false)
    private MultipartFile file;

    /** 选取的项目 */
    @TableField(exist = false)
    private List<String> codes;

    @TableField(exist = false)
    private String budgetTakeCode;
    @TableField(exist = false)
    private String budgetName;
    @TableField(exist = false)
    private String budgetTypeCode;
    @TableField(exist = false)
    private String deptCode;

    @TableField(exist = false)
    private List<Integer> ids;


}
