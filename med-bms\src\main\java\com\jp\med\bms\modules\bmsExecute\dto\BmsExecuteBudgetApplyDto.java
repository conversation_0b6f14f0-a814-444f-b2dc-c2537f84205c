package com.jp.med.bms.modules.bmsExecute.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 预算调整申请表
 * <AUTHOR>
 * @email -
 * @date 2023-06-01 14:47:42
 */
@Data
@TableName("bms_budget_apply" )
public class BmsExecuteBudgetApplyDto extends CommonQueryDto {

    /** 预算调整申请表ID */
    @TableId(value = "budget_apply_id",type = IdType.AUTO)
    private Long budgetApplyId;

    /** 申请类型 */
    @TableField("flow_model_id")
    private Long flowModelId;

    /** 创建时间 */
    @TableField("create_time")
    private Date createTime;

    /** 创建人 */
    @TableField("username")
    private String username;

    /** 说明 */
    @TableField("remark")
    private String remark;

    /** 附件 */
    @TableField("attachment")
    private String attachment;

    /** 状态(0:已保存,1:已提交,2:已驳回,3:申请成功) */
    @TableField("status")
    private String status;

    /** 医疗机构ID */
    @TableField("hospital_id")
    private String hospitalId;

    /** 业务key */
    @TableField("business_key")
    private String businessKey;

    /**  流程编码 */
    @TableField("budget_flow_id")
    private Long budgetFlowId;

    /** 申请部门 */
    @TableField("org_id")
    private String orgId;

    /** 任务ID */
    @TableField(exist = false)
    private String taskId;

    /**附件*/
    @TableField(exist = false)
    private MultipartFile file;

    /** 操作类型 */
    @TableField(exist = false)
    private String operationType;

    /** 调整的预算 */
    @TableField(exist = false)
    private List<BmsExecuteBudgetAdjustDto> adjustList;

    @TableField("budget_take_code")
    private String budgetTakeCode;

    @TableField("process_instance_id")
    private String processInstanceId;

    @TableField(exist = false)
    private String orgName;

    @TableField("emp_code")
    private String empCode;

    @TableField("att")
    private String att;

    @TableField("att_name")
    private String attName;

    /** 附件 */
    @TableField(exist = false)
    private List<MultipartFile> attFiles;

    @TableField(exist = false)
    private String empName;

    @TableField(exist = false)
    private Map<String,Object> bpmParams;

}
