package com.jp.med.bms.modules.bmsExecute.dto;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;
import java.math.BigDecimal;
import java.util.List;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 预算编制数据
 * <AUTHOR>
 * @email -
 * @date 2023-04-26 15:40:51
 */
@Data
@TableName("bms_budget_data" )
public class BmsExecuteBudgetDataDto extends CommonQueryDto {

    /** 预算编制项数据ID */
    @TableId("budget_data_id")
    private Integer budgetDataId;

    /** 预算执行节点 */
    @TableField(value = "flow_detail_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String flowDetailCode;

    /** 预算编制项 */
    @TableField(value = "budget_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String budgetCode;

    /** 预算数 */
    @TableField(value = "budget_amount",insertStrategy = FieldStrategy.NOT_EMPTY)
    private BigDecimal budgetAmount;

    /** 预算科室 */
    @TableField(value = "org_id",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String orgId;

    /** 预算编制任务 */
    @TableField(value = "budget_task_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String budgetTaskCode;

    /** 预算单位 */
    @TableField(value = "hospital_id",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String hospitalId;

    /** 执行科室 */
    @TableField(value = "execute_dept",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String executeDept;

    /** 1:保存  2:提交*/
    @TableField(exist = false)
    private String type;

    /** 执行类型 */
    @TableField(exist = false)
    private String ptr;

    /** 填报状态表ID */
    @TableField(exist = false)
    private Integer budgetFillingId;

    /** 流程执行顺序 */
    @TableField(exist = false)
    private Integer organizationOrder;

    /** 流程填报状态 */
    @TableField(exist = false)
    private String status;

    /** 预算执行节点 */
    @TableField(exist = false)
    private Long nextFlowDetailId;

    /** 科室类型 */
    @TableField(exist = false)
    private String deptType;

    /** 流程状态 */
    @TableField(exist = false)
    private String flag;

    /** 流程编码 */
    @TableField(exist = false)
    private String flowDetailCodeNext;

    @TableField(exist = false)
    private Integer startPage;

    @TableField(exist = false)
    private Integer endPage;

    /** 上次文件类型 */
    @TableField(exist = false)
    private String uploadType;

    @TableField(exist = false)
    private MultipartFile[] files;

    /** 修改的数据 */
    private List<BmsExecuteBudgetDataDto> updateList;

    /** 查询类型 */
    @TableField(exist = false)
    private String queryFlag;

    /** 预算类别 */
    @TableField(exist = false)
    private String budgetTypeCode;

    /** 科室查询条件 */
    @TableField(exist = false)
    private String[] deptQuery;

    /** 是否按照经济科目汇总 */
    @TableField(exist = false)
    private String econSub;

}
