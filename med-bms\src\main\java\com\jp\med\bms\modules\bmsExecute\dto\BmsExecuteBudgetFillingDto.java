package com.jp.med.bms.modules.bmsExecute.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;
import java.math.BigDecimal;

import lombok.Data;

/**
 * 预算填报状态表
 * <AUTHOR>
 * @email -
 * @date 2023-04-28 10:35:42
 */
@Data
@TableName("bms_budget_filling" )
public class BmsExecuteBudgetFillingDto extends CommonQueryDto {

    /** 预算填报状态ID */
    @TableId("budget_filling_id")
    private Integer budgetFillingId;

    /** 预算编制详情 */
    @TableField("flow_detail_code")
    private String flowDetailCode;

    /** 预算任务  */
    @TableField("budget_task_code")
    private String budgetTaskCode;

    /** 填报部门 */
    @TableField("org_id")
    private String orgId;

    /** 填报状态(0:填报中,1:已提交,2:已驳回,3:填报完成) */
    @TableField("status")
    private String status;

    /** 编制节点名称 */
    @TableField(exist = false)
    private String flowDetailName;

    /** 流程顺序 */
    @TableField(exist = false)
    private String organizationOrder;

    /** 预算编制流程ID */
    @TableField(exist = false)
    private Long budgetFlowId;

}
