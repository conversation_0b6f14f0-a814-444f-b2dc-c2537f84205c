package com.jp.med.bms.modules.bmsExecute.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 预算结果
 * <AUTHOR>
 * @email -
 * @date 2023-05-31 17:55:21
 */
@Data
@TableName("bms_budget_results" )
public class BmsExecuteBudgetResultsDto extends CommonQueryDto {

    /** 预算结果ID */
    @TableId("budget_results_id")
    private Long budgetResultsId;

    /** 預算执行节点 **/
//    @TableField("flow_detail_code")
    @TableField(exist = false)
    private String flowDetailCode;

    /** 预算编制项编码 */
    @TableField("budget_code")
    private String budgetCode;

    /** 预算数 */
    @TableField("budget_amount")
    private BigDecimal budgetAmount;

    /** 预算科室 */
    @TableField("org_id")
    private String orgId;

    /** 归口科室 */
    @TableField("centralized_dept")
    private String centralizedDept;

    /** 预算任务 */
    @TableField("budget_task_code")
    private String budgetTaskCode;

    /** 预算单位 */
    @TableField("hospital_id")
    private String hospitalId;

    /** 年 */
    @TableField(exist = false)
    private String year;

    /** ids */
    @TableField(exist = false)
    private List<String> orgIds;

    /** 预算项目codes **/
    @TableField(exist = false)
    private List<String> budgetCodes;

    /** 报销项目codes **/
    @TableField(exist = false)
    private List<String> reimItemCodes;

    @TableField(exist = false)
    private String budgetTakeCode;

    @TableField(exist = false)
    private String budgetName;
    @TableField(exist = false)
    private String budgetTypeCode;
    @TableField(exist = false)
    private String deptCode;

}
