package com.jp.med.bms.modules.bmsExecute.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;
import java.math.BigDecimal;

import lombok.Data;

/**
 * 科研教学经费预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 19:57:30
 */
@Data
@TableName("bms_fund" )
public class BmsFundDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    @ExcelIgnore
    private Integer id;

    /** 科室 */
    @TableField(value = "dept",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("申请科室")
    private String dept;

    /** 教学经费 */
    @TableField(value = "teach_fund",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("教学经费")
    private BigDecimal teachFund;

    /** 科研经费 */
    @TableField(value = "study_fund",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("科研经费")
    private BigDecimal studyFund;

    /** 预算数 */
    @TableField(value = "budget_amount",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("预算金额")
    private BigDecimal budgetAmount;

    /** 备注 */
    @TableField(value = "memo",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("备注")
    private String memo;

    /** 预算任务 */
    @TableField(value = "task_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelIgnore
    private String taskCode;

    /** 审核状态(0:未审核 1:已审核) */
    @TableField(value = "chk",insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelIgnore
    private String chk;

}
