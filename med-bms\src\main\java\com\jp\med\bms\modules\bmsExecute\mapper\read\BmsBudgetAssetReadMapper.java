package com.jp.med.bms.modules.bmsExecute.mapper.read;

import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetAssetDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetAssetVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 医疗设备购置预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:19:45
 */
@Mapper
public interface BmsBudgetAssetReadMapper extends BaseMapper<BmsBudgetAssetDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsBudgetAssetVo> queryList(BmsBudgetAssetDto dto);
}
