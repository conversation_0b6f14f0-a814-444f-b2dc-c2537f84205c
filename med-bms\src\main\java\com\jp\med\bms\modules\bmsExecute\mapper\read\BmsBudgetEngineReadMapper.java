package com.jp.med.bms.modules.bmsExecute.mapper.read;

import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetEngineDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetEngineVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 信息化建设项目预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:28:01
 */
@Mapper
public interface BmsBudgetEngineReadMapper extends BaseMapper<BmsBudgetEngineDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsBudgetEngineVo> queryList(BmsBudgetEngineDto dto);
}
