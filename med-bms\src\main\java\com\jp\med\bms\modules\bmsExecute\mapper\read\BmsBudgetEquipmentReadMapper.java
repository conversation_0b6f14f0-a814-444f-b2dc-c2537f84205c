package com.jp.med.bms.modules.bmsExecute.mapper.read;

import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetEquipmentDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetEquipmentVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 通用设备预算
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 14:06:20
 */
@Mapper
public interface BmsBudgetEquipmentReadMapper extends BaseMapper<BmsBudgetEquipmentDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsBudgetEquipmentVo> queryList(BmsBudgetEquipmentDto dto);
}
