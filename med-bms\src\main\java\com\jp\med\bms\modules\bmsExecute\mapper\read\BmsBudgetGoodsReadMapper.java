package com.jp.med.bms.modules.bmsExecute.mapper.read;

import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetGoodsDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetGoodsVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 库房物资采购预算
 * <AUTHOR>
 * @email -
 * @date 2023-11-16 11:22:49
 */
@Mapper
public interface BmsBudgetGoodsReadMapper extends BaseMapper<BmsBudgetGoodsDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsBudgetGoodsVo> queryList(BmsBudgetGoodsDto dto);
}
