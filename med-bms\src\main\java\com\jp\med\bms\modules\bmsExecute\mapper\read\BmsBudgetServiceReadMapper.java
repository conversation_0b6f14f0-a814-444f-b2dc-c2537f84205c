package com.jp.med.bms.modules.bmsExecute.mapper.read;

import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetServiceDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetServiceVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 服务类采购预算
 * <AUTHOR>
 * @email -
 * @date 2023-11-16 11:03:01
 */
@Mapper
public interface BmsBudgetServiceReadMapper extends BaseMapper<BmsBudgetServiceDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsBudgetServiceVo> queryList(BmsBudgetServiceDto dto);
}
