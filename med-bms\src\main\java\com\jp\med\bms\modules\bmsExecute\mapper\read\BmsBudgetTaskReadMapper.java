package com.jp.med.bms.modules.bmsExecute.mapper.read;

import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetTaskDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetTaskVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 预算任务表
 * <AUTHOR>
 * @email -
 * @date 2023-10-19 17:18:28
 */
@Mapper
public interface BmsBudgetTaskReadMapper extends BaseMapper<BmsBudgetTaskDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsBudgetTaskVo> queryList(BmsBudgetTaskDto dto);


    /**
     * 查询任务流程的详情
     * @param dto
     * @return
     */
    List<BmsBudgetTaskVo> queryTaskFlowDetail(BmsBudgetTaskDto dto);

}
