package com.jp.med.bms.modules.bmsExecute.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetWorkloadDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgDeptCrspVo;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetSRVo;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetWorkloadVo;
import com.jp.med.bms.modules.bmsExecute.vo.BmsReimItemToBudgCfgVo;
import com.jp.med.bms.modules.dispose.vo.TitleVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 预算项目工作量情况
 *
 * <AUTHOR>
 * @email -
 * @date 2024-07-27 15:16:25
 */
@Mapper
public interface BmsBudgetWorkloadReadMapper extends BaseMapper<BmsBudgetWorkloadDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<BmsBudgetWorkloadVo> queryList(BmsBudgetWorkloadDto dto);

    /**
     * 查询月度累计总额
     *
     * @param dto
     * @return
     */
    List<BmsBudgetSRVo> queryMonthCountList(BmsBudgetWorkloadDto dto);

    List<TitleVo> queryTtile(BmsBudgetWorkloadDto dto);

    /**
     * 查询excel表头
     *
     * @param dto
     * @return
     */
    List<BmsBudgetWorkloadVo> queryExcelTitle(BmsBudgetWorkloadDto dto);


    /**
     * 基础
     *
     * @param dto
     * @return
     */
    List<BmsBudgDeptCrspVo> queryBudgetDeptCrspList(BmsBudgDeptCrspVo dto);

    /**
     * 查询经济科目对应预算的项
     *
     * @param dto
     * @return
     */
    List<BmsReimItemToBudgCfgVo> queryReimItemToBudget(BmsBudgetWorkloadDto dto);

    /**
     * 查询 科目 实际发生值
     *
     * @param dto
     * @return
     */
    List<BmsBudgetWorkloadVo> queryActualDetail(BmsBudgetWorkloadDto dto);

    /**
     * 查询项目发生值汇总
     *
     * @param dto
     * @return
     */
    List<BmsBudgetWorkloadVo> queryActualSummary(BmsBudgetWorkloadDto dto);

    /**
     * 查询差旅、培训明细实际数据
     * @return
     */
    List<BmsBudgetWorkloadVo> queryCLPXActualDetail(BmsBudgetWorkloadDto dto);
}
