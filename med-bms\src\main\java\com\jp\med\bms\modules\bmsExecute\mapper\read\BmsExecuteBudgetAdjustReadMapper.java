package com.jp.med.bms.modules.bmsExecute.mapper.read;

import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetAdjustDto;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetApplyDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetAdjustVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 预算调整表
 * <AUTHOR>
 * @email -
 * @date 2023-06-01 18:20:31
 */
@Mapper
public interface BmsExecuteBudgetAdjustReadMapper extends BaseMapper<BmsExecuteBudgetAdjustDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsExecuteBudgetAdjustVo> queryList(BmsExecuteBudgetAdjustDto dto);

    /**
     * 查询预算信息
     * @param dto
     * @return
     */
    List<BmsExecuteBudgetAdjustVo> queryBudget(BmsExecuteBudgetAdjustDto dto);

    List<BmsExecuteBudgetAdjustVo> queryBudgetNew(BmsExecuteBudgetAdjustDto dto);

    List<BmsExecuteBudgetAdjustVo> queryListNew(BmsExecuteBudgetApplyDto dto);
}
