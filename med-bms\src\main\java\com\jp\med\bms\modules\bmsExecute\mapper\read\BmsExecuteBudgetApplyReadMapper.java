package com.jp.med.bms.modules.bmsExecute.mapper.read;

import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetApplyDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetApplyVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 预算调整申请表
 * <AUTHOR>
 * @email -
 * @date 2023-06-01 14:47:42
 */
@Mapper
public interface BmsExecuteBudgetApplyReadMapper extends BaseMapper<BmsExecuteBudgetApplyDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsExecuteBudgetApplyVo> queryList(BmsExecuteBudgetApplyDto dto);

    BmsExecuteBudgetApplyDto selectByParam(BmsExecuteBudgetApplyDto dto);
}
