package com.jp.med.bms.modules.bmsExecute.mapper.read;

import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetDataDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetDataVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 预算编制数据
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-26 15:40:51
 */
@Mapper
public interface BmsExecuteBudgetDataReadMapper extends BaseMapper<BmsExecuteBudgetDataDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<BmsExecuteBudgetDataVo> queryList(BmsExecuteBudgetDataDto dto);

    /**
     * 查询科室
     *
     * @param dto
     * @return
     */
    List<BmsOrgVo> queryOrg(BmsExecuteBudgetDataDto dto);

    /**
     * 查询
     *
     * @param dto
     * @return
     */
    List<BmsExecuteBudgetDataVo> queryAllAmount(BmsExecuteBudgetDataDto dto);

    /**
     * 查询预算项目明细
     *
     * @param dto
     * @return
     */
    List<BmsExecuteBudgetDataVo> queryDataDetail(BmsExecuteBudgetDataDto dto);

    /**
     * 查询编制项目排序规则
     *
     * @param dto
     * @return
     */
    List<Map<String, Object>> queryBudgetOrder(BmsExecuteBudgetDataDto dto);
}
