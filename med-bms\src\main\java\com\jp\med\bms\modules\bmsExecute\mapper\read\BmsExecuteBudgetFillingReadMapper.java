package com.jp.med.bms.modules.bmsExecute.mapper.read;

import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetFillingDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetFillingVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 预算填报状态表
 * <AUTHOR>
 * @email -
 * @date 2023-04-28 10:35:42
 */
@Mapper
public interface BmsExecuteBudgetFillingReadMapper extends BaseMapper<BmsExecuteBudgetFillingDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsExecuteBudgetFillingVo> queryList(BmsExecuteBudgetFillingDto dto);

    /**
     * 查询执行情况
     * @return
     */
    List<BmsExecuteBudgetFillingVo> queryImplementation(BmsExecuteBudgetFillingDto dto);
}
