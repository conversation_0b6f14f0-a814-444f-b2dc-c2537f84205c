package com.jp.med.bms.modules.bmsExecute.mapper.read;

import com.jp.med.bms.modules.bmsExecute.dto.BmsFundDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsFundVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 科研教学经费预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 19:57:30
 */
@Mapper
public interface BmsFundReadMapper extends BaseMapper<BmsFundDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsFundVo> queryList(BmsFundDto dto);
}
