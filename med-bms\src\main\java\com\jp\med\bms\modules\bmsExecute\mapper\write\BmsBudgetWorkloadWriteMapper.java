package com.jp.med.bms.modules.bmsExecute.mapper.write;

import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetWorkloadDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 预算项目工作量情况
 * <AUTHOR>
 * @email -
 * @date 2024-07-27 15:16:25
 */
@Mapper
public interface BmsBudgetWorkloadWriteMapper extends BaseMapper<BmsBudgetWorkloadDto> {

    void insertWorkLoad(BmsBudgetWorkloadDto dto);

    /**
     * 删除业务量数据
     * @param dto
     */
    void deleteWorkLoads(BmsBudgetWorkloadDto dto);
}
