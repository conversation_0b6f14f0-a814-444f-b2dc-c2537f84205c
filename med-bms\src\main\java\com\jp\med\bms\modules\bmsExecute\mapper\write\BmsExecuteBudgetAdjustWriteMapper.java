package com.jp.med.bms.modules.bmsExecute.mapper.write;

import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetAdjustDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 预算调整表
 * <AUTHOR>
 * @email -
 * @date 2023-06-01 18:20:31
 */
@Mapper
public interface BmsExecuteBudgetAdjustWriteMapper extends BaseMapper<BmsExecuteBudgetAdjustDto> {

    /**
     * 删除预算调整信息
     * @param dto
     */
    void deleteAdjust(BmsExecuteBudgetAdjustDto dto);
}
