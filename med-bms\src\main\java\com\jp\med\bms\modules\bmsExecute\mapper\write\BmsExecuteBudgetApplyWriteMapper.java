package com.jp.med.bms.modules.bmsExecute.mapper.write;

import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetApplyDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 预算调整申请表
 * <AUTHOR>
 * @email -
 * @date 2023-06-01 14:47:42
 */
@Mapper
public interface BmsExecuteBudgetApplyWriteMapper extends BaseMapper<BmsExecuteBudgetApplyDto> {

    /**
     * 根据预算调整修改预算结果
     * @param dto
     */
    void updateAdjustResult(BmsExecuteBudgetApplyDto dto);
}
