package com.jp.med.bms.modules.bmsExecute.mapper.write;

import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetDataDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 预算编制数据
 * <AUTHOR>
 * @email -
 * @date 2023-04-26 15:40:51
 */
@Mapper
public interface BmsExecuteBudgetDataWriteMapper extends BaseMapper<BmsExecuteBudgetDataDto> {
    /**
     *
     * @param dto
     */
    void updateData(BmsExecuteBudgetDataDto dto);

    /**
     * 修改填报状态
     * @param dto
     */
    void updateFillingType(BmsExecuteBudgetDataDto dto);

    /**
     * 新增下一步流程的数据
     * @param dto
     */
    int insertNextNode(BmsExecuteBudgetDataDto dto);
}
