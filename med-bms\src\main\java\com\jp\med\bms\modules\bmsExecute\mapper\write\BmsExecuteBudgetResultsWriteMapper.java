package com.jp.med.bms.modules.bmsExecute.mapper.write;

import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetResultsDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 预算结果
 * <AUTHOR>
 * @email -
 * @date 2023-05-31 17:55:21
 */
@Mapper
public interface BmsExecuteBudgetResultsWriteMapper extends BaseMapper<BmsExecuteBudgetResultsDto> {
    /**
     * 写入预算结果表
     * @param dto
     */
    void insertResult(BmsExecuteBudgetResultsDto dto);
}
