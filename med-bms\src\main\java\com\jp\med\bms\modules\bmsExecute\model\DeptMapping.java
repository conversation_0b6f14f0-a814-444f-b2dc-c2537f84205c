package com.jp.med.bms.modules.bmsExecute.model;

import java.util.List;

/**
 * 科室映射类
 * 用于存储预算科室与HRP科室和用友科室的映射关系
 */
public class DeptMapping {
    
    /**
     * 预算科室名称
     */
    private String budgetDeptName;
    
    /**
     * HRP科室名称列表
     */
    private List<String> hrpDeptNames;
    
    /**
     * 用友科室名称列表
     */
    private List<String> yyDeptNames;

    public DeptMapping() {
    }

    public DeptMapping(String budgetDeptName, List<String> hrpDeptNames, List<String> yyDeptNames) {
        this.budgetDeptName = budgetDeptName;
        this.hrpDeptNames = hrpDeptNames;
        this.yyDeptNames = yyDeptNames;
    }

    public String getBudgetDeptName() {
        return budgetDeptName;
    }

    public void setBudgetDeptName(String budgetDeptName) {
        this.budgetDeptName = budgetDeptName;
    }

    public List<String> getHrpDeptNames() {
        return hrpDeptNames;
    }

    public void setHrpDeptNames(List<String> hrpDeptNames) {
        this.hrpDeptNames = hrpDeptNames;
    }

    public List<String> getYyDeptNames() {
        return yyDeptNames;
    }

    public void setYyDeptNames(List<String> yyDeptNames) {
        this.yyDeptNames = yyDeptNames;
    }
} 