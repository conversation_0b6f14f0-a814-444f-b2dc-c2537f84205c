package com.jp.med.bms.modules.bmsExecute.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetAssetDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetAssetVo;

import java.util.List;

/**
 * 医疗设备购置预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:19:45
 */
public interface BmsBudgetAssetReadService extends IService<BmsBudgetAssetDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsBudgetAssetVo> queryList(BmsBudgetAssetDto dto);
}

