package com.jp.med.bms.modules.bmsExecute.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetEngineDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetEngineVo;

import java.util.List;

/**
 * 信息化建设项目预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:28:01
 */
public interface BmsBudgetEngineReadService extends IService<BmsBudgetEngineDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsBudgetEngineVo> queryList(BmsBudgetEngineDto dto);
}

