package com.jp.med.bms.modules.bmsExecute.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetEquipmentDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetEquipmentVo;

import java.util.List;

/**
 * 通用设备预算
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 14:06:20
 */
public interface BmsBudgetEquipmentReadService extends IService<BmsBudgetEquipmentDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsBudgetEquipmentVo> queryList(BmsBudgetEquipmentDto dto);
}

