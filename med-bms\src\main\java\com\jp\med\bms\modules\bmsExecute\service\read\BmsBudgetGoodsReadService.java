package com.jp.med.bms.modules.bmsExecute.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetGoodsDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetGoodsVo;

import java.util.List;

/**
 * 库房物资采购预算
 * <AUTHOR>
 * @email -
 * @date 2023-11-16 11:22:49
 */
public interface BmsBudgetGoodsReadService extends IService<BmsBudgetGoodsDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsBudgetGoodsVo> queryList(BmsBudgetGoodsDto dto);
}

