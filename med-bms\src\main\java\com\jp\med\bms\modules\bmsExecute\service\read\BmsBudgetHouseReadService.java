package com.jp.med.bms.modules.bmsExecute.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetHouseDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetHouseVo;

import java.util.List;

/**
 * 工程类政府采购预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:34:43
 */
public interface BmsBudgetHouseReadService extends IService<BmsBudgetHouseDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsBudgetHouseVo> queryList(BmsBudgetHouseDto dto);
}

