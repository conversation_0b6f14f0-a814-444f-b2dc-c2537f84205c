package com.jp.med.bms.modules.bmsExecute.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetKeyDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetKeyVo;

import java.util.List;

/**
 * 重点学专科预算表
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:15:52
 */
public interface BmsBudgetKeyReadService extends IService<BmsBudgetKeyDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsBudgetKeyVo> queryList(BmsBudgetKeyDto dto);
}

