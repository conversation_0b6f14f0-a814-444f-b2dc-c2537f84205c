package com.jp.med.bms.modules.bmsExecute.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetServiceDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetServiceVo;

import java.util.List;

/**
 * 服务类采购预算
 * <AUTHOR>
 * @email -
 * @date 2023-11-16 11:03:01
 */
public interface BmsBudgetServiceReadService extends IService<BmsBudgetServiceDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsBudgetServiceVo> queryList(BmsBudgetServiceDto dto);
}

