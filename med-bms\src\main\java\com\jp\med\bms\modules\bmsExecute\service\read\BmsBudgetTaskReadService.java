package com.jp.med.bms.modules.bmsExecute.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetTaskDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetTaskVo;

import java.util.List;

/**
 * 预算任务表
 * <AUTHOR>
 * @email -
 * @date 2023-10-19 17:18:28
 */
public interface BmsBudgetTaskReadService extends IService<BmsBudgetTaskDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsBudgetTaskVo> queryList(BmsBudgetTaskDto dto);

    /**
     * 查询任务流程的详情
     * @param dto
     * @return
     */
    List<BmsBudgetTaskVo> queryTaskFlowDetail(BmsBudgetTaskDto dto);

}

