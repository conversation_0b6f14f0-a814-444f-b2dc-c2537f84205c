package com.jp.med.bms.modules.bmsExecute.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetWorkloadDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetWorkloadVo;

import java.util.List;
import java.util.Map;

/**
 * 预算项目工作量情况
 * <AUTHOR>
 * @email -
 * @date 2024-07-27 15:16:25
 */
public interface BmsBudgetWorkloadReadService extends IService<BmsBudgetWorkloadDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsBudgetWorkloadVo> queryList(BmsBudgetWorkloadDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<BmsBudgetWorkloadVo> queryPageList(BmsBudgetWorkloadDto dto);

    /**
     * 查询业务工作量执行情况
     * @param dto
     * @return
     */
     Map<String,Object> querySRReport(BmsBudgetWorkloadDto dto);

    Map<String,Object> queryZCLReport(BmsBudgetWorkloadDto dto);

    List<BmsBudgetWorkloadVo> queryEconCodeDetail(BmsBudgetWorkloadDto dto);

    List<BmsBudgetWorkloadVo> queryActigCodeDetail(BmsBudgetWorkloadDto dto);

    List<BmsBudgetWorkloadVo> queryZCLReportDetail(BmsBudgetWorkloadDto dto);
}

