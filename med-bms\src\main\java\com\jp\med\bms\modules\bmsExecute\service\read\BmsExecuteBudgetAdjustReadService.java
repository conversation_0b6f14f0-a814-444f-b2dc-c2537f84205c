package com.jp.med.bms.modules.bmsExecute.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetAdjustDto;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetApplyDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetAdjustVo;

import java.util.List;
import java.util.Map;

/**
 * 预算调整表
 * <AUTHOR>
 * @email -
 * @date 2023-06-01 18:20:31
 */
public interface BmsExecuteBudgetAdjustReadService extends IService<BmsExecuteBudgetAdjustDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsExecuteBudgetAdjustVo> queryList(BmsExecuteBudgetAdjustDto dto);

    /**
     * 查询预算信息
     * @param dto
     * @return
     */
    List<BmsExecuteBudgetAdjustVo> queryBudget(BmsExecuteBudgetAdjustDto dto);

    /**
     * 查询预算信息
     * @param dto
     * @return
     */
    List<BmsExecuteBudgetAdjustVo> queryBudgetNew(BmsExecuteBudgetAdjustDto dto);

    Map<String,Object> queryListNew(BmsExecuteBudgetApplyDto dto);
}

