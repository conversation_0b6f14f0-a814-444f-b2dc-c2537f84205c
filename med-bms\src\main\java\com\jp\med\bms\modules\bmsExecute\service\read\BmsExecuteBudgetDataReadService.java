package com.jp.med.bms.modules.bmsExecute.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetDataDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetDataVo;

import java.util.List;
import java.util.Map;

/**
 * 预算编制数据
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-26 15:40:51
 */
public interface BmsExecuteBudgetDataReadService extends IService<BmsExecuteBudgetDataDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    Map<String, Object> queryList(BmsExecuteBudgetDataDto dto);

    /**
     * 查询最终编制结果
     *
     * @param dto
     * @return
     */
    Map<String, Object> queryData(BmsExecuteBudgetDataDto dto);

    /**
     * 查询预算汇总
     *
     * @param dto
     * @return
     */
    Map<String, Object> queryBudgetSummary(BmsExecuteBudgetDataDto dto);

    /**
     * 页面初始化
     *
     * @param dto
     * @return
     */
    Map<String, Object> queryPageInit(BmsExecuteBudgetDataDto dto);


    /**
     * 查询编制项目排序规则
     *
     * @param dto
     * @return
     */
    Map<String, Object> queryBudgetOrder(BmsExecuteBudgetDataDto dto);


    /**
     * 文件上传
     *
     * @param dto
     * @return
     */
    List<BmsExecuteBudgetDataVo> upload(BmsExecuteBudgetDataDto dto);

    /**
     * 查询预算数据明细
     *
     * @param dto
     * @return
     */
    List<BmsExecuteBudgetDataVo> queryDataDetail(BmsExecuteBudgetDataDto dto);

    /**
     * 查询预算报表汇总
     *
     * @param dto
     * @return
     */
    Map<String, Object> queryBudgetReportSummary(BmsExecuteBudgetDataDto dto);
}

