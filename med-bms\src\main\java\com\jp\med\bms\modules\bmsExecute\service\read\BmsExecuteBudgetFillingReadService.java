package com.jp.med.bms.modules.bmsExecute.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetFillingDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetFillingVo;

import java.util.List;
import java.util.Map;

/**
 * 预算填报状态表
 * <AUTHOR>
 * @email -
 * @date 2023-04-28 10:35:42
 */
public interface BmsExecuteBudgetFillingReadService extends IService<BmsExecuteBudgetFillingDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsExecuteBudgetFillingVo> queryList(BmsExecuteBudgetFillingDto dto);

    /**
     * 页面初始化查询
     * @param dto
     * @return
     */
    Map<String, Object> initPage(BmsExecuteBudgetFillingDto dto);
}

