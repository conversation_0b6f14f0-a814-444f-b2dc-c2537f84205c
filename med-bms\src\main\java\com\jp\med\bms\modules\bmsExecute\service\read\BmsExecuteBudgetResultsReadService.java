package com.jp.med.bms.modules.bmsExecute.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetDataDto;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetResultsDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetResultsVo;

import java.util.List;
import java.util.Map;

/**
 * 预算结果
 * <AUTHOR>
 * @email -
 * @date 2023-06-01 10:11:35
 */
public interface BmsExecuteBudgetResultsReadService extends IService<BmsExecuteBudgetResultsDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsExecuteBudgetResultsVo> queryList(BmsExecuteBudgetResultsDto dto);

    /**
     * 查询科室预算
     * @param dto
     * @return
     */
    List<BmsExecuteBudgetResultsVo> queryDeptBudget(BmsExecuteBudgetResultsDto dto);

    /**
     * 查询预算报表推进进度
     * @param dto
     * @return
     */
    Map<String,Object> queryBudgetReport(BmsExecuteBudgetDataDto dto);

    List<BmsExecuteBudgetResultsVo> queryListNew(BmsExecuteBudgetResultsDto dto);

    /**
     * 查询进修经费(JXJF)剩余预算
     * @param dto 查询参数
     * @return 进修经费剩余预算列表
     */
    List<BmsExecuteBudgetResultsVo> queryJXJFRemainingBudget(BmsExecuteBudgetResultsDto dto);
}

