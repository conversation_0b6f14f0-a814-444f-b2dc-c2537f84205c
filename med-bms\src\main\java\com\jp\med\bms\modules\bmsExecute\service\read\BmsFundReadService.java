package com.jp.med.bms.modules.bmsExecute.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsFundDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsFundVo;

import java.util.List;

/**
 * 科研教学经费预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 19:57:30
 */
public interface BmsFundReadService extends IService<BmsFundDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsFundVo> queryList(BmsFundDto dto);
}

