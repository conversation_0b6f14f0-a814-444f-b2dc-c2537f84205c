package com.jp.med.bms.modules.bmsExecute.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsBudgetAssetReadMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetAssetDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetAssetVo;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsBudgetAssetReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class BmsBudgetAssetReadServiceImpl extends ServiceImpl<BmsBudgetAssetReadMapper, BmsBudgetAssetDto> implements BmsBudgetAssetReadService {

    @Autowired
    private BmsBudgetAssetReadMapper bmsBudgetAssetReadMapper;

    @Override
    public List<BmsBudgetAssetVo> queryList(BmsBudgetAssetDto dto) {
        return bmsBudgetAssetReadMapper.queryList(dto);
    }

}
