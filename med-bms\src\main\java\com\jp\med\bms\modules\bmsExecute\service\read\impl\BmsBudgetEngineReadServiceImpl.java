package com.jp.med.bms.modules.bmsExecute.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsBudgetEngineReadMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetEngineDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetEngineVo;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsBudgetEngineReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class BmsBudgetEngineReadServiceImpl extends ServiceImpl<BmsBudgetEngineReadMapper, BmsBudgetEngineDto> implements BmsBudgetEngineReadService {

    @Autowired
    private BmsBudgetEngineReadMapper bmsBudgetEngineReadMapper;

    @Override
    public List<BmsBudgetEngineVo> queryList(BmsBudgetEngineDto dto) {
        return bmsBudgetEngineReadMapper.queryList(dto);
    }

}
