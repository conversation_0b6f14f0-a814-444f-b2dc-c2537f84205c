package com.jp.med.bms.modules.bmsExecute.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsBudgetEquipmentReadMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetEquipmentDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetEquipmentVo;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsBudgetEquipmentReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class BmsBudgetEquipmentReadServiceImpl extends ServiceImpl<BmsBudgetEquipmentReadMapper, BmsBudgetEquipmentDto> implements BmsBudgetEquipmentReadService {

    @Autowired
    private BmsBudgetEquipmentReadMapper bmsBudgetEquipmentReadMapper;

    @Override
    public List<BmsBudgetEquipmentVo> queryList(BmsBudgetEquipmentDto dto) {
        return bmsBudgetEquipmentReadMapper.queryList(dto);
    }

}
