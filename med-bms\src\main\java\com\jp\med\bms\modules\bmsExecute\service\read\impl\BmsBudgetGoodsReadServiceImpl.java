package com.jp.med.bms.modules.bmsExecute.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsBudgetGoodsReadMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetGoodsDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetGoodsVo;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsBudgetGoodsReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class BmsBudgetGoodsReadServiceImpl extends ServiceImpl<BmsBudgetGoodsReadMapper, BmsBudgetGoodsDto> implements BmsBudgetGoodsReadService {

    @Autowired
    private BmsBudgetGoodsReadMapper bmsBudgetGoodsReadMapper;

    @Override
    public List<BmsBudgetGoodsVo> queryList(BmsBudgetGoodsDto dto) {
        return bmsBudgetGoodsReadMapper.queryList(dto);
    }

}
