package com.jp.med.bms.modules.bmsExecute.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsBudgetHouseReadMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetHouseDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetHouseVo;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsBudgetHouseReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class BmsBudgetHouseReadServiceImpl extends ServiceImpl<BmsBudgetHouseReadMapper, BmsBudgetHouseDto> implements BmsBudgetHouseReadService {

    @Autowired
    private BmsBudgetHouseReadMapper bmsBudgetHouseReadMapper;

    @Override
    public List<BmsBudgetHouseVo> queryList(BmsBudgetHouseDto dto) {
        return bmsBudgetHouseReadMapper.queryList(dto);
    }

}
