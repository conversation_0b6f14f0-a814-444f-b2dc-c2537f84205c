package com.jp.med.bms.modules.bmsExecute.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsBudgetKeyReadMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetKeyDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetKeyVo;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsBudgetKeyReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class BmsBudgetKeyReadServiceImpl extends ServiceImpl<BmsBudgetKeyReadMapper, BmsBudgetKeyDto> implements BmsBudgetKeyReadService {

    @Autowired
    private BmsBudgetKeyReadMapper bmsBudgetKeyReadMapper;

    @Override
    public List<BmsBudgetKeyVo> queryList(BmsBudgetKeyDto dto) {
        return bmsBudgetKeyReadMapper.queryList(dto);
    }

}
