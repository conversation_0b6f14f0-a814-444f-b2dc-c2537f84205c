package com.jp.med.bms.modules.bmsExecute.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsBudgetServiceReadMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetServiceDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetServiceVo;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsBudgetServiceReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class BmsBudgetServiceReadServiceImpl extends ServiceImpl<BmsBudgetServiceReadMapper, BmsBudgetServiceDto> implements BmsBudgetServiceReadService {

    @Autowired
    private BmsBudgetServiceReadMapper bmsBudgetServiceReadMapper;

    @Override
    public List<BmsBudgetServiceVo> queryList(BmsBudgetServiceDto dto) {
        return bmsBudgetServiceReadMapper.queryList(dto);
    }

}
