package com.jp.med.bms.modules.bmsExecute.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsBudgetTaskReadMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetTaskDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetTaskVo;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsBudgetTaskReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class BmsBudgetTaskReadServiceImpl extends ServiceImpl<BmsBudgetTaskReadMapper, BmsBudgetTaskDto> implements BmsBudgetTaskReadService {

    @Autowired
    private BmsBudgetTaskReadMapper bmsBudgetTaskReadMapper;

    @Override
    public List<BmsBudgetTaskVo> queryList(BmsBudgetTaskDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return bmsBudgetTaskReadMapper.queryList(dto);
    }

    @Override
    public List<BmsBudgetTaskVo> queryTaskFlowDetail(BmsBudgetTaskDto dto) {
        return bmsBudgetTaskReadMapper.queryTaskFlowDetail(dto);
    }


}
