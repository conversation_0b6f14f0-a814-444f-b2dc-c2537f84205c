package com.jp.med.bms.modules.bmsExecute.service.read.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.bms.constant.BmsConst;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetTaskDto;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetWorkloadDto;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetResultsDto;
import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsBudgetTaskReadMapper;
import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsBudgetWorkloadReadMapper;
import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsExecuteBudgetResultsReadMapper;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsBudgetWorkloadReadService;
import com.jp.med.bms.modules.bmsExecute.util.DeptMappingUtil;
import com.jp.med.bms.modules.bmsExecute.vo.*;
import com.jp.med.bms.modules.dispose.vo.TitleVo;
import com.jp.med.bms.util.StringUtil;
import com.jp.med.bms.util.ValidateUtil;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.emp.HrmOrgAgencyMapDto;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.HrmOrgMapFeignService;
import com.jp.med.common.util.HttpRequestUtil;
import com.jp.med.common.vo.HrmOrgAgencyMapVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class BmsBudgetWorkloadReadServiceImpl extends ServiceImpl<BmsBudgetWorkloadReadMapper, BmsBudgetWorkloadDto> implements BmsBudgetWorkloadReadService {

    @Autowired
    private BmsBudgetWorkloadReadMapper bmsBudgetWorkloadReadMapper;

    @Autowired
    private BmsBudgetTaskReadMapper bmsBudgetTaskReadMapper;

    @Autowired
    private BmsExecuteBudgetResultsReadMapper bmsExecuteBudgetResultsReadMapper;

    @Autowired
    private HrmOrgMapFeignService hrmOrgMapFeignService;

    @Value("${urls.mid.yy.query-budget-actual}")
    private String yyBudgetActualUrl;

    // 从前端移植的常量定义
    private static final Map<String, String> BUDGET_CODES = new HashMap<String, String>() {{
        put("PXF", BmsConst.BudgetCode.PXF);           // 培训费
        put("CLF", BmsConst.BudgetCode.CLF);           // 差旅费
        put("PXF_ZN", BmsConst.BudgetCode.PXF_ZN);     // 培训费(职能)
        put("XSHYDQPX", BmsConst.BudgetCode.XSHYDQPX); // 学术会议
        put("JXJF", BmsConst.BudgetCode.JXJF);         // 进修经费
        put("YYZLXRWPXJF", BmsConst.BudgetCode.YYZLXRWPXJF); // 医院指令性任务
        put("JXYXJYXMJF", BmsConst.BudgetCode.JXYXJYXMJF);   // 继续教育
        put("KYXMPXJF", BmsConst.BudgetCode.KYXMPXJF);       // 科研项目
    }};

    private static final Map<String, String> DEPT_TYPES = new HashMap<String, String>() {{
        put("FUNCTIONAL", BmsConst.DeptType.FUNCTIONAL);  // 职能
        put("CLINICAL", BmsConst.DeptType.CLINICAL);     // 临床
    }};

    private static final List<String> TRAINING_ITEMS = Arrays.asList(
            BmsConst.BudgetCode.XSHYDQPX, 
            BmsConst.BudgetCode.JXJF, 
            BmsConst.BudgetCode.YYZLXRWPXJF, 
            BmsConst.BudgetCode.JXYXJYXMJF, 
            BmsConst.BudgetCode.KYXMPXJF);

    @Override
    public List<BmsBudgetWorkloadVo> queryList(BmsBudgetWorkloadDto dto) {
        return bmsBudgetWorkloadReadMapper.queryList(dto);
    }

    @Override
    public List<BmsBudgetWorkloadVo> queryPageList(BmsBudgetWorkloadDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return bmsBudgetWorkloadReadMapper.queryList(dto);
    }

    @Override
    public Map<String, Object> querySRReport(BmsBudgetWorkloadDto dto) {
        if (StringUtils.isEmpty(dto.getBudgetTaskCode())) {
            throw new AppException("预算年度不能为空");
        }
        dto.setSqlAutowiredHospitalCondition(true);
        //查询表头数据
        List<TitleVo> ratioTitles = new ArrayList<>();
        //过滤门诊、住院四项指标 todo

        List<String> targetColumn = Arrays.asList("MZRCKZFY", "CYRC", "CJFY", "MZRC");

        String MZRCKZFY = "MZRCKZFY";
        String CJFY = "CJFY";

        List<String> targetColumnPersons = Arrays.asList("CYRC", "MZRC");
        List<String> targetColumnAvg = Arrays.asList("MZRCKZFY", "CJFY");

        List<TitleVo> titles = bmsBudgetWorkloadReadMapper.queryTtile(dto);

        titles.stream().forEach(item -> {
            if (targetColumn.contains(item.getKey())) {
                ratioTitles.add(item);
                item.setTitleExplain(item.getTitle() + "当月实际发生数");
                TitleVo yearAmtTitle = new TitleVo();
                yearAmtTitle.setTitle(item.getTitle() + "年度总额");
                yearAmtTitle.setKey(item.getKey() + "_yearAmt");
                yearAmtTitle.setSummary(false);
                yearAmtTitle.setSorter("default");
                yearAmtTitle.setWidth("150");
                yearAmtTitle.setTitleExplain(item.getTitle() + "当年累计总额");
                ratioTitles.add(yearAmtTitle);
                TitleVo amtRatio = new TitleVo();
                amtRatio.setTitle(item.getTitle() + "年度执行比例");
                amtRatio.setKey(item.getKey() + "_amtRatio");
                amtRatio.setSummary(false);
                amtRatio.setSorter("default");
                amtRatio.setWidth("150");
                amtRatio.setTitleExplain(item.getTitle() + "当年执行比例");
                ratioTitles.add(amtRatio);
                TitleVo budgetAmt = new TitleVo();
                budgetAmt.setTitle(item.getTitle() + "年度预算总额");
                budgetAmt.setKey(item.getKey() + "_budgetAmt");
                budgetAmt.setSummary(false);
                budgetAmt.setSorter("default");
                budgetAmt.setWidth("150");
                budgetAmt.setTitleExplain(item.getTitle() + "年度预算总额");
                ratioTitles.add(budgetAmt);
            }
        });
        //添加科室和科室名称表头
        TitleVo deptTitle = new TitleVo();
        deptTitle.setTitle("科室");
        deptTitle.setKey("orgId");
        deptTitle.setSummary(false);
        deptTitle.setSorter("default");
        deptTitle.setOrder(-1);
        deptTitle.setWidth("150");
        deptTitle.setFixed("left");
        ratioTitles.add(0, deptTitle);
        TitleVo deptNameTitle = new TitleVo();
        deptNameTitle.setTitle("科室名称");
        deptNameTitle.setKey("orgName");
        deptNameTitle.setSummary(false);
        deptNameTitle.setSorter("default");
        deptNameTitle.setOrder(0);
        deptNameTitle.setWidth("150");
        deptNameTitle.setFixed("left");
        ratioTitles.add(1, deptNameTitle);

        //查询业务量指标数据(月度值)
        List<BmsBudgetWorkloadVo> vos = bmsBudgetWorkloadReadMapper.queryList(dto);
        //查询业务量指标数据(累计值)
        List<BmsBudgetSRVo> bmsBudgetSRVos = bmsBudgetWorkloadReadMapper.queryMonthCountList(dto);
        Map<String, BmsBudgetSRVo> bmsBudgetSRVoMap = bmsBudgetSRVos.stream().collect(Collectors.toMap(BmsBudgetSRVo::getDeptCode, Function.identity()));

        //部门 业务指标行数据
        List<Map<String, String>> resulRows = new ArrayList<>();
        //按部门进行分组
        Map<String, List<BmsBudgetWorkloadVo>> collect = vos.stream().collect(Collectors.groupingBy(BmsBudgetWorkloadVo::getDeptCode));
        BigDecimal avgFee = new BigDecimal(0);
        String budgetCode = "";
        BmsBudgetSRVo currBmsBudgetSRVo;
        for (Map.Entry<String, List<BmsBudgetWorkloadVo>> entry : collect.entrySet()) {
            String key = entry.getKey();
            currBmsBudgetSRVo = bmsBudgetSRVoMap.get(key);

            List<BmsBudgetWorkloadVo> rows = entry.getValue();
            Map<String, String> row = new HashMap<>();
            row.put("orgId", rows.get(0).getDeptCode());
            row.put("orgName", rows.get(0).getDeptName());

            //获取数据、总费用/均次
            BigDecimal finalAvgFee = avgFee;
            BmsBudgetSRVo finalCurrBmsBudgetSRVo = currBmsBudgetSRVo;
            Class<?> clazz;
            if (!ValidateUtil.isEmpty(currBmsBudgetSRVo)) {
                //为空则表示未上传对应科室数据、获取Class对象
                clazz = finalCurrBmsBudgetSRVo.getClass();
            } else {
                clazz = null;
            }
            //循环获取均费
            BmsBudgetSRVo monthBmsBudgetSRVo = new BmsBudgetSRVo();
            Method invoFunction;
            for (BmsBudgetWorkloadVo budgetWorkloadVo : rows) {
                try {
                    if (ValidateUtil.isEmpty(clazz) || ValidateUtil.isEmpty(budgetWorkloadVo.getActualAmt())) {
                        continue;
                    }
                    invoFunction = clazz.getDeclaredMethod("set" + StringUtil.toCamelCase(budgetWorkloadVo.getBudgetCode()), String.class);
                    invoFunction.setAccessible(true);
                    invoFunction.invoke(monthBmsBudgetSRVo, budgetWorkloadVo.getActualAmt().toString());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
            //遍历每个值
            rows.stream().forEach(item -> {
                try {
                    if (!ValidateUtil.isEmpty(clazz) && !ValidateUtil.isEmpty(item.getActualAmt())) {
                        Method yearInvoFunction = clazz.getDeclaredMethod("get" + StringUtil.toCamelCase(item.getBudgetCode()));
                        Object obj = yearInvoFunction.invoke(finalCurrBmsBudgetSRVo);
                        // BigDecimal itemVal = new BigDecimal(.toString());
                        if (targetColumnAvg.contains(item.getBudgetCode())) {
                            //判断是否
                            if (item.getBudgetCode().equals(MZRCKZFY)) {
                                row.put(item.getBudgetCode(), new BigDecimal(monthBmsBudgetSRVo.getMzrckzfy()).divide(new BigDecimal(monthBmsBudgetSRVo.getMzrc()), 2, BigDecimal.ROUND_HALF_UP).toString());
                            } else if (item.getBudgetCode().equals(CJFY)) {
                                row.put(item.getBudgetCode(), new BigDecimal(monthBmsBudgetSRVo.getCjfy()).divide(new BigDecimal(monthBmsBudgetSRVo.getCyrc()), 2, BigDecimal.ROUND_HALF_UP).toString());
                            }
                        } else {
                            row.put(item.getBudgetCode(), item.getActualAmt().toString());
                        }
                        row.put(item.getBudgetCode() + "_yearAmt", obj.toString());
                        BigDecimal ratio = new BigDecimal(0);
                        if (!ValidateUtil.isEmpty(item.getBudgetAmt()) && item.getBudgetAmt().compareTo(new BigDecimal(0)) > 0) {
                            ratio = new BigDecimal(obj.toString()).divide(item.getBudgetAmt(), 4, BigDecimal.ROUND_HALF_UP);
                        }
                        row.put(item.getBudgetCode() + "_amtRatio", String.format("%.2f", ratio.floatValue() * 100) + "%");
                    }
                    //预算都可以直接赋值
                    row.put(item.getBudgetCode() + "_budgetAmt", item.getBudgetAmt().toString());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
            resulRows.add(row);
        }
        //结果按科室排序
        Collections.sort(resulRows, ((o1, o2) -> {
            String pre = o1.get("orgId");
            String suf = o2.get("orgId");
            return pre.compareTo(suf);
        }));

        Map<String, Object> data = new HashMap<>();
        data.put("data", resulRows);
        data.put("title", ratioTitles);

        return data;
    }

    @Override
    public Map<String, Object> queryZCLReport(BmsBudgetWorkloadDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        //通过taskcode查询task获取年度
        LambdaQueryWrapper<BmsBudgetTaskDto> taskQueryWrapper = Wrappers.lambdaQuery();
        taskQueryWrapper.eq(BmsBudgetTaskDto::getBudgetTaskCode, dto.getBudgetTaskCode());
        BmsBudgetTaskDto bmsBudgetTaskDto = bmsBudgetTaskReadMapper.selectOne(taskQueryWrapper);
        if (Objects.isNull(bmsBudgetTaskDto)) {
            throw new AppException("当前年度任务不存在！");
        }
        YearMonth startMonth = YearMonth.parse(dto.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM"));
        YearMonth endMonth = YearMonth.parse(dto.getEndDate(), DateTimeFormatter.ofPattern("yyyy-MM"));
        if (startMonth.getYear() != endMonth.getYear()) {
            throw new AppException("不能选择跨年的日期区间！");
        }
        if (startMonth.isAfter(endMonth)) {
            throw new AppException("日期区间选择错误，请重新选择");
        }
        //设置查询日期区间
        dto.setStartDate(startMonth.atDay(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        dto.setEndDate(endMonth.atEndOfMonth().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        dto.setYear(bmsBudgetTaskDto.getYear());
        Map<String, Object> resultMap = new HashMap<>();

        try {
            int year = Integer.parseInt(bmsBudgetTaskDto.getYear());

            if (year > 2025) {
                // 2025年以后，从HRP中查询数据
                processBudgetDataAfter2025(dto, resultMap);
            } else if (year == 2025) {
                // 2025年的处理逻辑
                processBudgetDataFor2025(dto, resultMap);
            } else {
                // 2025年之前的处理逻辑
                processBudgetDataBefore2025(dto, resultMap);
            }

            // 在原有的逻辑基础上，添加使用前端移植逻辑处理数据
            Map<String, Object> processedData = processTableData(resultMap, null, dto.getYear());

            // 添加原始数据到结果中，保持向后兼容
            resultMap.putAll(processedData);

            return resultMap;
        } catch (Exception e) {
            log.error("处理支出工作量预算执行情况数据失败", e);
            throw new AppException("处理支出工作量预算执行情况数据失败: " + e.getMessage());
        }
    }

    /**
     * 处理2025年后的预算数据
     *
     * @param dto       预算工作量DTO
     * @param resultMap 结果映射
     */
    private void processBudgetDataAfter2025(BmsBudgetWorkloadDto dto, Map<String, Object> resultMap) {
        // 查询差旅费、培训费各类型的预算值
        BmsExecuteBudgetResultsDto param = new BmsExecuteBudgetResultsDto();
        param.setBudgetTaskCode(dto.getBudgetTaskCode());
        param.setBudgetCodes(Arrays.asList("XSHYDQPX", "JXJF", "JXYXJYXMJF", "PXF_ZN", "CLF"));
        List<BmsExecuteBudgetResultsVo> econBudgetDetails = bmsExecuteBudgetResultsReadMapper.queryBudgetResList(param);

        // 将不同的培训费类型归一
        List<BmsExecuteBudgetResultsVo> budgetRes = processBudgetCategories(econBudgetDetails);
        resultMap.put("econBudgetDetails", budgetRes);

        // 查询科目-预算映射表(包括会计科目和经济科目)
        List<BmsReimItemToBudgCfgVo> reimToBudgetVos = bmsBudgetWorkloadReadMapper.queryReimItemToBudget(dto);
        List<String> filterBudgetCodes = new ArrayList<>();
        // 处理预算代码
        List<String> actigBudgetCodes = extractBudgetCodes(reimToBudgetVos, MedConst.TYPE_1, filterBudgetCodes);
        List<String> econBudgetCodes = extractBudgetCodes(reimToBudgetVos, MedConst.TYPE_2, filterBudgetCodes);
        econBudgetCodes.removeIf(code -> code.equals("CLF") || code.equals("PXF"));

        // 查询所有预算项汇总
        List<BmsExecuteBudgetResultsVo> bmsExecuteBudgetResultsVos = bmsExecuteBudgetResultsReadMapper.queryBudgetSummary(dto);

        // 处理会计科目和经济科目的预算汇总
        List<BmsBudgetSummaryVo> actigBudgetSummary = processSubjectBudgetSummary(bmsExecuteBudgetResultsVos, actigBudgetCodes, MedConst.TYPE_1);
        List<BmsBudgetSummaryVo> econBudgetSummary = processSubjectBudgetSummary(bmsExecuteBudgetResultsVos, econBudgetCodes, MedConst.TYPE_2);

        // 查询差旅费、培训费实际值明细数据
        List<BmsBudgetWorkloadVo> extraEconWorkloadDtos = queryExtraEconWorkloadData(dto);
        resultMap.put("econActualDetails", extraEconWorkloadDtos);

        // 提取科目代码
        List<String> actigCodes = extractReimItemCodes(reimToBudgetVos, MedConst.TYPE_1);
        List<String> econCodes = extractReimItemCodes(reimToBudgetVos, MedConst.TYPE_2);

        // 查询会计科目和经济科目实际发生值
        List<BmsBudgetWorkloadVo> actigWorkloadDtos = queryActualSummaryData(dto, actigCodes, MedConst.TYPE_1);
        List<BmsBudgetWorkloadVo> econWorkloadDtos = queryActualSummaryData(dto, econCodes, MedConst.TYPE_2);

        // 添加组织机构数据
        addOrganizationData(resultMap);

        // 计算汇总数据
        List<BmsBudgetSummaryVo> summarys = getSummarys(actigBudgetSummary, econBudgetSummary, actigWorkloadDtos, econWorkloadDtos, reimToBudgetVos);
        resultMap.put("summaryItems", summarys);
    }

    /**
     * 处理2025年的预算数据
     *
     * @param dto       预算工作量DTO
     * @param resultMap 结果映射
     */
    private void processBudgetDataFor2025(BmsBudgetWorkloadDto dto, Map<String, Object> resultMap) {
        // 细项 差旅费、培训费(职能)、学术会议、进修经费、医院指令性任务、继续教育、科研项目
        List<String> items = Arrays.asList("CLF", "PXF_ZN", "XSHYDQPX", "JXJF", "YYZLXRWPXJF", "JXYXJYXMJF", "KYXMPXJF");
        BmsExecuteBudgetResultsDto param = new BmsExecuteBudgetResultsDto();
        param.setBudgetTaskCode(dto.getBudgetTaskCode());
        param.setHospitalId("zjxrmyy");
        param.setBudgetCodes(items);
        List<BmsExecuteBudgetResultsVo> econBudgetDetails = bmsExecuteBudgetResultsReadMapper.queryListNew(param);

        // 重新计算各个科室预算数据
        List<BmsExecuteBudgetResultsVo> econBudgetDetailsResult = calculateDeptBudget(econBudgetDetails);
        resultMap.put("econBudgetDetails", econBudgetDetailsResult);

        // 细项明细
        List<BmsBudgetWorkloadVo> extraEconWorkloadDtos = new ArrayList<>();

        // 查询科目-预算映射表(包括会计科目和经济科目)
        List<BmsReimItemToBudgCfgVo> reimToBudgetVos = bmsBudgetWorkloadReadMapper.queryReimItemToBudget(dto);

        // 处理预算代码,同时过滤差旅和培训预算
        List<String> actigBudgetCodes = extractBudgetCodes(reimToBudgetVos, MedConst.TYPE_1, items);
        List<String> econBudgetCodes = extractBudgetCodes(reimToBudgetVos, MedConst.TYPE_2, items);


        List<String> actigCodes = extractReimItemCodes(reimToBudgetVos, MedConst.TYPE_1);
        List<String> reimItemCodes = extractReimItemCodes(reimToBudgetVos, MedConst.TYPE_2);

        // 细项对比时，不对比CLF和培训费
        reimItemCodes.removeIf(code -> code.equals(BmsConst.ECON_SHOW_DETAILS_TRAVEL) || code.equals(BmsConst.ECON_SHOW_DETAILS_TRAINING));

        // 查询所有预算项汇总
        List<BmsExecuteBudgetResultsVo> bmsExecuteBudgetResultsVos = bmsExecuteBudgetResultsReadMapper.queryBudgetSummary(dto);

        // 处理会计科目和经济科目的预算汇总
        List<BmsBudgetSummaryVo> actigBudgetSummary = processSubjectBudgetSummary(bmsExecuteBudgetResultsVos, actigBudgetCodes, MedConst.TYPE_1);
        List<BmsBudgetSummaryVo> econBudgetSummary = processSubjectBudgetSummary(bmsExecuteBudgetResultsVos, econBudgetCodes, MedConst.TYPE_2);

        // 查询汇总实际发生值
        dto.setActigCodeStr(actigCodes);
        dto.setEconCodeStr(reimItemCodes);
        JSONObject jsonObject = yyBudgetActual(dto, BmsConst.YY_BUDGET_TOTAL_INFO);

        // 会计科目实际发生值
        JSONArray actigBudgetResult = jsonObject.getJSONArray("actigBudgetResult");
        List<BmsBudgetWorkloadVo> actigWorkloadDtos = JSON.parseArray(actigBudgetResult.toJSONString(), BmsBudgetWorkloadVo.class);

        // 经济科目实际发生值
        JSONArray econBudgetResult = jsonObject.getJSONArray("econBudgetResult");
        List<BmsBudgetWorkloadVo> econWorkloadDtos = JSON.parseArray(econBudgetResult.toJSONString(), BmsBudgetWorkloadVo.class);

        // 获取差旅和培训对应的科室实际使用明细
        List<BmsBudgetWorkloadVo> bmsBudgetWorkloadVoClfList = getYyBudgetActualClfAndPxfDetail(dto, MedConst.TYPE_2, Arrays.asList(BmsConst.ECON_SHOW_DETAILS_TRAVEL), BmsConst.YY_BUDGET_ECON_DETAIL_INFO);
        List<BmsBudgetWorkloadVo> bmsBudgetWorkloadVoPxfList = getYyBudgetActualClfAndPxfDetail(dto, MedConst.TYPE_2, Arrays.asList(BmsConst.ECON_SHOW_DETAILS_TRAINING), BmsConst.YY_BUDGET_ECON_DETAIL_INFO);
        extraEconWorkloadDtos.addAll(bmsBudgetWorkloadVoClfList);
        extraEconWorkloadDtos.addAll(bmsBudgetWorkloadVoPxfList);
        resultMap.put("econActualDetails", extraEconWorkloadDtos);

        // 添加组织机构数据
        addOrganizationData(resultMap);

        // 细项对比时，不对比CLF和培训费
        econBudgetCodes.removeIf(code -> code.equals("CLF") || code.equals("PXF"));

        // 计算汇总数据
        List<BmsBudgetSummaryVo> summarys = getSummarys(actigBudgetSummary, econBudgetSummary, actigWorkloadDtos, econWorkloadDtos, reimToBudgetVos);
        resultMap.put("summaryItems", summarys);
    }

    /**
     * 查询指定经济科目或者会计科目的明细
     *
     * @param econCodeStr
     * @param subCodeType
     * @param operationType
     * @return
     */
    private List<BmsBudgetWorkloadVo> getYyBudgetActualClfAndPxfDetail(BmsBudgetWorkloadDto dto, String subCodeType, List<String> econCodeStr, String operationType) {
        // 获取经济科目下的
        dto.setType(subCodeType);
        dto.setActigCodeStr(null);
        dto.setEconCodeStr(econCodeStr);
        dto.setActigSubCode(null);
        dto.setEconSubCode(null);
        //从hrp 查询数据
        JSONObject jsonObject = yyBudgetActual(dto, operationType);
        JSONArray econDetails = jsonObject.getJSONArray("econDetails");
        List<BmsBudgetWorkloadVo> vos = JSON.parseArray(econDetails.toJSONString(), BmsBudgetWorkloadVo.class);
        return vos.stream().map(e -> {
            BmsBudgetWorkloadVo vo = new BmsBudgetWorkloadVo();
            vo.setDeptCode(e.getDeptCode());
            vo.setDeptName(e.getDeptName());
            vo.setReimItemCode(StringUtils.equals(dto.getType(), MedConst.TYPE_1) ? e.getActigSubCode() : e.getEconSubCode());
            vo.setReimItemName(StringUtils.equals(dto.getType(), MedConst.TYPE_2) ? e.getActigSubName() : e.getEconSubName());
            vo.setActualAmt(e.getActualAmt());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 处理2025年之前的预算数据
     *
     * @param dto       预算工作量DTO
     * @param resultMap 结果映射
     */
    private void processBudgetDataBefore2025(BmsBudgetWorkloadDto dto, Map<String, Object> resultMap) {
        // 查询差旅费、培训费各科室年度预算
        List<BmsExecuteBudgetResultsVo> econBudgetDetails = getEconBudgetDetails();
        resultMap.put("econBudgetDetails", econBudgetDetails);

        // 查询会计科目项目汇总
        List<BmsBudgetSummaryVo> actigBudgetSummary = getItemBudgetSummary(MedConst.TYPE_1);

        // 查询经济科目项目汇总
        List<BmsBudgetSummaryVo> econBudgetSummary = getItemBudgetSummary(MedConst.TYPE_2);

        // 查询科目-预算映射表
        List<BmsReimItemToBudgCfgVo> reimToBudgetVos = bmsBudgetWorkloadReadMapper.queryReimItemToBudget(dto);
        List<String> actigCodes = extractReimItemCodes(reimToBudgetVos, MedConst.TYPE_1);
        List<String> reimItemCodes = extractReimItemCodes(reimToBudgetVos, MedConst.TYPE_2);

        dto.setActigCodeStr(actigCodes);
        dto.setEconCodeStr(reimItemCodes);

        // 查询实际发生值
        JSONObject jsonObject = yyBudgetActual(dto, BmsConst.YY_BUDGET_TOTAL_INFO);

        // 差旅费、培训费实际值明细数据
        JSONArray extraEconDetails = jsonObject.getJSONArray("extraEconDetails");
        List<BmsBudgetWorkloadVo> extraEconWorkloadDtos = JSON.parseArray(extraEconDetails.toJSONString(), BmsBudgetWorkloadVo.class);
        resultMap.put("econActualDetails", extraEconWorkloadDtos);

        // 会计科目实际发生值
        JSONArray actigBudgetResult = jsonObject.getJSONArray("actigBudgetResult");
        List<BmsBudgetWorkloadVo> actigWorkloadDtos = JSON.parseArray(actigBudgetResult.toJSONString(), BmsBudgetWorkloadVo.class);

        // 经济科目实际发生值
        JSONArray econBudgetResult = jsonObject.getJSONArray("econBudgetResult");
        List<BmsBudgetWorkloadVo> econWorkloadDtos = JSON.parseArray(econBudgetResult.toJSONString(), BmsBudgetWorkloadVo.class);

        // 添加组织机构数据
        addOrganizationData(resultMap);

        // 计算汇总数据
        List<BmsBudgetSummaryVo> summarys = getSummarys(actigBudgetSummary, econBudgetSummary, actigWorkloadDtos, econWorkloadDtos, reimToBudgetVos);
        resultMap.put("summaryItems", summarys);
    }

    /**
     * 计算部门预算
     *
     * @param econBudgetDetails 经济预算明细
     * @return 处理后的预算结果
     */
    private List<BmsExecuteBudgetResultsVo> calculateDeptBudget(List<BmsExecuteBudgetResultsVo> econBudgetDetails) {
        // 按预算类型分类
        List<BmsExecuteBudgetResultsVo> econBudgetDetailsClf = econBudgetDetails.stream()
                .filter(e -> ValidateUtil.isNotEmpty(e.getBudgetParentId()) &&
                        e.getBudgetParentId().startsWith(BmsConst.BUDGET_BASE_CODE_CLF))
                .collect(Collectors.toList());

        List<BmsExecuteBudgetResultsVo> econBudgetDetailsPxf = econBudgetDetails.stream()
                .filter(e -> ValidateUtil.isNotEmpty(e.getBudgetParentId()) &&
                        e.getBudgetParentId().startsWith(BmsConst.BUDGET_BASE_CODE_PXF))
                .collect(Collectors.toList());

        // 计算各个科室差旅预算之和
        Map<String, BigDecimal> budgetItemClfMap = econBudgetDetailsClf.stream().collect(
                Collectors.groupingBy(
                        BmsExecuteBudgetResultsVo::getOrgName,
                        Collectors.reducing(BigDecimal.ZERO, BmsExecuteBudgetResultsVo::getBudgetAmount, BigDecimal::add)
                )
        );

        // 计算各个科室培训预算之和
        Map<String, BigDecimal> budgetItemPxfMap = econBudgetDetailsPxf.stream().collect(
                Collectors.groupingBy(
                        BmsExecuteBudgetResultsVo::getOrgName,
                        Collectors.reducing(BigDecimal.ZERO, BmsExecuteBudgetResultsVo::getBudgetAmount, BigDecimal::add)
                )
        );

        List<BmsExecuteBudgetResultsVo> result = new ArrayList<>();

        // 创建差旅费预算集
        for (Map.Entry<String, BigDecimal> entry : budgetItemClfMap.entrySet()) {
            BmsExecuteBudgetResultsVo itemResultVo = new BmsExecuteBudgetResultsVo();
            itemResultVo.setOrgName(entry.getKey());
            itemResultVo.setBudgetCode(BmsConst.BUDGET_BASE_CODE_CLF);
            itemResultVo.setBudgetAmount(entry.getValue());
            result.add(itemResultVo);
        }

        // 创建培训费用预算集
        for (Map.Entry<String, BigDecimal> entry : budgetItemPxfMap.entrySet()) {
            BmsExecuteBudgetResultsVo itemResultVo = new BmsExecuteBudgetResultsVo();
            itemResultVo.setOrgName(entry.getKey());
            itemResultVo.setBudgetCode(BmsConst.BUDGET_BASE_CODE_PXF);
            itemResultVo.setBudgetAmount(entry.getValue());
            result.add(itemResultVo);
        }

        return result;
    }

    /**
     * 处理预算类别，将培训费类型归一
     *
     * @param econBudgetDetails 经济预算明细
     * @return 处理后的预算明细
     */
    private List<BmsExecuteBudgetResultsVo> processBudgetCategories(List<BmsExecuteBudgetResultsVo> econBudgetDetails) {
        List<BmsExecuteBudgetResultsVo> budgetRes = new ArrayList<>();
        Map<String, List<BmsExecuteBudgetResultsVo>> collect = econBudgetDetails.stream()
                .collect(Collectors.groupingBy(BmsExecuteBudgetResultsVo::getOrgId));

        collect.forEach((key, value) -> {
            // budgetcode为CLF分为一组，其他所有类型分为一组(作为培训费)
            Map<String, List<BmsExecuteBudgetResultsVo>> collect2 = value.stream()
                    .collect(Collectors.groupingBy(item ->
                            StringUtils.equals(item.getBudgetCode(), "CLF") ? "CLF" : "PXF"
                    ));

            collect2.forEach((key2, value2) -> {
                if (StringUtils.equals(key2, "CLF")) {
                    budgetRes.addAll(value2);
                } else {
                    // 计算总金额作为培训费的预算金额
                    BigDecimal totalAmt = value2.stream()
                            .map(BmsExecuteBudgetResultsVo::getBudgetAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BmsExecuteBudgetResultsVo pxItem = new BmsExecuteBudgetResultsVo();
                    pxItem.setBudgetCode("PXF");
                    pxItem.setBudgetAmount(totalAmt);
                    pxItem.setOrgId(value2.get(0).getOrgId());
                    pxItem.setOrgName(value2.get(0).getOrgName());
                    pxItem.setBudgetTaskCode(value2.get(0).getBudgetTaskCode());
                    pxItem.setCentralizedDept(value2.get(0).getCentralizedDept());
                    budgetRes.add(pxItem);
                }
            });
        });

        return budgetRes;
    }

    /**
     * 提取预算代码
     *
     * @param reimToBudgetVos   科目-预算映射
     * @param type              类型
     * @param filterBudgetCodes 需要过滤掉的预算编码
     * @return 预算代码列表
     */
    private List<String> extractBudgetCodes(List<BmsReimItemToBudgCfgVo> reimToBudgetVos, String type, List<String> filterBudgetCodes) {
        return reimToBudgetVos.stream()
                .filter(e -> StringUtils.equals(e.getType(), type))
                .filter(e -> !filterBudgetCodes.contains(e.getBudgetCode()))
                .map(BmsReimItemToBudgCfgVo::getBudgetCode)
                .collect(Collectors.toList());
    }

    /**
     * 提取科目代码
     *
     * @param reimToBudgetVos 科目-预算映射
     * @param type            类型
     * @return 科目代码列表
     */
    private List<String> extractReimItemCodes(List<BmsReimItemToBudgCfgVo> reimToBudgetVos, String type) {
        return reimToBudgetVos.stream()
                .filter(e -> StringUtils.equals(e.getType(), type))
                .map(BmsReimItemToBudgCfgVo::getReimItemCode)
                .collect(Collectors.toList());
    }

    /**
     * 处理科目预算汇总
     *
     * @param budgetResults 预算结果
     * @param budgetCodes   预算代码
     * @param type          类型
     * @return 预算汇总列表
     */
    private List<BmsBudgetSummaryVo> processSubjectBudgetSummary(
            List<BmsExecuteBudgetResultsVo> budgetResults,
            List<String> budgetCodes,
            String type) {
        return budgetResults.stream()
                .filter(e -> budgetCodes.contains(e.getBudgetCode()))
                .map(e -> {
                    BmsBudgetSummaryVo vo = new BmsBudgetSummaryVo();
                    vo.setLeadDeptName(e.getCentralizedDeptName());
                    vo.setType(type);
                    vo.setBudgetCode(e.getBudgetCode());
                    vo.setBudgetName(e.getBudgetName());
                    vo.setBudgetAmt(e.getActigAmt());
                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 查询额外的经济工作量数据
     *
     * @param dto 预算工作量DTO
     * @return 工作量数据列表
     */
    private List<BmsBudgetWorkloadVo> queryExtraEconWorkloadData(BmsBudgetWorkloadDto dto) {
        dto.setEconCodeStr(Arrays.asList(BmsConst.ECON_SHOW_DETAILS_TRAVEL, BmsConst.ECON_SHOW_DETAILS_TRAINING));
        dto.setType(MedConst.TYPE_2);
        return bmsBudgetWorkloadReadMapper.queryActualDetail(dto);
    }

    /**
     * 查询实际汇总数据
     *
     * @param dto   预算工作量DTO
     * @param codes 代码列表
     * @param type  类型
     * @return 工作量数据列表
     */
    private List<BmsBudgetWorkloadVo> queryActualSummaryData(BmsBudgetWorkloadDto dto, List<String> codes, String type) {
        if (MedConst.TYPE_1.equals(type)) {
            dto.setActigCodeStr(codes);
        } else {
            dto.setEconCodeStr(codes);
        }
        dto.setType(type);
        return bmsBudgetWorkloadReadMapper.queryActualSummary(dto);
    }

    /**
     * 添加组织机构数据
     *
     * @param resultMap 结果映射
     */
    private void addOrganizationData(Map<String, Object> resultMap) {
        String[] orgNames = getStandardOrgNames();
        resultMap.put("orgNames", orgNames);
        resultMap.put("orgNamesHrpMap", budgetDeptMapHRPDept());
        resultMap.put("orgNamesYyMap", budgetDeptMapYYDept());
    }

    /**
     * 获取标准的组织机构名称列表
     *
     * @return 组织机构名称数组
     */
    private String[] getStandardOrgNames() {
        return DeptMappingUtil.getAllBudgetDeptNames();
    }

    /**
     * 预算科室对应用友科室映射，仅名称
     *
     * @return
     */
    private Map<String, List<String>> budgetDeptMapYYDept() {
        return DeptMappingUtil.getBudgetToYyDeptMap();
    }

    /**
     * hrp预算科室对应科室映射，仅名称
     *
     * @return
     */
    private Map<String, List<String>> budgetDeptMapHRPDept() {
        return DeptMappingUtil.getBudgetToHrpDeptMap();
    }

    @Override
    public List<BmsBudgetWorkloadVo> queryZCLReportDetail(BmsBudgetWorkloadDto dto) {
        LambdaQueryWrapper<BmsBudgetTaskDto> taskQueryWrapper = Wrappers.lambdaQuery();
        taskQueryWrapper.eq(BmsBudgetTaskDto::getBudgetTaskCode, dto.getBudgetTaskCode());
        BmsBudgetTaskDto bmsBudgetTaskDto = bmsBudgetTaskReadMapper.selectOne(taskQueryWrapper);
        if (Objects.isNull(bmsBudgetTaskDto)) {
            throw new AppException("当前年度任务不存在！");
        }
        YearMonth startMonth = YearMonth.parse(dto.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM"));
        YearMonth endMonth = YearMonth.parse(dto.getEndDate(), DateTimeFormatter.ofPattern("yyyy-MM"));
        if (startMonth.getYear() != endMonth.getYear()) {
            throw new AppException("不能选择跨年的日期区间！");
        }
        if (startMonth.isAfter(endMonth)) {
            throw new AppException("日期区间选择错误，请重新选择");
        }
        //设置查询日期区间
        dto.setStartDate(startMonth.atDay(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        dto.setEndDate(endMonth.atEndOfMonth().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        dto.setYear(bmsBudgetTaskDto.getYear());

        if (!StringUtils.isEmpty(dto.getDeptCode())) {
            //如果部门查询不为空，则转换为yy部门，进行查询
            //查询科室映射表
            CommonResult<List<HrmOrgAgencyMapVo>> result = hrmOrgMapFeignService.noPageList(new HrmOrgAgencyMapDto());
            if (Objects.isNull(result) || CollectionUtil.isEmpty(result.getData())) {
                throw new AppException("当前未维护科室顸映射");
            }
            Optional<HrmOrgAgencyMapVo> first = result.getData().stream().filter(e -> StringUtils.equals(e.getHrpOrgCode(), dto.getDeptCode())).findFirst();
            if (first.isEmpty()) {
                throw new AppException("不存在当前科室对应映射");
            }
            //设置为用友科室code
            dto.setDeptCode(first.get().getYyOrgCode());
        }

        //查询  科目-预算项映射表
        List<BmsReimItemToBudgCfgVo> bmsReimItemToBudgCfgVos = bmsBudgetWorkloadReadMapper.queryReimItemToBudget(dto);
        List<String> itemCodes = bmsReimItemToBudgCfgVos.stream().filter(e -> StringUtils.equals(e.getBudgetCode(), dto.getBudgetCode())).map(e -> e.getReimItemCode()).collect(Collectors.toList());
        dto.setSqlAutowiredHospitalCondition(true);
        if (Integer.parseInt(bmsBudgetTaskDto.getYear()) > 2024) {
            if (StringUtils.equals(dto.getType(), MedConst.TYPE_1)) {
                dto.setActigCodeStr(itemCodes);
            } else {
                dto.setEconCodeStr(itemCodes);
            }
            //从hrp 查询数据
            List<BmsBudgetWorkloadVo> vos = bmsBudgetWorkloadReadMapper.queryActualDetail(dto);
            return vos.stream().map(e -> {
                BmsBudgetWorkloadVo vo = new BmsBudgetWorkloadVo();
                vo.setDeptCode(e.getDeptCode());
                vo.setDeptName(e.getDeptName());
                vo.setReimItemCode(StringUtils.equals(dto.getType(), MedConst.TYPE_1) ? e.getActigSubCode() : e.getEconSubCode());
                vo.setReimItemName(StringUtils.equals(dto.getType(), MedConst.TYPE_2) ? e.getActigSubName() : e.getEconSubName());
                vo.setActualAmt(e.getActualAmt());
                return vo;
            }).collect(Collectors.toList());
        }

        if (StringUtils.equals(dto.getType(), MedConst.TYPE_1)) {
            //会计科目
            dto.setActigCodeStr(itemCodes);
            JSONObject jsonObject = yyBudgetActual(dto, BmsConst.YY_BUDGET_ACTIG_DETAIL_INFO);
            JSONArray actigDetails = jsonObject.getJSONArray("actigDetails");
            List<BmsBudgetWorkloadVo> vos = JSON.parseArray(actigDetails.toJSONString(), BmsBudgetWorkloadVo.class);
            return vos.stream().map(e -> {
                BmsBudgetWorkloadVo vo = new BmsBudgetWorkloadVo();
                vo.setDeptCode(e.getDeptCode());
                vo.setDeptName(e.getDeptName());
                vo.setReimItemCode(e.getActigSubCode());
                vo.setReimItemName(e.getActigSubName());
                vo.setActualAmt(e.getActualAmt());
                return vo;
            }).collect(Collectors.toList());
        }
        //经济科目
        dto.setEconCodeStr(itemCodes);
        JSONObject jsonObject = yyBudgetActual(dto, BmsConst.YY_BUDGET_ECON_DETAIL_INFO);
        JSONArray econDetails = jsonObject.getJSONArray("econDetails");
        List<BmsBudgetWorkloadVo> vos = JSON.parseArray(econDetails.toJSONString(), BmsBudgetWorkloadVo.class);
        return vos.stream().map(e -> {
            BmsBudgetWorkloadVo vo = new BmsBudgetWorkloadVo();
            vo.setDeptCode(e.getDeptCode());
            vo.setDeptName(e.getDeptName());
            vo.setReimItemCode(e.getEconSubCode());
            vo.setReimItemName(e.getEconSubName());
            vo.setActualAmt(e.getActualAmt());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<BmsBudgetWorkloadVo> queryEconCodeDetail(BmsBudgetWorkloadDto dto) {
        //通过taskcode查询task获取年度
        LambdaQueryWrapper<BmsBudgetTaskDto> taskQueryWrapper = Wrappers.lambdaQuery();
        taskQueryWrapper.eq(BmsBudgetTaskDto::getBudgetTaskCode, dto.getBudgetTaskCode());
        BmsBudgetTaskDto bmsBudgetTaskDto = bmsBudgetTaskReadMapper.selectOne(taskQueryWrapper);
        if (Objects.isNull(bmsBudgetTaskDto)) {
            throw new AppException("当前年度任务不存在！");
        }
        dto.setYear(bmsBudgetTaskDto.getYear());
        if (Integer.parseInt(bmsBudgetTaskDto.getYear()) > 2024) {
            //todo 2025年开始，从hrp中查询数据
            return new ArrayList<>();
        } else {
            //查询某经济科目明细
            JSONObject jsonObject = yyBudgetActual(dto, BmsConst.YY_BUDGET_ECON_DETAIL_INFO);
            JSONArray econDetails = jsonObject.getJSONArray("econDetails");
            return JSON.parseArray(econDetails.toJSONString(), BmsBudgetWorkloadVo.class);
        }
    }

    @Override
    public List<BmsBudgetWorkloadVo> queryActigCodeDetail(BmsBudgetWorkloadDto dto) {
        //通过taskcode查询task获取年度
        LambdaQueryWrapper<BmsBudgetTaskDto> taskQueryWrapper = Wrappers.lambdaQuery();
        taskQueryWrapper.eq(BmsBudgetTaskDto::getBudgetTaskCode, dto.getBudgetTaskCode());
        BmsBudgetTaskDto bmsBudgetTaskDto = bmsBudgetTaskReadMapper.selectOne(taskQueryWrapper);
        if (Objects.isNull(bmsBudgetTaskDto)) {
            throw new AppException("当前年度任务不存在！");
        }
        dto.setYear(bmsBudgetTaskDto.getYear());
        if (Integer.parseInt(bmsBudgetTaskDto.getYear()) > 2024) {
            //todo 2025年开始，从hrp中查询数据
            return new ArrayList<>();
        } else {
            //查询某会计科目明细
            JSONObject jsonObject = yyBudgetActual(dto, BmsConst.YY_BUDGET_ACTIG_DETAIL_INFO);
            JSONArray actigDetails = jsonObject.getJSONArray("actigDetails");
            return JSON.parseArray(actigDetails.toJSONString(), BmsBudgetWorkloadVo.class);
        }
    }

    private JSONObject yyBudgetActual(BmsBudgetWorkloadDto dto, String operationType) {

        Map<String, Object> params = new HashMap<>();
        params.put("year", dto.getYear());
        params.put("startDate", dto.getStartDate());
        params.put("endDate", dto.getEndDate());
        if (!Objects.isNull(dto.getEconCodeStr())) {
            params.put("econCodeStr", dto.getEconCodeStr());
        }
        if (!Objects.isNull(dto.getActigCodeStr())) {
            params.put("actigCodeStr", dto.getActigCodeStr());
        }
        params.put("deptCode", dto.getDeptCode());
        params.put("econSubCode", dto.getEconSubCode());
        params.put("actigSubCode", dto.getActigSubCode());
        params.put("yyBudgetInfoType", operationType);

        JSONObject jsonObject = queryYyBudgetActualInfo(yyBudgetActualUrl, params);
        return jsonObject;
    }

    /**
     * 查询用友会计科目、经济科目实际发生值
     *
     * @param url
     * @param params
     * @return
     */
    private JSONObject queryYyBudgetActualInfo(String url, Map<String, Object> params) {
        String result = HttpRequestUtil.post(params, url);
        result = result.substring(1, result.length() - 1).replaceAll("\\\\", "");
        JSONObject jsonObject = JSON.parseObject(result);
        Integer code = jsonObject.getInteger("code");
        String msg = jsonObject.getString("message");
        if (code != 200) {
            log.error("获取用友实际发生值失败:" + msg);
            throw new AppException("获取用友实际发生值失败: " + msg);
        }
        return jsonObject.getJSONObject("data");
    }

    /**
     * 查询 经济科目(差旅费、培训费) 明细 预算值
     *
     * @return
     */
    private List<BmsExecuteBudgetResultsVo> getEconBudgetDetails() {
        String[] orgCodes = new String[]{};
        String[] orgNames = new String[]{"口腔科", "急诊科", "健康体检科", "便民门诊", "门诊部", "肛肠科", "发热门诊", "全科医疗科", "呼吸与危重症医学科", "消化内科", "神经内科", "心血管内科",
                "肾内、内分泌科", "老年病科", "大内、大外科", "胃肠外科", "肝胆胰外科", "神经外科", "骨科", "泌尿外科", "胸外科", "妇产科", "儿科", "眼科", "耳鼻咽喉头颈外科", "皮肤科", "感染性疾病科",
                "肿瘤科、安宁疗护科", "康复医学科", "重症医学科", "疼痛科", "中医科", "病理科", "检验科", "输血科", "放射科", "药剂科", "超声科", "手术室", "消毒供应室", "病案科", "医院办公室", "党委办公室",
                "宣传科", "审计科", "人力资源部", "运管办", "医务部", "护理部", "医院感染管理科", "医院质量管理办公室", "科教科", "医疗保险与价格管理科", "财务科", "保卫科", "信息科", "工会",
                "总务科", "医学装备与资产管理科", "采管科", "服务部", "临床药学科", "血防科", "预防保健科", "艾滋病治疗管理办公室", "评审评价办", "医院领导"};
        String[] pxfAmt = {"12000",
                "40000", "20000", "0", "23000", "0", "0", "10000", "50000", "80000", "30000", "100000", "50000", "25000", "10000", "20000", "15000", "40000", "100000", "30000",
                "20000", "35000", "30000", "20000", "20000", "20000", "10000", "25000", "50000", "50000", "2000", "8000", "10000", "50000", "10000", "25000", "100000", "18000",
                "20000", "8000", "35000", "65000", "40000", "30000", "15000", "25000", "25000", "60000", "50000", "50000", "30000", "300000", "10000", "10000", "2000",
                "10000", "2000", "25000", "25000", "18000", "5000", "30000", "2000", "15000", "8000", "15000","0"};
        String[] clfAmt = {"0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0",
                "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0",
                "5000", "15000", "15000", "5000", "15000", "30000", "8000", "15000", "15000", "15000", "15000", "20000", "10000", "5000", "5000", "8000",
                "0", "50000", "0", "15000", "2000", "2000", "0", "5000", "0", "2000","0"};
        String[] budgetCodes = new String[]{"PXF", "CLF"};
        String[] budgetNames = new String[]{"培训费", "差旅费"};
        List<BmsExecuteBudgetResultsVo> result = new ArrayList<>();
        for (int i = 0; i < budgetCodes.length; i++) {
            for (int j = 0; j < orgNames.length; j++) {
                BmsExecuteBudgetResultsVo vo = new BmsExecuteBudgetResultsVo();
                vo.setOrgName(orgNames[j]);
                vo.setBudgetCode(budgetCodes[i]);
                vo.setBudgetName(budgetNames[i]);
                vo.setBudgetAmount(i == 0 ? new BigDecimal(pxfAmt[j]) : new BigDecimal(clfAmt[j]));
                result.add(vo);
            }
        }

        return result;
        //构造明细

        /*return Arrays.stream(BmsYearEconBudgetResult.values())
                .map(e -> {
                    BmsExecuteBudgetResultsVo vo = new BmsExecuteBudgetResultsVo();
                    vo.setOrgId(e.getOrgId());
                    vo.setOrgName(e.getOrgName());
                    vo.setBudgetCode(e.getBudgetCode());
                    vo.setBudgetName(e.getBudgetName());
                    vo.setActigAmt(new BigDecimal(e.getActigAmt()));
                    return vo;
                }).collect(Collectors.toList());*/
    }

    /**
     * 查询 预算项汇总 预算值
     *
     * @return
     */
    private List<BmsBudgetSummaryVo> getItemBudgetSummary(String type) {
        //经济科目-预算 映射
        String[] budgetNames = new String[]{
                "工资支出", "绩效支出", "办公用品", "印刷费", "水费", "电费", "物业管理", "房屋维修费", "租赁费", "总务低耗品", "其他总务材料", "专用燃料费", "车辆保养维护费", "医疗废物处置费",
                "书报杂志费", "公杂费", "邮寄费", "电话费", "公务接待费", "租车费", "会议费", "会员费", "手续费", "过路过桥费", "汽车保险费", "其他商品服务支出-其他", "专用设备维修费", "一般设备维修费",
                "医用低值易耗品", "医用高值材料", "放射材料费", "化验材料费", "党建活动经费", "税金及附加费用", "退休人员医疗费", "奖励金", "债务利息", "坏账准备", "其他支出", "咨询费", "退休人员其他费用",
                "车辆燃料费", "抚恤金", "生活补助", "职工医疗费", "医疗风险金", "退休人员生活补助", "西药", "中草药", "制剂材料", "血费", "劳务费", "委托业务费", "医疗纠纷处置费", "文化建设费", "广告宣传费",
                "福利费", "网络信息系统运行维护费", "其他卫生材料费", "工会经费"
        };
        String[] leadDepts = {"人力资源部", "运管办", "总务科", "总务科", "总务科", "总务科", "总务科", "总务科", "总务科", "总务科", "总务科", "总务科", "总务科", "总务科",
                "院办公室", "院办公室", "院办公室", "院办公室", "院办公室", "院办公室", "院办公室", "院办公室", "财务科", "财务科", "财务科", "财务科", "医学装备与资产管理科", "医学装备与资产管理科",
                "医学装备与资产管理科", "医学装备与资产管理科", "医学装备与资产管理科", "医学装备与资产管理科", "党委办公室", "财务科", "财务科", "财务科", "财务科", "财务科",
                "财务科", "财务科", "财务科", "财务科", "财务科", "财务科", "财务科", "财务科", "财务科", "药剂科", "药剂科", "药剂科", "输血科", "医务部", "医务部",
                "医务部", "宣传科", "宣传科", "预保科", "信息科", "医学装备与资产管理科", "工会"};
        String[] budgetAmt = new String[]{
                "102742950", "190289330", "963800", "983450", "578700", "6200000", "7577400", "2741800", "570000", "987570", "2118200", "330000", "150000", "1500000", "100000", "102600", "3400", "412050",
                "171500", "710000", "30000", "117500", "50000", "38000", "53000", "4178748", "3860310", "1076100", "1700000", "64000000", "2500000", "33800000", "100000", "850000", "8000", "9600", "9000000",
                "6000000", "2400000", "110000", "1500000", "50000", "200592", "150000", "10000", "2000000", "3250000", "112500000", "2400000", "2000000", "4000000", "886000", "11855200", "125000", "165000",
                "329200", "220000", "6500000", "42000000", "5000000"
        };
        String[] budgetCodes = new String[]{
                "GZZC", "JXZC", "BGYP", "YSF", "SF", "DF", "WYGL", "FWWXF", "ZLF", "ZWDHP", "QTZWCL", "ZYRLF", "CLBYWHF", "YLFWCZF", "SBZZF", "GZF", "YJF", "DHF", "GWJDF", "ZCF",
                "HYF", "HYF1", "SXF", "GLGQF", "QCBXF", "QTSPFWZC_QT", "ZYSBWXF", "YBSBWXF", "YYDZYHP", "YYGZCL", "FSCLF", "HYCLF", "DJHDJF", "SJJFJFY", "TXRYYLF", "JLJ", "ZWLX",
                "HZZB", "QTZC", "ZXF", "TXRYQTFY", "CLRLF", "FXJ", "SHBZ", "ZGYLF", "YLFXJ", "TXRYSHBZ", "XY", "ZCY", "ZJCLF", "XF", "LWF", "WTYWF", "YLJFCLF", "WHJSF", "GGXCF",
                "FLF", "WLXXXTYHWHF", "QTWSCLF", "GHJF"
        };

        //会计-预算映射
        String[] actigNames = new String[]{"无形资产摊销费", "固定资产折旧费", "预算准备金", "财政项目支出"};
        String[] actigLeadDepts = {"医学装备与资产管理科", "医学装备与资产管理科", "财务科", "财务科"};
        String[] actigAmt = new String[]{"2000000", "46700000", "0", "475000000"};
        String[] actigBudgetCodes = new String[]{"WXZCTXF", "GDZCZJF", "YSZBJ", "CZXMZC"};

        List<BmsBudgetSummaryVo> result = new ArrayList<>();
        if (StringUtils.equals(type, MedConst.TYPE_1)) {
            //返回会计预算值
            for (int i = 0; i < actigBudgetCodes.length; i++) {
                BmsBudgetSummaryVo vo = new BmsBudgetSummaryVo();
                vo.setLeadDeptName(actigLeadDepts[i]);
                vo.setType(MedConst.TYPE_1);
                vo.setBudgetCode(actigBudgetCodes[i]);
                vo.setBudgetName(actigNames[i]);
                vo.setBudgetAmt(new BigDecimal(actigAmt[i]));
                result.add(vo);
            }
        } else {
            //返回经济科目预算值
            for (int i = 0; i < budgetCodes.length; i++) {
                BmsBudgetSummaryVo vo = new BmsBudgetSummaryVo();
                vo.setLeadDeptName(leadDepts[i]);
                vo.setType(MedConst.TYPE_2);
                vo.setBudgetCode(budgetCodes[i]);
                vo.setBudgetName(budgetNames[i]);
                vo.setBudgetAmt(new BigDecimal(budgetAmt[i]));
                result.add(vo);
            }
        }

        return result;
    }

    /**
     * @param actigBudgetSummary  会计科目预算汇总
     * @param econBudgetSummary   经济科目预算汇总
     * @param actigWorkloadDtos   会计科目实际发生值
     * @param econWorkloadDtos    经济科目实际发生值
     * @param reimItemToBudgetMap 科目-预算 映射关系  （多个科目可能对应一个预算）
     * @return
     */
    private List<BmsBudgetSummaryVo> getSummarys(List<BmsBudgetSummaryVo> actigBudgetSummary,
                                                 List<BmsBudgetSummaryVo> econBudgetSummary,
                                                 List<BmsBudgetWorkloadVo> actigWorkloadDtos,
                                                 List<BmsBudgetWorkloadVo> econWorkloadDtos,
                                                 List<BmsReimItemToBudgCfgVo> reimItemToBudgetMap) {
        //合并会计科目汇总
        List<BmsBudgetSummaryVo> summaryVos = new ArrayList<>();
        //查询 科目-预算项映射表
//        List<BmsReimItemToBudgCfgVo> reimItemToBudgetMap = bmsBudgetWorkloadReadMapper.queryReimItemToBudget(dto);
        //4.1筛选会计科目映射值
        List<BmsReimItemToBudgCfgVo> actigItemMap = reimItemToBudgetMap.stream().filter(e -> StringUtils.equals(e.getType(), MedConst.TYPE_1)).collect(Collectors.toList());
        Map<String, List<BmsReimItemToBudgCfgVo>> actigItemMapGroup = actigItemMap.stream().collect(Collectors.groupingBy(BmsReimItemToBudgCfgVo::getBudgetCode));
        Map<String, List<BmsBudgetWorkloadVo>> actigActualGroup = actigWorkloadDtos.stream().collect(Collectors.groupingBy(BmsBudgetWorkloadVo::getActigSubCode));
        actigBudgetSummary.stream().forEach(e -> {
            BmsBudgetSummaryVo vo = new BmsBudgetSummaryVo();
            vo.setLeadDeptName(e.getLeadDeptName());
            vo.setType(e.getType());
            vo.setBudgetCode(e.getBudgetCode());
            vo.setBudgetName(e.getBudgetName());
            vo.setBudgetAmt(e.getBudgetAmt());
            //获取此预算项对应的所有会计科目
            List<BmsReimItemToBudgCfgVo> bmsReimItemToBudgCfgVos = actigItemMapGroup.get(e.getBudgetCode());
            //计算所有会计科目的总和
            BigDecimal totalAmt = BigDecimal.ZERO;
            if (!Objects.isNull(bmsReimItemToBudgCfgVos)) {
                if (bmsReimItemToBudgCfgVos.size() > 1) {
                    //是否是多个项的总和
                    vo.setIsSummary(MedConst.TYPE_1);
                }
                for (int i = 0; i < bmsReimItemToBudgCfgVos.size(); i++) {
                    //当前会计科目总和
                    List<BmsBudgetWorkloadVo> bmsBudgetWorkloadVos = actigActualGroup.get(bmsReimItemToBudgCfgVos.get(i).getReimItemCode());
                    if (!Objects.isNull(bmsBudgetWorkloadVos)) {
                        BigDecimal reduce = bmsBudgetWorkloadVos
                                .stream()
                                .map(e2 -> e2.getActualAmt())
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        totalAmt = totalAmt.add(reduce);
                    }
                }
            }
            vo.setActualAmt(totalAmt);
            summaryVos.add(vo);
        });

        //4.2 筛选经济科目映射值
        List<BmsReimItemToBudgCfgVo> econItemMap = reimItemToBudgetMap.stream().filter(e -> StringUtils.equals(e.getType(), MedConst.TYPE_2)).collect(Collectors.toList());
        Map<String, List<BmsReimItemToBudgCfgVo>> econItemMapGroup = econItemMap.stream().collect(Collectors.groupingBy(BmsReimItemToBudgCfgVo::getBudgetCode));
        Map<String, List<BmsBudgetWorkloadVo>> econActualGroup = econWorkloadDtos.stream().collect(Collectors.groupingBy(BmsBudgetWorkloadVo::getEconSubCode));
        econBudgetSummary.stream().forEach(e -> {
            BmsBudgetSummaryVo vo = new BmsBudgetSummaryVo();
            vo.setLeadDeptName(e.getLeadDeptName());
            vo.setType(e.getType());
            vo.setBudgetCode(e.getBudgetCode());
            vo.setBudgetName(e.getBudgetName());
            vo.setBudgetAmt(e.getBudgetAmt());
            //获取此预算项对应的所有经济科目
            List<BmsReimItemToBudgCfgVo> bmsReimItemToBudgCfgVos = econItemMapGroup.get(e.getBudgetCode());
            //计算所有经济科目的总和
            BigDecimal totalAmt = BigDecimal.ZERO;
            if (!Objects.isNull(bmsReimItemToBudgCfgVos)) {
                if (bmsReimItemToBudgCfgVos.size() > 1) {
                    //是否是多个项的总和
                    vo.setIsSummary(MedConst.TYPE_1);
                }
                for (int i = 0; i < bmsReimItemToBudgCfgVos.size(); i++) {
                    //当前经济科目总和
                    List<BmsBudgetWorkloadVo> bmsBudgetWorkloadVos = econActualGroup.get(bmsReimItemToBudgCfgVos.get(i).getReimItemCode());
                    if (!Objects.isNull(bmsBudgetWorkloadVos)) {
                        BigDecimal reduce = bmsBudgetWorkloadVos
                                .stream()
                                .map(e2 -> e2.getActualAmt())
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        totalAmt = totalAmt.add(reduce);
                    }
                }
            }
            vo.setActualAmt(totalAmt);
            summaryVos.add(vo);
        });

        return summaryVos;
    }

    // 从前端移植的辅助方法

    /**
     * 获取年份配置
     *
     * @param year 年份
     * @return 年份配置
     */
    private Map<String, Object> getYearConfig(String year) {
        int yearValue = Integer.parseInt(year);
        String yearRangeType;

        if (yearValue < BmsConst.Year.YEAR_2025) {
            yearRangeType = BmsConst.YearRangeType.BEFORE_2025;
        } else if (yearValue == BmsConst.Year.YEAR_2025) {
            yearRangeType = BmsConst.YearRangeType.YEAR_2025;
        } else {
            yearRangeType = BmsConst.YearRangeType.AFTER_2025;
        }

        Map<String, Object> config = new HashMap<>();
        config.put("yearRangeType", yearRangeType);

        // 兼容原有的代码，也保留is2024字段
        boolean is2024 = "2024".equals(year);
        config.put("is2024", is2024);

        // 对于项目列索引，使用Before_2025的规则
        boolean isBeforeTwoFive = BmsConst.YearRangeType.BEFORE_2025.equals(yearRangeType) || 
                                  BmsConst.YearRangeType.YEAR_2025.equals(yearRangeType);
        config.put("projectColumnIndex", isBeforeTwoFive ? 
                BmsConst.TableColumnKey.PROJECT_COLUMN_INDEX_BEFORE_2025 : 
                BmsConst.TableColumnKey.PROJECT_COLUMN_INDEX_AFTER_2025);
        config.put("rightProjectColumnIndex", isBeforeTwoFive ? 
                BmsConst.TableColumnKey.RIGHT_PROJECT_COLUMN_INDEX_BEFORE_2025 : 
                BmsConst.TableColumnKey.RIGHT_PROJECT_COLUMN_INDEX_AFTER_2025);

        return config;
    }

    /**
     * 计算完成比例
     *
     * @param actual 实际值
     * @param budget 预算值
     * @return 完成比例
     */
    private String calculateCompletionRate(BigDecimal actual, BigDecimal budget) {
        if (budget == null || budget.compareTo(BigDecimal.ZERO) == 0) {
            return "";
        }
        return actual.multiply(new BigDecimal(100)).divide(budget, 2, BigDecimal.ROUND_HALF_UP).toString() + "%";
    }

    /**
     * 过滤预算详情
     *
     * @param details    预算详情
     * @param budgetCode 预算代码
     * @param orgNames   部门名称列表
     * @return 过滤后的预算总额
     */
    private BigDecimal filterBudgetDetails(List<BmsExecuteBudgetResultsVo> details, String budgetCode, List<String> orgNames) {
        if (details == null || details.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return details.stream()
                .filter(a -> budgetCode.equals(a.getBudgetCode()) && orgNames.contains(a.getOrgName()))
                .map(BmsExecuteBudgetResultsVo::getBudgetAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 过滤实际详情
     *
     * @param details   实际详情
     * @param condition 过滤条件
     * @param orgNames  部门名称列表
     * @return 过滤后的实际总额
     */
    private BigDecimal filterActualDetails(List<BmsBudgetWorkloadVo> details, Map<String, Object> condition, List<String> orgNames) {
        if (details == null || details.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return details.stream()
                .filter(a -> {
                    String reimItemCode = (String) condition.get("reimItemCode");
                    Object budgetCode = condition.get("budgetCode");
                    Boolean noBudgetCode = (Boolean) condition.get("noBudgetCode");

                    if (reimItemCode != null) {
                        return reimItemCode.equals(a.getReimItemCode()) && orgNames.contains(a.getDeptName());
                    }

                    if (budgetCode != null) {
                        if (budgetCode instanceof List) {
                            @SuppressWarnings("unchecked")
                            List<String> budgetCodes = (List<String>) budgetCode;
                            return budgetCodes.contains(a.getBudgetCode()) && orgNames.contains(a.getDeptName());
                        } else {
                            return budgetCode.equals(a.getBudgetCode()) && orgNames.contains(a.getDeptName());
                        }
                    }

                    if (Boolean.TRUE.equals(noBudgetCode)) {
                        return a.getBudgetCode() == null && orgNames.contains(a.getDeptName());
                    }

                    return false;
                })
                .map(BmsBudgetWorkloadVo::getActualAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 生成表头
     *
     * @param year 年份
     * @return 表头和列索引
     */
    private Map<String, Object> generateTableHeaders(String year) {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> yearConfig = getYearConfig(year);
        String yearRangeType = (String) yearConfig.get("yearRangeType");
        List<Map<String, Object>> titles = new ArrayList<>();

        // 添加固定的组织名称列
        Map<String, Object> orgNameColumn = new HashMap<>();
        orgNameColumn.put("title", "预算科室名称");
        orgNameColumn.put("width", BmsConst.TableStyle.DEFAULT_WIDTH);
        orgNameColumn.put("order", 0);
        orgNameColumn.put("fixed", BmsConst.TableStyle.FIXED_LEFT);
        orgNameColumn.put("key", BmsConst.TableColumnKey.ORG_NAME);
        titles.add(orgNameColumn);

        int i = 1;

        if (BmsConst.YearRangeType.BEFORE_2025.equals(yearRangeType) || 
            BmsConst.YearRangeType.YEAR_2025.equals(yearRangeType)) {
            // 2025年之前的表头配置
            List<Map<String, String>> headersBefore2025 = new ArrayList<>();
            headersBefore2025.add(createHeaderMap("培训费预算数", "col_" + i++));
            headersBefore2025.add(createHeaderMap("培训费发生值", "col_" + i++));
            headersBefore2025.add(createHeaderMap("培训费完成比例", "col_" + i++));
            headersBefore2025.add(createHeaderMap("差旅费预算数", "col_" + i++));
            headersBefore2025.add(createHeaderMap("差旅费发生值", "col_" + i++));
            headersBefore2025.add(createHeaderMap("差旅费完成比例", "col_" + i++));

            for (Map<String, String> header : headersBefore2025) {
                Map<String, Object> column = new HashMap<>();
                column.put("title", header.get("title"));
                column.put("width", BmsConst.TableStyle.PROJECT_WIDTH);
                column.put("order", 0);
                column.put("key", header.get("key"));
                titles.add(column);
            }
        } else {
            // 2025年及以后的表头配置
            List<Map<String, String>> headersAfter2025 = new ArrayList<>();
            headersAfter2025.add(createHeaderMap("差旅费预算数", "col_" + i++));
            headersAfter2025.add(createHeaderMap("差旅费发生值", "col_" + i++));
            headersAfter2025.add(createHeaderMap("差旅费完成比例", "col_" + i++));
            headersAfter2025.add(createHeaderMap("培训费(职能)预算数", "col_" + i++));
            headersAfter2025.add(createHeaderMap("培训费(职能)发生值", "col_" + i++));
            headersAfter2025.add(createHeaderMap("培训费(职能)完成比例", "col_" + i++));
            headersAfter2025.add(createHeaderMap("培训费(学术会议)预算数", "col_" + i++));
            headersAfter2025.add(createHeaderMap("培训费(学术会议)发生值", "col_" + i++));
            headersAfter2025.add(createHeaderMap("培训费(学术会议)完成比例", "col_" + i++));
            headersAfter2025.add(createHeaderMap("培训费(进修经费)预算数", "col_" + i++));
            headersAfter2025.add(createHeaderMap("培训费(进修经费)发生值", "col_" + i++));
            headersAfter2025.add(createHeaderMap("培训费(进修经费)完成比例", "col_" + i++));
            headersAfter2025.add(createHeaderMap("培训费(医院指令性任务)预算数", "col_" + i++));
            headersAfter2025.add(createHeaderMap("培训费(医院指令性任务)发生值", "col_" + i++));
            headersAfter2025.add(createHeaderMap("培训费(医院指令性任务)完成比例", "col_" + i++));
            headersAfter2025.add(createHeaderMap("培训费(继续教育)预算数", "col_" + i++));
            headersAfter2025.add(createHeaderMap("培训费(继续教育)发生值", "col_" + i++));
            headersAfter2025.add(createHeaderMap("培训费(继续教育)完成比例", "col_" + i++));
            headersAfter2025.add(createHeaderMap("培训费(科研项目)预算数", "col_" + i++));
            headersAfter2025.add(createHeaderMap("培训费(科研项目)发生值", "col_" + i++));
            headersAfter2025.add(createHeaderMap("培训费(科研项目)完成比例", "col_" + i++));

            for (Map<String, String> header : headersAfter2025) {
                Map<String, Object> column = new HashMap<>();
                column.put("title", header.get("title"));
                column.put("width", BmsConst.TableStyle.PROJECT_WIDTH);
                column.put("order", 0);
                column.put("key", header.get("key"));
                titles.add(column);
            }
        }

        result.put("titles", titles);
        result.put("columnIndex", i);

        return result;
    }

    /**
     * 创建表头映射
     *
     * @param title 标题
     * @param key   键
     * @return 表头映射
     */
    private Map<String, String> createHeaderMap(String title, String key) {
        Map<String, String> map = new HashMap<>();
        map.put("title", title);
        map.put("key", key);
        return map;
    }

    /**
     * 添加项目列
     *
     * @param titles      已有表头
     * @param columnIndex 列索引
     * @param year        年份
     * @return 添加项目列后的表头
     */
    private List<Map<String, Object>> addProjectColumns(List<Map<String, Object>> titles, int columnIndex, String year) {
        Map<String, Object> yearConfig = getYearConfig(year);
        String projectColumnIndex = (String) yearConfig.get("projectColumnIndex");
        String rightProjectColumnIndex = (String) yearConfig.get("rightProjectColumnIndex");
        int i = columnIndex;

        // 第一个预算项目列
        Map<String, Object> projectColumn1 = new HashMap<>();
        projectColumn1.put("title", "预算项目");
        projectColumn1.put("width", BmsConst.TableStyle.PROJECT_WIDTH);
        projectColumn1.put("order", 0);
        projectColumn1.put("key", "col_" + i++);
        projectColumn1.put("isProject", true);
        projectColumn1.put("dataIndex", projectColumnIndex);

        // 添加前端渲染器相关信息
        Map<String, Object> renderInfo1 = new HashMap<>();
        renderInfo1.put("type", BmsConst.RenderType.PROJECT);
        renderInfo1.put("dataKey", projectColumnIndex);
        renderInfo1.put("leadDeptKey", BmsConst.TableRenderKey.LEFT_LEAD_DEPT);
        renderInfo1.put("typeKey", BmsConst.TableRenderKey.LEFT_TYPE);
        renderInfo1.put("budgetCodeKey", BmsConst.TableRenderKey.LEFT_BUDGET_CODE);
        projectColumn1.put("renderInfo", renderInfo1);

        titles.add(projectColumn1);

        // 预算数
        Map<String, Object> budgetColumn1 = new HashMap<>();
        budgetColumn1.put("title", "预算数");
        budgetColumn1.put("width", BmsConst.TableStyle.PROJECT_WIDTH);
        budgetColumn1.put("order", 0);
        budgetColumn1.put("key", "col_" + i++);
        titles.add(budgetColumn1);

        // 发生值
        Map<String, Object> actualColumn1 = new HashMap<>();
        actualColumn1.put("title", "发生值");
        actualColumn1.put("width", BmsConst.TableStyle.PROJECT_WIDTH);
        actualColumn1.put("order", 0);
        actualColumn1.put("key", "col_" + i++);
        titles.add(actualColumn1);

        // 完成比例
        Map<String, Object> rateColumn1 = new HashMap<>();
        rateColumn1.put("title", "完成比例");
        rateColumn1.put("width", BmsConst.TableStyle.PROJECT_WIDTH);
        rateColumn1.put("order", 0);
        rateColumn1.put("key", "col_" + i++);
        titles.add(rateColumn1);

        // 第二个预算项目列
        Map<String, Object> projectColumn2 = new HashMap<>();
        projectColumn2.put("title", "预算项目");
        projectColumn2.put("width", BmsConst.TableStyle.PROJECT_WIDTH);
        projectColumn2.put("order", 0);
        projectColumn2.put("key", "col_" + i++);
        projectColumn2.put("isProject", true);
        projectColumn2.put("dataIndex", rightProjectColumnIndex);

        // 添加前端渲染器相关信息
        Map<String, Object> renderInfo2 = new HashMap<>();
        renderInfo2.put("type", BmsConst.RenderType.PROJECT);
        renderInfo2.put("dataKey", rightProjectColumnIndex);
        renderInfo2.put("leadDeptKey", BmsConst.TableRenderKey.RIGHT_LEAD_DEPT);
        renderInfo2.put("typeKey", BmsConst.TableRenderKey.RIGHT_TYPE);
        renderInfo2.put("budgetCodeKey", BmsConst.TableRenderKey.RIGHT_BUDGET_CODE);
        projectColumn2.put("renderInfo", renderInfo2);

        titles.add(projectColumn2);

        // 预算数
        Map<String, Object> budgetColumn2 = new HashMap<>();
        budgetColumn2.put("title", "预算数");
        budgetColumn2.put("width", BmsConst.TableStyle.PROJECT_WIDTH);
        budgetColumn2.put("order", 0);
        budgetColumn2.put("key", "col_" + i++);
        titles.add(budgetColumn2);

        // 发生值
        Map<String, Object> actualColumn2 = new HashMap<>();
        actualColumn2.put("title", "发生值");
        actualColumn2.put("width", BmsConst.TableStyle.PROJECT_WIDTH);
        actualColumn2.put("order", 0);
        actualColumn2.put("key", "col_" + i++);
        titles.add(actualColumn2);

        // 完成比例
        Map<String, Object> rateColumn2 = new HashMap<>();
        rateColumn2.put("title", "完成比例");
        rateColumn2.put("width", BmsConst.TableStyle.PROJECT_WIDTH);
        rateColumn2.put("order", 0);
        rateColumn2.put("key", "col_" + i++);
        titles.add(rateColumn2);

        return titles;
    }

    /**
     * 构建数据行
     *
     * @param yearRangeType     年份范围类型
     * @param orgNames          部门名称列表
     * @param orgNamesHrpMap    部门名称映射[预算-HRP]
     * @param orgNamesYyMap     部门名称映射[预算-用友]
     * @param orgTypes          部门类型
     * @param econBudgetDetails 经济预算详情
     * @param econActualDetails 经济实际详情
     * @param budgetDeptMapping 预算部门映射
     * @return 数据行
     */
    private List<Map<String, Object>> buildDataRows(String yearRangeType, String[] orgNames, Map<String, List<String>> orgNamesHrpMap, Map<String, List<String>> orgNamesYyMap,
                                                    String[] orgTypes, List<BmsExecuteBudgetResultsVo> econBudgetDetails,
                                                    List<BmsBudgetWorkloadVo> econActualDetails, Object budgetDeptMapping) {
        List<Map<String, Object>> rows = new ArrayList<>();

        if (BmsConst.YearRangeType.BEFORE_2025.equals(yearRangeType) || 
            BmsConst.YearRangeType.YEAR_2025.equals(yearRangeType)) {
            List<String> yyDepts;
            List<String> hrpDepts;
            for (String name : orgNames) {
                yyDepts = orgNamesYyMap.get(name);
                if (yyDepts == null) {
                    yyDepts = Collections.emptyList();
                }

                hrpDepts = orgNamesHrpMap.get(name);
                if (yyDepts == null) {
                    hrpDepts = Collections.emptyList();
                }

                // 培训费预算数
                BigDecimal col_1 = filterBudgetDetails(econBudgetDetails, BUDGET_CODES.get("PXF"), hrpDepts);
                // 培训费发生值
                Map<String, Object> condition = new HashMap<>();
                condition.put("reimItemCode", BmsConst.ECON_SHOW_DETAILS_TRAINING);
                BigDecimal col_2 = filterActualDetails(econActualDetails, condition, yyDepts);
                String col_3 = calculateCompletionRate(col_2, col_1);

                // 差旅费预算数
                BigDecimal col_4 = filterBudgetDetails(econBudgetDetails, BUDGET_CODES.get("CLF"), hrpDepts);
                // 差旅费发生值
                condition = new HashMap<>();
                condition.put("reimItemCode", BmsConst.ECON_SHOW_DETAILS_TRAVEL);
                BigDecimal col_5 = filterActualDetails(econActualDetails, condition, yyDepts);
                String col_6 = calculateCompletionRate(col_5, col_4);

                Map<String, Object> row = new HashMap<>();
                row.put(BmsConst.TableColumnKey.ORG_NAME, name);
                row.put("col_1", col_1);
                row.put("col_2", col_2);
                row.put("col_3", col_3);
                row.put("col_4", col_4);
                row.put("col_5", col_5);
                row.put("col_6", col_6);

                rows.add(row);
            }
        } else {
            for (int idx = 0; idx < orgNames.length; idx++) {
                String name = orgNames[idx];

                // 获取部门类型
                String deptType = orgTypes[idx];
                if (budgetDeptMapping != null && budgetDeptMapping instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> mapping = (Map<String, Object>) budgetDeptMapping;
                    if (mapping.containsKey("data")) {
                        @SuppressWarnings("unchecked")
                        List<Map<String, Object>> data = (List<Map<String, Object>>) mapping.get("data");
                        for (Map<String, Object> bdm : data) {
                            if (name.equals(bdm.get("sourceDeptName")) && bdm.get("sourceBgtCode") == null) {
                                deptType = (String) bdm.get("deptType");
                                break;
                            }
                        }
                    }
                }

                List<String> yyDepts = orgNamesYyMap.get(name);
                if (yyDepts == null) {
                    yyDepts = Collections.emptyList();
                }

                List<String> hrpDepts = orgNamesHrpMap.get(name);
                if (yyDepts == null) {
                    hrpDepts = Collections.emptyList();
                }

                boolean isFunctional = DEPT_TYPES.get("FUNCTIONAL").equals(deptType);

                // 差旅费
                BigDecimal col_1 = filterBudgetDetails(econBudgetDetails, BUDGET_CODES.get("CLF"), hrpDepts);
                Map<String, Object> condition = new HashMap<>();
                condition.put("noBudgetCode", true);
                BigDecimal col_2 = filterActualDetails(econActualDetails, condition, yyDepts);
                String col_3 = calculateCompletionRate(col_2, col_1);

                // 培训费(职能)
                BigDecimal col_4 = filterBudgetDetails(econBudgetDetails, BUDGET_CODES.get("PXF_ZN"), hrpDepts);
                BigDecimal col_5 = BigDecimal.ZERO;
                if (isFunctional) {
                    condition = new HashMap<>();
                    condition.put("budgetCode", TRAINING_ITEMS);
                    col_5 = filterActualDetails(econActualDetails, condition, yyDepts);
                }
                String col_6 = calculateCompletionRate(col_5, col_4);

                // 学术会议
                BigDecimal col_7 = filterBudgetDetails(econBudgetDetails, BUDGET_CODES.get("XSHYDQPX"), hrpDepts);
                condition = new HashMap<>();
                condition.put("budgetCode", BUDGET_CODES.get("XSHYDQPX"));
                BigDecimal col_8 = filterActualDetails(econActualDetails, condition, yyDepts);
                if (isFunctional) {
                    col_8 = BigDecimal.ZERO;
                }
                String col_9 = calculateCompletionRate(col_8, col_7);

                // 进修经费
                BigDecimal col_10 = filterBudgetDetails(econBudgetDetails, BUDGET_CODES.get("JXJF"), hrpDepts);
                condition = new HashMap<>();
                condition.put("budgetCode", BUDGET_CODES.get("JXJF"));
                BigDecimal col_11 = filterActualDetails(econActualDetails, condition, yyDepts);
                if (isFunctional) {
                    col_11 = BigDecimal.ZERO;
                }
                String col_12 = calculateCompletionRate(col_11, col_10);

                // 医院指令性任务
                final BigDecimal col_13 = econBudgetDetails.stream()
                        .filter(a -> BUDGET_CODES.get("YYZLXRWPXJF").equals(a.getBudgetCode()))
                        .map(BmsExecuteBudgetResultsVo::getBudgetAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                condition = new HashMap<>();
                condition.put("budgetCode", BUDGET_CODES.get("YYZLXRWPXJF"));
                BigDecimal col_14 = filterActualDetails(econActualDetails, condition, yyDepts);
                if (isFunctional) {
                    col_14 = BigDecimal.ZERO;
                }
                String col_15 = calculateCompletionRate(col_14, col_13);

                // 继续教育
                BigDecimal col_16 = filterBudgetDetails(econBudgetDetails, BUDGET_CODES.get("JXYXJYXMJF"), hrpDepts);
                condition = new HashMap<>();
                condition.put("budgetCode", BUDGET_CODES.get("JXYXJYXMJF"));
                BigDecimal col_17 = filterActualDetails(econActualDetails, condition, yyDepts);
                if (isFunctional) {
                    col_17 = BigDecimal.ZERO;
                }
                String col_18 = calculateCompletionRate(col_17, col_16);

                // 科研项目
                final BigDecimal col_19 = econBudgetDetails.stream()
                        .filter(a -> BUDGET_CODES.get("KYXMPXJF").equals(a.getBudgetCode()))
                        .map(BmsExecuteBudgetResultsVo::getBudgetAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                condition = new HashMap<>();
                condition.put("budgetCode", BUDGET_CODES.get("KYXMPXJF"));
                BigDecimal col_20 = filterActualDetails(econActualDetails, condition, yyDepts);
                if (isFunctional) {
                    col_20 = BigDecimal.ZERO;
                }
                String col_21 = calculateCompletionRate(col_20, col_19);

                Map<String, Object> row = new HashMap<>();
                row.put("orgName", name);
                row.put("col_1", col_1);
                row.put("col_2", col_2);
                row.put("col_3", col_3);
                row.put("col_4", col_4);
                row.put("col_5", col_5);
                row.put("col_6", col_6);
                row.put("col_7", col_7);
                row.put("col_8", col_8);
                row.put("col_9", col_9);
                row.put("col_10", col_10);
                row.put("col_11", col_11);
                row.put("col_12", col_12);
                row.put("col_13", col_13);
                row.put("col_14", col_14);
                row.put("col_15", col_15);
                row.put("col_16", col_16);
                row.put("col_17", col_17);
                row.put("col_18", col_18);
                row.put("col_19", col_19);
                row.put("col_20", col_20);
                row.put("col_21", col_21);

                rows.add(row);
            }
        }

        return rows;
    }

    /**
     * 构建项目行
     *
     * @param yearRangeType 年份范围类型
     * @param summaryItems  汇总项目
     * @return 项目行
     */
    private List<Map<String, Object>> buildItemRows(String yearRangeType, List<BmsBudgetSummaryVo> summaryItems) {
        List<Map<String, Object>> itemRows = new ArrayList<>();
        int rowNum = (int) Math.ceil(summaryItems.size() / 2.0);

        for (int i = 0; i < rowNum; i++) {
            BmsBudgetSummaryVo left = (i * 2 < summaryItems.size()) ? summaryItems.get(i * 2) : null;
            BmsBudgetSummaryVo right = (i * 2 + 1 < summaryItems.size()) ? summaryItems.get(i * 2 + 1) : null;

            Map<String, Object> itemRow = new HashMap<>();

            if (BmsConst.YearRangeType.BEFORE_2025.equals(yearRangeType) || 
                BmsConst.YearRangeType.YEAR_2025.equals(yearRangeType)) {
                itemRow.put(BmsConst.TableColumnKey.PROJECT_COLUMN_INDEX_BEFORE_2025, left != null ? left.getBudgetName() : "");
                itemRow.put("col_8", left != null ? left.getBudgetAmt() : "");
                itemRow.put("col_9", left != null ? left.getActualAmt() : "");
                itemRow.put("col_10", (left != null && left.getBudgetAmt() != null && left.getActualAmt() != null) ?
                        calculateCompletionRate(left.getActualAmt(), left.getBudgetAmt()) : "");
                itemRow.put(BmsConst.TableRenderKey.LEFT_LEAD_DEPT, left != null ? left.getLeadDeptName() : "");
                itemRow.put(BmsConst.TableRenderKey.LEFT_TYPE, left != null ? left.getType() : "");
                itemRow.put(BmsConst.TableRenderKey.LEFT_BUDGET_CODE, left != null ? left.getBudgetCode() : "");
                itemRow.put(BmsConst.TableRenderKey.LEFT_SUMMARY, left != null ? left.getIsSummary() : "");

                itemRow.put(BmsConst.TableColumnKey.RIGHT_PROJECT_COLUMN_INDEX_BEFORE_2025, right != null ? right.getBudgetName() : "");
                itemRow.put("col_12", right != null ? right.getBudgetAmt() : "");
                itemRow.put("col_13", right != null ? right.getActualAmt() : "");
                itemRow.put("col_14", (right != null && right.getBudgetAmt() != null && right.getActualAmt() != null) ?
                        calculateCompletionRate(right.getActualAmt(), right.getBudgetAmt()) : "");
                itemRow.put(BmsConst.TableRenderKey.RIGHT_LEAD_DEPT, right != null ? right.getLeadDeptName() : "");
                itemRow.put(BmsConst.TableRenderKey.RIGHT_TYPE, right != null ? right.getType() : "");
                itemRow.put(BmsConst.TableRenderKey.RIGHT_BUDGET_CODE, right != null ? right.getBudgetCode() : "");
                itemRow.put(BmsConst.TableRenderKey.RIGHT_SUMMARY, right != null ? right.getIsSummary() : "");
            } else {
                itemRow.put(BmsConst.TableColumnKey.PROJECT_COLUMN_INDEX_AFTER_2025, left != null ? left.getBudgetName() : "");
                itemRow.put("col_23", left != null ? left.getBudgetAmt() : "");
                itemRow.put("col_24", left != null ? left.getActualAmt() : "");
                itemRow.put("col_25", (left != null && left.getBudgetAmt() != null && left.getActualAmt() != null) ?
                        calculateCompletionRate(left.getActualAmt(), left.getBudgetAmt()) : "");
                itemRow.put(BmsConst.TableRenderKey.LEFT_LEAD_DEPT, left != null ? left.getLeadDeptName() : "");
                itemRow.put(BmsConst.TableRenderKey.LEFT_TYPE, left != null ? left.getType() : "");
                itemRow.put(BmsConst.TableRenderKey.LEFT_BUDGET_CODE, left != null ? left.getBudgetCode() : "");
                itemRow.put(BmsConst.TableRenderKey.LEFT_SUMMARY, left != null ? left.getIsSummary() : "");

                itemRow.put(BmsConst.TableColumnKey.RIGHT_PROJECT_COLUMN_INDEX_AFTER_2025, right != null ? right.getBudgetName() : "");
                itemRow.put("col_27", right != null ? right.getBudgetAmt() : "");
                itemRow.put("col_28", right != null ? right.getActualAmt() : "");
                itemRow.put("col_29", (right != null && right.getBudgetAmt() != null && right.getActualAmt() != null) ?
                        calculateCompletionRate(right.getActualAmt(), right.getBudgetAmt()) : "");
                itemRow.put("right_leadDept", right != null ? right.getLeadDeptName() : "");
                itemRow.put("right_type", right != null ? right.getType() : "");
                itemRow.put("right_budgetCode", right != null ? right.getBudgetCode() : "");
                itemRow.put("right_summary", right != null ? right.getIsSummary() : "");
            }

            itemRows.add(itemRow);
        }

        return itemRows;
    }

    /**
     * 处理表格数据
     *
     * @param reportData        报表数据
     * @param budgetDeptMapping 预算部门映射
     * @param year              年份
     * @return 处理后的表格数据
     */
    private Map<String, Object> processTableData(Map<String, Object> reportData, Object budgetDeptMapping, String year) {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> yearConfig = getYearConfig(year);
        String yearRangeType = (String) yearConfig.get("yearRangeType");

        String[] orgNames = (String[]) reportData.get("orgNames");
        //        预算-HRP科室
        Map<String, List<String>> orgNamesHrpMap = (Map<String, List<String>>) reportData.get("orgNamesHrpMap");
        //        预算-用友科室
        Map<String, List<String>> orgNamesYyMap = (Map<String, List<String>>) reportData.get("orgNamesYyMap");

        String[] orgTypes = (String[]) reportData.get("orgTypes");

        List<BmsExecuteBudgetResultsVo> econBudgetDetails = (List<BmsExecuteBudgetResultsVo>) reportData.get("econBudgetDetails");

        List<BmsBudgetWorkloadVo> econActualDetails = (List<BmsBudgetWorkloadVo>) reportData.get("econActualDetails");

        List<BmsBudgetSummaryVo> summaryItems = (List<BmsBudgetSummaryVo>) reportData.get("summaryItems");

        // 构建数据行
        List<Map<String, Object>> rows = buildDataRows(yearRangeType, orgNames, orgNamesHrpMap, orgNamesYyMap, orgTypes, econBudgetDetails, econActualDetails, budgetDeptMapping);

        // 构建项目行
        List<Map<String, Object>> itemRows = buildItemRows(yearRangeType, summaryItems);

        // 合并数据
        List<Map<String, Object>> data = new ArrayList<>();
        for (int i = 0; i < rows.size(); i++) {
            Map<String, Object> mergedRow = new HashMap<>(rows.get(i));
            if (i < itemRows.size()) {
                mergedRow.putAll(itemRows.get(i));
            }
            data.add(mergedRow);
        }

        // 生成表头
        Map<String, Object> tableHeadersResult = generateTableHeaders(year);
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> titles = (List<Map<String, Object>>) tableHeadersResult.get("titles");
        int columnIndex = (int) tableHeadersResult.get("columnIndex");

        // 添加项目列
        List<Map<String, Object>> processedTitles = addProjectColumns(titles, columnIndex, year);

        result.put("columns", processedTitles);
        result.put("tableData", data);

        return result;
    }
}
