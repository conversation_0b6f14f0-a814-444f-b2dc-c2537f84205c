package com.jp.med.bms.modules.bmsExecute.service.read.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetAdjustDto;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetApplyDto;
import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsExecuteBudgetAdjustReadMapper;
import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsExecuteBudgetApplyReadMapper;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsExecuteBudgetAdjustReadService;
import com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetAdjustVo;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.OSSUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Transactional(readOnly = true)
@Service
public class BmsExecuteBudgetAdjustReadServiceImpl extends ServiceImpl<BmsExecuteBudgetAdjustReadMapper, BmsExecuteBudgetAdjustDto> implements BmsExecuteBudgetAdjustReadService {

    @Autowired
    private BmsExecuteBudgetAdjustReadMapper bmsExecuteBudgetAdjustReadMapper;
    @Autowired
    private BmsExecuteBudgetApplyReadMapper bmsExecuteBudgetApplyReadMapper;

    @Override
    public List<BmsExecuteBudgetAdjustVo> queryList(BmsExecuteBudgetAdjustDto dto) {
        List<BmsExecuteBudgetAdjustVo> budgetAdjustVos = bmsExecuteBudgetAdjustReadMapper.queryList(dto);
        budgetAdjustVos.forEach(item ->
                {
                    if (!StringUtils.isEmpty(item.getAttachment())) {
                        item.setAttachmentUrl(OSSUtil.getPresignedObjectUrl(OSSConst.BUCKET_BMS, item.getAttachment()));
                    }
                }
        );
        return budgetAdjustVos;
    }

    @Override
    public Map<String, Object> queryListNew(BmsExecuteBudgetApplyDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        BmsExecuteBudgetApplyDto bmsExecuteBudgetApplyDto = bmsExecuteBudgetApplyReadMapper.selectByParam(dto);
        if (ObjectUtil.isEmpty(bmsExecuteBudgetApplyDto)){
            throw new AppException("没有找到对应的审核信息");
        }

        List<BmsExecuteBudgetAdjustVo> budgetAdjustVos = bmsExecuteBudgetAdjustReadMapper.queryListNew(bmsExecuteBudgetApplyDto);
        budgetAdjustVos.forEach(item ->
                {
                    if (!StringUtils.isEmpty(item.getAttachment())) {
                        item.setAttachmentUrl(OSSUtil.getPresignedObjectUrl(OSSConst.BUCKET_BMS, item.getAttachment()));
                    }
                }
        );

        HashMap<String, Object> map = new HashMap<>();
        map.put("apply",bmsExecuteBudgetApplyDto);
        map.put("adjust",budgetAdjustVos);
        return map;
    }

    @Override
    public List<BmsExecuteBudgetAdjustVo> queryBudget(BmsExecuteBudgetAdjustDto dto) {
        return bmsExecuteBudgetAdjustReadMapper.queryBudget(dto);
    }

    @Override
    public List<BmsExecuteBudgetAdjustVo> queryBudgetNew(BmsExecuteBudgetAdjustDto dto) {
        return bmsExecuteBudgetAdjustReadMapper.queryBudgetNew(dto);
    }
}
