package com.jp.med.bms.modules.bmsExecute.service.read.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeFlowModelDto;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeFlowModelReadMapper;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeFlowModelVo;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.dto.prcs.PrcsDetlDto;
//import com.jp.med.common.util.ActivitiUtil;
import com.jp.med.common.util.OSSUtil;
//import org.activiti.bpmn.model.*;
//import org.activiti.bpmn.model.Process;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsExecuteBudgetApplyReadMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetApplyDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetApplyVo;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsExecuteBudgetApplyReadService;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Transactional(readOnly = true)
@Service
public class BmsExecuteBudgetApplyReadServiceImpl extends ServiceImpl<BmsExecuteBudgetApplyReadMapper, BmsExecuteBudgetApplyDto> implements BmsExecuteBudgetApplyReadService {

    @Autowired
    private BmsExecuteBudgetApplyReadMapper bmsExecuteBudgetApplyReadMapper;

    @Autowired
    private BmsDisposeFlowModelReadMapper bmsDisposeFlowModelReadMapper;

    @Override
    public List<BmsExecuteBudgetApplyVo> queryList(BmsExecuteBudgetApplyDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<BmsExecuteBudgetApplyVo> applyVos = bmsExecuteBudgetApplyReadMapper.queryList(dto);
        for (BmsExecuteBudgetApplyVo applyVo : applyVos) {
            applyVo.setModelCaseUrl(OSSUtil.getPresignedObjectUrl(OSSConst.BUCKET_ACTIVITI, applyVo.getModelCase()));
        }
        return applyVos;
    }

    @Override
    public Map<String, Object> queryModalInit(BmsExecuteBudgetApplyDto dto) {
        BmsDisposeFlowModelDto modelDto = new BmsDisposeFlowModelDto();
        List<BmsDisposeFlowModelVo> modelVos = bmsDisposeFlowModelReadMapper.queryList(modelDto);
        return null;
    }

    @Override
    public List<BmsExecuteBudgetApplyVo> queryAdjustAudit(BmsExecuteBudgetApplyDto dto) {
//        List<Process> drg = ActivitiUtil.getTasksByDeployment("NEW");
//        Map<String, FlowElement> flowElementMap = drg.get(0).getFlowElementMap();
//        List<PrcsDetlDto> resultList = new ArrayList<>();
//        for (Map.Entry<String, FlowElement> entry : flowElementMap.entrySet()) {
//            if (entry.getValue() instanceof UserTask){
//                UserTask userTask = (UserTask) entry.getValue();
//                List<SequenceFlow> outgoingFlows = userTask.getOutgoingFlows();
//                Map<String, String> vai = new HashMap<>();
//                PrcsDetlDto prcsDetlDto = new PrcsDetlDto();
//                for (SequenceFlow outgoingFlow : outgoingFlows) {
//                    String expression = outgoingFlow.getConditionExpression();
//                    String targetRef = outgoingFlow.getTargetRef();
//                    if (!Objects.isNull(expression)){
//                        vai.put(expression, targetRef);
//                    }
//                }
//                prcsDetlDto.setChker(userTask.getAssignee());
//                prcsDetlDto.setPrcsCode(userTask.getId());
//                prcsDetlDto.setNodeType("1");
//                prcsDetlDto.setChildCode(JSON.toJSONString(vai));
//                prcsDetlDto.setNodeName(userTask.getName());
//                resultList.add(prcsDetlDto);
//            } else if (entry.getValue() instanceof ExclusiveGateway){
//                ExclusiveGateway exclusiveGateway = (ExclusiveGateway) entry.getValue();
//            }else if (entry.getValue() instanceof SequenceFlow){
//                SequenceFlow sequenceFlow = (SequenceFlow) entry.getValue();
//            }else if (entry.getValue() instanceof StartEvent){
//                StartEvent startEvent = (StartEvent) entry.getValue();
//                PrcsDetlDto prcsDetlDto = new PrcsDetlDto();
//                prcsDetlDto.setPrcsCode(startEvent.getId());
//                prcsDetlDto.setNodeType("0");
//                prcsDetlDto.setNodeName(startEvent.getName());
//                resultList.add(prcsDetlDto);
//            }else if (entry.getValue() instanceof EndEvent){
//                EndEvent endEvent = (EndEvent) entry.getValue();
//                PrcsDetlDto prcsDetlDto = new PrcsDetlDto();
//                prcsDetlDto.setPrcsCode(endEvent.getId());
//                prcsDetlDto.setNodeType("2");
//                prcsDetlDto.setNodeName(endEvent.getName());
//                resultList.add(prcsDetlDto);
//            }
//        }
//        List<BmsExecuteBudgetApplyVo> list = new ArrayList<>();
//        for (Task task : ActivitiUtil.getTasksByAssignee(dto.getUsername())) {
//            dto.setBusinessKey(task.getBusinessKey());
//            List<BmsExecuteBudgetApplyVo> list1 = queryList(dto);
//            BmsExecuteBudgetApplyVo vo = list1.get(0);
//            vo.setStatus(task.getName());
//            vo.setTaskId(task.getId());
//            list.add(vo);
//        }
        return new ArrayList<>();
    }

}
