package com.jp.med.bms.modules.bmsExecute.service.read.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.jp.med.bms.constant.BmsConst;
import com.jp.med.bms.modules.bmsExecute.dto.*;
import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsBudgetTaskReadMapper;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetTaskVo;
import com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTableProjDto;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeFlowDetailDto;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetProjReadMapper;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetTableProjReadMapper;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeFlowDetailReadMapper;
import com.jp.med.bms.modules.dispose.vo.*;
import com.jp.med.bms.util.ValidateUtil;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.FormulaUtil;
import com.jp.med.common.util.TreeNewUtil;
import com.jp.med.common.util.TreeUtil;
import org.apache.commons.lang.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsExecuteBudgetDataReadMapper;
import com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetDataVo;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsExecuteBudgetDataReadService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class BmsExecuteBudgetDataReadServiceImpl extends ServiceImpl<BmsExecuteBudgetDataReadMapper, BmsExecuteBudgetDataDto> implements BmsExecuteBudgetDataReadService {

    @Autowired
    private BmsExecuteBudgetDataReadMapper bmsExecuteBudgetDataReadMapper;

    @Autowired
    private BmsDisposeBudgetProjReadMapper bmsDisposeBudgetProjReadMapper;

    @Autowired
    private BmsDisposeBudgetTableProjReadMapper bmsDisposeBudgetTableProjReadMapper;

    @Autowired
    private BmsDisposeFlowDetailReadMapper bmsDisposeFlowDetailReadMapper;

    @Autowired
    private BmsBudgetTaskReadMapper bmsBudgetTaskReadMapper;

    @Override
    public Map<String, Object> queryList(BmsExecuteBudgetDataDto dto) {
        Map<String, Object> resultMap = new HashMap<>();
        LinkedHashMap<String, String> title = new LinkedHashMap<>();
        // 查询任务
        BmsBudgetTaskDto budgetTaskDto = new BmsBudgetTaskDto();
        budgetTaskDto.setBudgetTaskCode(dto.getBudgetTaskCode());
        List<BmsBudgetTaskVo> vos = bmsBudgetTaskReadMapper.queryList(budgetTaskDto);
        //查询编制项目
        BmsDisposeBudgetTableProjDto tableProj = new BmsDisposeBudgetTableProjDto();
        tableProj.setBudgetTableId(vos.get(0).getBudgetTableId());
        List<BmsDisposeBudgetTableProjVo> tableProjVos = bmsDisposeBudgetTableProjReadMapper.queryById(tableProj);
        Map<String, List<BmsDisposeBudgetTableProjVo>> listMap = tableProjVos.stream().collect(Collectors.groupingBy(BmsDisposeBudgetTableProjVo::getBudgetCode));
        // 查询编制数据
        List<BmsExecuteBudgetDataVo> resultList = new ArrayList<>();
        dto.setSqlAutowiredHospitalCondition(true);
        List<BmsExecuteBudgetDataVo> budgetDataVos = bmsExecuteBudgetDataReadMapper.queryList(dto);
        if (!budgetDataVos.isEmpty()) {
            for (BmsExecuteBudgetDataVo budgetDataVo : budgetDataVos) {
                budgetDataVo.setBudgetNameAll(listMap.get(budgetDataVo.getBudgetCode()).get(0).getBudgetName());
            }
            // 根据执行步骤分开
            Map<String, List<BmsExecuteBudgetDataVo>> collect = budgetDataVos.stream().collect(Collectors.groupingBy(BmsExecuteBudgetDataVo::getFlowDetailCode));
            // 按步骤和编码区分
            Map<String, Map<String, BmsExecuteBudgetDataVo>> val = new HashMap<>();
            for (Map.Entry<String, List<BmsExecuteBudgetDataVo>> listEntry : collect.entrySet()) {
                List<BmsExecuteBudgetDataVo> value = listEntry.getValue();
                Map<String, BmsExecuteBudgetDataVo> voMap = value.stream().collect(Collectors.toMap(vo -> vo.getBudgetCode() + "_" + vo.getOrgId(), vo -> vo, (o1, o2) -> o2));
                val.put(listEntry.getKey(), voMap);
            }
            resultList = collect.get(dto.getFlowDetailCode());
            dto.setSqlAutowiredHospitalCondition(true);
            // 获取所有数据用于计算变动值
            List<BmsExecuteBudgetDataVo> dataVos = bmsExecuteBudgetDataReadMapper.queryAllAmount(dto);
            Map<String, BmsExecuteBudgetDataVo> dataMap = dataVos.stream().collect(Collectors.toMap(vo -> vo.getBudgetCode() + "_" + vo.getOrgId(), vo -> vo, (o1, o2) -> o2));
//            if (collect.size() > 1) {
            BmsDisposeFlowDetailDto flowDetailDto = new BmsDisposeFlowDetailDto();
            flowDetailDto.setBudgetFlowCode(vos.get(0).getBudgetFlowCode());
            // 查询该预算的执行步骤明细
            List<BmsDisposeFlowDetailVo> detailVos = bmsDisposeFlowDetailReadMapper.queryFlowDetail(flowDetailDto);
            for (BmsExecuteBudgetDataVo budgetDataVo : resultList) {
                Map<String, BigDecimal> map = new HashMap<>();
                for (BmsDisposeFlowDetailVo detailVo : detailVos) {
                    if (dto.getOrganizationOrder() > detailVo.getOrganizationOrder()) {
//                        List<BmsExecuteBudgetDataVo> value = collect.get(detailVo.getFlowDetailCode());
//                        Map<String, BmsExecuteBudgetDataVo> voMap = value.stream().collect(Collectors.toMap(vo -> vo.getBudgetCode() + "_" + vo.getOrgId(), vo -> vo, (o1, o2) -> o2));
                        try {
                            map.put(detailVo.getFlowDetailCode(), val.get(detailVo.getFlowDetailCode()).get(budgetDataVo.getBudgetCode() + "_" + budgetDataVo.getOrgId()).getBudgetAmount());
                        } catch (Exception ignored) {

                        }
                        title.put(detailVo.getFlowDetailCode(), detailVo.getFlowDetailName());
                    }
                }
                budgetDataVo.setExt(map);
                // 判断该属性是否为计算出来的属性
                if (MedConst.TYPE_1.equals(budgetDataVo.getCal())) {
                    // 标识公式内的所有数据都存在
                    String formula = budgetDataVo.getFormula();
                    String[] tokens = formula.split("[-+*/()]");
                    for (String token : tokens) {
                        if (token.matches("\\d+")) {
                            continue;
                        }
                        BmsExecuteBudgetDataVo dataVo = dataMap.get(token + "_" + budgetDataVo.getOrgId());
                        BigDecimal budgetAmount;
                        if (Objects.isNull(dataVo)) {
                            budgetAmount = BigDecimal.ZERO;
                        } else {
                            budgetAmount = dataVo.getBudgetAmount();
                        }
                        formula = formula.replace(token, String.valueOf(budgetAmount));
                    }
                    BigDecimal decimal = FormulaUtil.formula(formula);
                    budgetDataVo.setBudgetAmount(decimal);
                }
            }
//            }
        }
        resultMap.put("data", resultList);
        resultMap.put("title", title);
        return resultMap;
    }


    public Map<String, Object> queryData(BmsExecuteBudgetDataDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        BmsDisposeFlowDetailDto detailDto = new BmsDisposeFlowDetailDto();
        detailDto.setFlowDetailCode(dto.getFlowDetailCode());
        BmsDisposeFlowDetailVo flowDetailVo = bmsDisposeFlowDetailReadMapper.queryByCode(detailDto);
//        dto.setEconSub(flowDetailVo.getEconSub());
        List<TitleVo> projVos = bmsDisposeBudgetProjReadMapper.queryTitle(dto);
        Map<String, List<BmsOrgVo>> dept = new HashMap<>();
        if (!Objects.isNull(dto.getDeptQuery()) && dto.getDeptQuery().length > 0) {
            List<BmsOrgVo> bmsOrgVos = bmsExecuteBudgetDataReadMapper.queryOrg(dto);
            dept = bmsOrgVos.stream().collect(Collectors.groupingBy(BmsOrgVo::getOrgId));
        }
        TitleVo titleVo = new TitleVo();
        titleVo.setTitle("科室");
        titleVo.setHeader("科室");
        titleVo.setWidth("150");
        titleVo.setOrder(-1);
        titleVo.setFixed("left");
        titleVo.setKey("orgId");
        titleVo.setSorter("default");
        projVos.add(0, titleVo);
        TitleVo titleVo1 = new TitleVo();
        titleVo1.setTitle("预算科室名称");
        titleVo1.setHeader("预算科室名称");
        titleVo1.setOrder(0);
        titleVo1.setWidth("150");
        titleVo1.setFixed("left");
        titleVo1.setKey("orgName");
        projVos.add(1, titleVo1);
        Map<String, Object> hashMap = new HashMap<>();
        // 查询编制数据
        List<Map<String, Object>> resultList = new ArrayList<>();
        List<BmsExecuteBudgetDataVo> budgetDataVos = bmsExecuteBudgetDataReadMapper.queryList(dto);
        Map<String, List<BmsExecuteBudgetDataVo>> listMap = budgetDataVos.stream().collect(Collectors.groupingBy(bmsExecuteBudgetDataVo -> bmsExecuteBudgetDataVo.getOrgId() + " " + bmsExecuteBudgetDataVo.getOrgName()));
        // 获取所有数据用于计算变动值
        List<BmsExecuteBudgetDataVo> dataVos = bmsExecuteBudgetDataReadMapper.queryAllAmount(dto);
        Map<String, BmsExecuteBudgetDataVo> dataMap = dataVos.stream().collect(Collectors.toMap(vo -> vo.getBudgetCode() + "_" + vo.getOrgId(), vo -> vo, (o1, o2) -> o2));
        for (Map.Entry<String, List<BmsExecuteBudgetDataVo>> entry : listMap.entrySet()) {
            if (!dept.isEmpty() && Objects.isNull(dept.get(entry.getValue().get(0).getOrgId()))) {
                continue;
            }
            Map<String, Object> res = new HashMap<>();
            List<BmsExecuteBudgetDataVo> value = entry.getValue();
            Map<String, List<BmsExecuteBudgetDataVo>> collected = value.stream().collect(Collectors.groupingBy(BmsExecuteBudgetDataVo::getBudgetCode));
            for (Map.Entry<String, List<BmsExecuteBudgetDataVo>> result : collected.entrySet()) {
                String key = result.getKey();
                List<BmsExecuteBudgetDataVo> resultValue = result.getValue();
                Map<String, BmsExecuteBudgetDataVo> voMap = resultValue.stream().collect(Collectors.toMap(BmsExecuteBudgetDataVo::getFlowDetailCode, dataVo -> dataVo, (o1, o2) -> o2));
//                resultValue.sort(Comparator.comparing(BmsExecuteBudgetDataVo::getOrganizationOrder).reversed());

                BmsExecuteBudgetDataVo dataVo = voMap.get(dto.getFlowDetailCode());
                if (MedConst.TYPE_1.equals(dataVo.getCal())) {
                    String formula = dataVo.getFormula();
                    String[] tokens = formula.split("[-+*/()]");
                    for (String token : tokens) {
                        if (token.matches("\\d+")) {
                            continue;
                        }
                        BmsExecuteBudgetDataVo dataVo1 = dataMap.get(token + "_" + dataVo.getOrgId());
                        BigDecimal budgetAmount;
                        if (Objects.isNull(dataVo1)) {
                            budgetAmount = BigDecimal.ZERO;
                        } else {
                            budgetAmount = dataVo1.getBudgetAmount();
                        }
                        formula = formula.replace(token, String.valueOf(budgetAmount));
                    }
                    BigDecimal decimal = FormulaUtil.formula(formula);
                    dataVo.setBudgetAmount(decimal);
                }
                res.put(key, dataVo.getBudgetAmount());
            }
            if (res.isEmpty()) {
                continue;
            }
            res.put("orgName", value.get(0).getOrgName());
            res.put("orgId", value.get(0).getOrgId());
            resultList.add(res);
        }
        resultList.sort(Comparator.comparing(o -> String.valueOf(o.get("orgId"))));
//        projVos.sort(Comparator.comparing(TitleVo::getOrder));
        hashMap.put("title", projVos);
        hashMap.put("data", resultList);
        return hashMap;
    }

    @Override
    public Map<String, Object> queryPageInit(BmsExecuteBudgetDataDto dto) {
        Map<String, Object> hashMap = new HashMap<>();
        BmsBudgetTaskDto taskDto = new BmsBudgetTaskDto();
        List<BmsBudgetTaskVo> vos = bmsBudgetTaskReadMapper.queryList(taskDto);
        hashMap.put("flow", vos);
        return hashMap;
    }

    @Override
    public Map<String, Object> queryBudgetOrder(BmsExecuteBudgetDataDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        List<Map<String, Object>> budgetOrderMap = bmsExecuteBudgetDataReadMapper.queryBudgetOrder(dto);
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("orderData",budgetOrderMap);
        return hashMap;
    }

    @Override
    public Map<String, Object> queryBudgetSummary(BmsExecuteBudgetDataDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        BmsDisposeFlowDetailDto detailDto = new BmsDisposeFlowDetailDto();
        detailDto.setFlowDetailCode(dto.getFlowDetailCode());
        BmsDisposeFlowDetailVo flowDetailVo = bmsDisposeFlowDetailReadMapper.queryByCode(detailDto);
//        dto.setEconSub(flowDetailVo.getEconSub());
        List<TitleVo> projVos = bmsDisposeBudgetProjReadMapper.queryNormalTitle(dto);
        List<TitleVo> leafNode = TreeUtil.getLeafNode(projVos);
        TreeNewUtil<String, TitleVo> util = new TreeNewUtil<>();
        projVos = util.buildTree(projVos);
        TitleVo title = buildTitle("科室", "科室", "150", "left", "orgId", -1);
        title.setSorter("default");
        projVos.add(0, title);
        projVos.add(1, buildTitle("预算科室名称", "预算科室名称", "150", "left", "orgName", 0));
        TitleVo builtTitle = buildTitle("合计", "合计", "150", "right", "allCount", projVos.size());
        builtTitle.setSummary(true);
        projVos.add(projVos.size(), builtTitle);
        Map<String, Object> hashMap = new HashMap<>();
        // 查询编制数据
        List<Map<String, Object>> resultList = new ArrayList<>();
        List<BmsExecuteBudgetDataVo> budgetDataVos = bmsExecuteBudgetDataReadMapper.queryList(dto);
        Map<String, List<BmsExecuteBudgetDataVo>> listMap = budgetDataVos.stream().collect(Collectors.groupingBy(bmsExecuteBudgetDataVo -> bmsExecuteBudgetDataVo.getOrgId() + " " + bmsExecuteBudgetDataVo.getOrgName()));
        // 获取所有数据用于计算变动值
        List<BmsExecuteBudgetDataVo> dataVos = bmsExecuteBudgetDataReadMapper.queryAllAmount(dto);
        Map<String, BmsExecuteBudgetDataVo> dataMap = dataVos.stream().collect(Collectors.toMap(vo -> vo.getBudgetCode() + "_" + vo.getOrgId(), vo -> vo, (o1, o2) -> o2));
        for (Map.Entry<String, List<BmsExecuteBudgetDataVo>> entry : listMap.entrySet()) {
            Map<String, Object> res = new HashMap<>();
            List<BmsExecuteBudgetDataVo> value = entry.getValue();
            Map<String, List<BmsExecuteBudgetDataVo>> collected = value.stream().collect(Collectors.groupingBy(BmsExecuteBudgetDataVo::getBudgetCode));
            for (Map.Entry<String, List<BmsExecuteBudgetDataVo>> result : collected.entrySet()) {
                String key = result.getKey();
                List<BmsExecuteBudgetDataVo> resultValue = result.getValue();
                List<BmsExecuteBudgetDataVo> resultData = resultValue.stream().filter(bmsExecuteBudgetDataVo -> bmsExecuteBudgetDataVo.getFlowDetailCode().equals(dto.getFlowDetailCode())).collect(Collectors.toList());
                BmsExecuteBudgetDataVo dataVo = resultData.get(0);
                if (MedConst.TYPE_1.equals(dataVo.getCal())) {
                    String formula = dataVo.getFormula();
                    String[] tokens = formula.split("[-+*/()]");
                    for (String token : tokens) {
                        if (token.matches("\\d+")) {
                            continue;
                        }
                        BmsExecuteBudgetDataVo dataVo1 = dataMap.get(token + "_" + dataVo.getOrgId());
                        BigDecimal budgetAmount;
                        if (Objects.isNull(dataVo1)) {
                            budgetAmount = BigDecimal.ZERO;
                        } else {
                            budgetAmount = dataVo1.getBudgetAmount();
                        }
                        formula = formula.replace(token, String.valueOf(budgetAmount));
                    }
                    BigDecimal decimal = FormulaUtil.formula(formula);
                    dataVo.setBudgetAmount(decimal);
                }
                res.put(key, dataVo.getBudgetAmount());
            }
            if (res.isEmpty()) {
                continue;
            }
            res.put("orgName", value.get(0).getOrgName());
            res.put("orgId", value.get(0).getOrgId());
            resultList.add(res);
        }
        resultList.sort(Comparator.comparing(o -> String.valueOf(o.get("orgId"))));
        hashMap.put("title", projVos);
        List<TitleVo> gzflzc = new ArrayList<>();
        for (TitleVo projVo : projVos) {
            if ("GZFLZC".equals(projVo.getKey())) {
                List<TitleVo> children = projVo.getChildren();
                TitleVo titleVo = buildTitle("小计", "小计", "150", "", "gzflzcCount", children.size());
                titleVo.setSummary(true);
                children.add(children.size(), titleVo);
                getAllLeafNodes(children, gzflzc);
                break;
            }
        }
        for (Map<String, Object> map : resultList) {
            BigDecimal count = BigDecimal.ZERO;
            BigDecimal gzflzcCount = BigDecimal.ZERO;
            for (TitleVo titleVo : leafNode) {
                Object object = map.get(titleVo.getKey());
                if (!Objects.isNull(object)) {
                    count = count.add(new BigDecimal(String.valueOf(object)));
                }
            }
            for (TitleVo titleVo : gzflzc) {
                Object object = map.get(titleVo.getKey());
                if (!Objects.isNull(object)) {
                    gzflzcCount = gzflzcCount.add(new BigDecimal(String.valueOf(object)));
                }
            }

            map.put("allCount", count);
            map.put("gzflzcCount", gzflzcCount);
        }
        hashMap.put("data", resultList);
        return hashMap;
    }

    @Override
    public List<BmsExecuteBudgetDataVo> upload(BmsExecuteBudgetDataDto dto) {
        Map<String, Object> map = queryList(dto);
        List<BmsExecuteBudgetDataVo> res = (List<BmsExecuteBudgetDataVo>) map.get("data");
        try {
            Map<Integer, List<BmsExecuteBudgetDataVo>> collect = res.stream().collect(Collectors.groupingBy(BmsExecuteBudgetDataVo::getBudgetDataId));
            Map<String, List<BmsExecuteBudgetDataVo>> orgMap = res.stream().collect(Collectors.groupingBy(BmsExecuteBudgetDataVo::getOrgId));
            for (MultipartFile file : dto.getFiles()) {
                if ("1".equals(dto.getUploadType())) {
                    EasyExcel.read(file.getInputStream(), BmsBudgetDataDto.class, new AnalysisEventListener<BmsBudgetDataDto>() {
                        @Override
                        public void invoke(BmsBudgetDataDto data, AnalysisContext context) {
                            List<BmsExecuteBudgetDataVo> budgetDataVos = collect.get(data.getId());
                            if (!Objects.isNull(budgetDataVos)) {
                                budgetDataVos.get(0).setBudgetAmount(data.getBudgetAmount());
                            }
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext context) {
                        }
                    }).sheet().doRead();
                }

                if ("2".equals(dto.getUploadType())) {
                    List<BmsDisposeBudgetProjVo> projVos = bmsDisposeBudgetProjReadMapper.queryExcelTitle(dto);
                    if (projVos.isEmpty()) {
                        throw new AppException("表头获取失败");
                    }
                    Map<String, String> title = projVos.stream().collect(Collectors.toMap(BmsDisposeBudgetProjVo::getBudgetName, BmsDisposeBudgetProjVo::getBudgetCode, (o1, o2) -> o2));
                    title.put("科室", "DEPT");
                    title.put("预算科室名称", "DEPT_NAME");
                    EasyExcel.read(file.getInputStream(), new AnalysisEventListener<Map<Integer, String>>() {
                        int ROW = 1;
                        Map<String, Integer> header = new HashMap<>();

                        @Override
                        public void invoke(Map<Integer, String> data, AnalysisContext context) {
                            if (ROW == 1) {
                                for (Map.Entry<Integer, String> entry : data.entrySet()) {
                                    header.put(title.get(entry.getValue()), entry.getKey());
                                }
                            } else {
                                List<BmsExecuteBudgetDataVo> budgetDataVos = orgMap.get(data.get(header.get("DEPT")));
                                if (!Objects.isNull(budgetDataVos)) {
                                    for (BmsExecuteBudgetDataVo budgetDataVo : budgetDataVos) {
                                        Integer integer = header.get(budgetDataVo.getBudgetCode());
                                        if (!Objects.isNull(integer)) {
                                            Object o = data.get(integer);
                                            BigDecimal amount = Objects.isNull(o) ? BigDecimal.ZERO : new BigDecimal(String.valueOf(o));
                                            amount = BigDecimal.ZERO.compareTo(amount) > -1 ? BigDecimal.ZERO : amount;
                                            budgetDataVo.setBudgetAmount(amount);
                                        }
                                    }
                                }
                            }
                            ROW++;
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext context) {
                        }
                    }).sheet().headRowNumber(-1).doRead();
                }
            }
        } catch (Exception e) {
            log.error(e.toString());
            throw new AppException("文件解析失败");
        }
        // 获取所有数据用于计算变动值
        List<BmsExecuteBudgetDataVo> dataVos = bmsExecuteBudgetDataReadMapper.queryAllAmount(dto);
        Map<String, BmsExecuteBudgetDataVo> dataMap = dataVos.stream().collect(Collectors.toMap(vo -> vo.getBudgetCode() + "_" + vo.getOrgId(), vo -> vo, (o1, o2) -> o2));
        for (BmsExecuteBudgetDataVo budgetDataVo : res) {
            // 判断该属性是否为计算出来的属性
            if (MedConst.TYPE_1.equals(budgetDataVo.getCal())) {
                // 标识公式内的所有数据都存在
                String formula = budgetDataVo.getFormula();
                String[] tokens = formula.split("[-+*/()]");
                for (String token : tokens) {
                    if (token.matches("\\d+")) {
                        continue;
                    }
                    BmsExecuteBudgetDataVo dataVo = dataMap.get(token + "_" + budgetDataVo.getOrgId());
                    BigDecimal budgetAmount;
                    if (Objects.isNull(dataVo)) {
                        budgetAmount = BigDecimal.ZERO;
                    } else {
                        budgetAmount = dataVo.getBudgetAmount();
                    }
                    formula = formula.replace(token, String.valueOf(budgetAmount));
                }
                BigDecimal decimal = FormulaUtil.formula(formula);
                budgetDataVo.setBudgetAmount(decimal);
            }
        }
        return res;
    }

    @Override
    public List<BmsExecuteBudgetDataVo> queryDataDetail(BmsExecuteBudgetDataDto dto) {
        return bmsExecuteBudgetDataReadMapper.queryDataDetail(dto);
    }

    @Override
    public Map<String, Object> queryBudgetReportSummary(BmsExecuteBudgetDataDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        BmsDisposeFlowDetailDto detailDto = new BmsDisposeFlowDetailDto();
        detailDto.setFlowDetailCode(dto.getFlowDetailCode());
        BmsDisposeFlowDetailVo flowDetailVo = bmsDisposeFlowDetailReadMapper.queryByCode(detailDto);
//        dto.setEconSub(flowDetailVo.getEconSub());
        List<TitleVo> projVos = bmsDisposeBudgetProjReadMapper.queryNormalTitle(dto);
        List<TitleVo> leafNode = TreeUtil.getLeafNode(projVos);
        TreeNewUtil<String, TitleVo> util = new TreeNewUtil<>();
        projVos = util.buildTree(projVos);
        TitleVo title = buildTitle("科室", "科室", "150", "left", "orgId", -1);
        title.setSorter("default");
        projVos.add(0, title);
        projVos.add(1, buildTitle("预算科室名称", "预算科室名称", "150", "left", "orgName", 0));
//        TitleVo builtTitle = buildTitle("合计", "合计", "150", "right", "allCount", projVos.size());
//        builtTitle.setSummary(true);
//        projVos.add(projVos.size(), builtTitle);
        Map<String, Object> hashMap = new HashMap<>();
        // 查询编制数据
        List<Map<String, Object>> resultList = new ArrayList<>();

        // 查询所有类型编制结果（ZCL、SR）
        dto.setBudgetTypeCode(null);
        List<BmsExecuteBudgetDataVo> budgetDataVos = bmsExecuteBudgetDataReadMapper.queryList(dto);

        Map<String, List<BmsExecuteBudgetDataVo>> listMapAll = budgetDataVos.stream().collect(Collectors.groupingBy(BmsExecuteBudgetDataVo::getBudgetTypeCode));
        List<BmsExecuteBudgetDataVo> zclBudgetDataVoList = listMapAll.get(BmsConst.BUDGET_TYPE_ZCL);
        List<BmsExecuteBudgetDataVo> srBudgetDataVoList = listMapAll.get(BmsConst.BUDGET_TYPE_SR);

        //处理支出类项目编制
        Map<String, List<BmsExecuteBudgetDataVo>> listMapZcl = zclBudgetDataVoList.stream().collect(Collectors.groupingBy(bmsExecuteBudgetDataVo -> bmsExecuteBudgetDataVo.getOrgId() + " " + bmsExecuteBudgetDataVo.getOrgName()));
        Map<String, List<BmsExecuteBudgetDataVo>> listMapSr = srBudgetDataVoList.stream().collect(Collectors.groupingBy(bmsExecuteBudgetDataVo -> bmsExecuteBudgetDataVo.getOrgId() + " " + bmsExecuteBudgetDataVo.getOrgName()));

        List<BmsExecuteBudgetDataVo> zclBudgetDataVoListLv2 = zclBudgetDataVoList.stream().filter(bmsExecuteBudgetDataVo -> bmsExecuteBudgetDataVo.getBudgetStatisticsLv2Code() != null).collect(Collectors.toList());
        //支出类按二级核算维度进行分组
        Map<String, List<BmsExecuteBudgetDataVo>> listMapZclLv2 = zclBudgetDataVoListLv2.stream().collect(Collectors.groupingBy(BmsExecuteBudgetDataVo::getBudgetStatisticsLv2Name));

        //支出类按一级核算维度进行分组
        Map<String, List<BmsExecuteBudgetDataVo>> listMapZclLv1 = zclBudgetDataVoList.stream().collect(Collectors.groupingBy(BmsExecuteBudgetDataVo::getBudgetStatisticsLv1Name));

        //计算药品费用（西医、中草药）
        Map<String, BigDecimal> totaldrugfeeMap = buildDurgCost(listMapSr);

        //获取所有数据用于计算变动值
        List<BmsExecuteBudgetDataVo> dataVos = bmsExecuteBudgetDataReadMapper.queryAllAmount(dto);

        Map<String, BmsExecuteBudgetDataVo> dataMap = dataVos.stream().collect(Collectors.toMap(vo -> vo.getBudgetCode() + "_" + vo.getOrgId(), vo -> vo, (o1, o2) -> o2));
        for (Map.Entry<String, List<BmsExecuteBudgetDataVo>> entry : listMapZcl.entrySet()) {
            Map<String, Object> res = new HashMap<>();
            List<BmsExecuteBudgetDataVo> value = entry.getValue();
            Map<String, List<BmsExecuteBudgetDataVo>> collected = value.stream().collect(Collectors.groupingBy(BmsExecuteBudgetDataVo::getBudgetCode));
            for (Map.Entry<String, List<BmsExecuteBudgetDataVo>> result : collected.entrySet()) {
                String key = result.getKey();
                List<BmsExecuteBudgetDataVo> resultValue = result.getValue();
                List<BmsExecuteBudgetDataVo> resultData = resultValue.stream().filter(bmsExecuteBudgetDataVo -> bmsExecuteBudgetDataVo.getFlowDetailCode().equals(dto.getFlowDetailCode())).collect(Collectors.toList());
                BmsExecuteBudgetDataVo dataVo = resultData.get(0);
                if (MedConst.TYPE_1.equals(dataVo.getCal())) {
                    String formula = dataVo.getFormula();
                    String[] tokens = formula.split("[-+*/()]");
                    for (String token : tokens) {
                        if (token.matches("\\d+")) {
                            continue;
                        }
                        BmsExecuteBudgetDataVo dataVo1 = dataMap.get(token + "_" + dataVo.getOrgId());
                        BigDecimal budgetAmount;
                        if (Objects.isNull(dataVo1)) {
                            budgetAmount = BigDecimal.ZERO;
                        } else {
                            budgetAmount = dataVo1.getBudgetAmount();
                        }
                        formula = formula.replace(token, String.valueOf(budgetAmount));
                    }
                    BigDecimal decimal = FormulaUtil.formula(formula);
                    dataVo.setBudgetAmount(decimal);
                }
                res.put(key, dataVo.getBudgetAmount());
            }
            if (res.isEmpty()) {
                continue;
            }
            res.put("orgName", value.get(0).getOrgName());
            res.put("orgId", value.get(0).getOrgId());
            resultList.add(res);
        }

        resultList.sort(Comparator.comparing(o -> String.valueOf(o.get("orgId"))));

        //遍历编制预算数据值，重置西医及费用
        for (Map<String, Object> result : resultList) {
            if (BmsConst.PHARMACY_DEPT_ORGCODE.equals(result.get("orgId"))) {
                //药剂科
                if (!ValidateUtil.isEmpty(totaldrugfeeMap.get(BmsConst.BUDGET_TOTALDRUGFEE_WESTERN))) {
                    //西药
                    result.put(BmsConst.BUDGET_TOTALDRUGFEE_WESTERN, totaldrugfeeMap.get(BmsConst.BUDGET_TOTALDRUGFEE_WESTERN));
                }
                if (!ValidateUtil.isEmpty(totaldrugfeeMap.get(BmsConst.BUDGET_TOTALDRUGFEE_CHINESE))) {
                    //中草药
                    result.put(BmsConst.BUDGET_TOTALDRUGFEE_CHINESE, totaldrugfeeMap.get(BmsConst.BUDGET_TOTALDRUGFEE_CHINESE));
                }
            }
        }
        //计算二级合计
        getBudgetAmountTotal(listMapZclLv2, projVos, resultList, totaldrugfeeMap, BmsConst.BUDGET_STATISTICS_TYPE_LV2);
        //计算一级合计
        getBudgetAmountTotal(listMapZclLv1, projVos, resultList, totaldrugfeeMap, BmsConst.BUDGET_STATISTICS_TYPE_LV1);

        hashMap.put("title", projVos);
        List<TitleVo> gzflzc = new ArrayList<>();
        for (TitleVo projVo : projVos) {
            if ("GZFLZC".equals(projVo.getKey())) {
                List<TitleVo> children = projVo.getChildren();
                TitleVo titleVo = buildTitle("小计", "小计", "150", "", "gzflzcCount", children.size());
                titleVo.setSummary(true);
                children.add(children.size(), titleVo);
                getAllLeafNodes(children, gzflzc);
                break;
            }
        }
        for (Map<String, Object> map : resultList) {
            BigDecimal count = BigDecimal.ZERO;
            BigDecimal gzflzcCount = BigDecimal.ZERO;
            for (TitleVo titleVo : leafNode) {
                Object object = map.get(titleVo.getKey());
                if (!Objects.isNull(object)) {
                    count = count.add(new BigDecimal(String.valueOf(object)));
                }
            }
            for (TitleVo titleVo : gzflzc) {
                Object object = map.get(titleVo.getKey());
                if (!Objects.isNull(object)) {
                    gzflzcCount = gzflzcCount.add(new BigDecimal(String.valueOf(object)));
                }
            }
            map.put("allCount", count);
            map.put("gzflzcCount", gzflzcCount);
        }
        hashMap.put("data", resultList);
        return hashMap;
    }

    /**
     * 获取合计信息
     *
     * @param listMapZcl
     * @param projVos
     * @param resultList
     */
    private void getBudgetAmountTotal(Map<String, List<BmsExecuteBudgetDataVo>> listMapZcl, List<TitleVo> projVos, List<Map<String, Object>> resultList, Map<String, BigDecimal> totaldrugfeeMap, String statisticsType) {
        if (!ValidateUtil.isEmpty(listMapZcl)) {
            String titleName = "";
            BigDecimal itemTotalSize;
            List<BmsExecuteBudgetDataVo> bmsExecList;
            Map<String, Object> res;

            BigDecimal allTotalAmount = new BigDecimal(0);
            for (Map.Entry<String, List<BmsExecuteBudgetDataVo>> result : listMapZcl.entrySet()) {
                res = new HashMap<>();
                itemTotalSize = new BigDecimal(0);
                //获取表头
                bmsExecList = result.getValue();
                titleName = result.getKey();
                TitleVo titleVo = buildTitle(titleName + "小计", titleName + "小计", "150", "", result.getKey(), 1);
                titleVo.setSummary(true);
                projVos.add(projVos.size(), titleVo);
                for (BmsExecuteBudgetDataVo budgetDataVo : bmsExecList) {
                    itemTotalSize = itemTotalSize.add(budgetDataVo.getBudgetAmount());
                }

                //药品费用处理
                if (BmsConst.BUDGET_TOTAL_SPFW_NAME.equals(result.getKey()) && BmsConst.BUDGET_STATISTICS_TYPE_LV2.equals(statisticsType)) {
                    if (!ValidateUtil.isEmpty(totaldrugfeeMap.get(BmsConst.BUDGET_TOTALDRUGFEE_WESTERN))) {
                        //西药
                        itemTotalSize = itemTotalSize.add(totaldrugfeeMap.get(BmsConst.BUDGET_TOTALDRUGFEE_WESTERN));
                    }
                    if (!ValidateUtil.isEmpty(totaldrugfeeMap.get(BmsConst.BUDGET_TOTALDRUGFEE_CHINESE))) {
                        //中草药
                        itemTotalSize = itemTotalSize.add(totaldrugfeeMap.get(BmsConst.BUDGET_TOTALDRUGFEE_CHINESE));
                    }
                }
                res.put("orgName", titleName);
                res.put("orgId", titleName);
                res.put(result.getKey(), itemTotalSize);

                allTotalAmount = allTotalAmount.add(itemTotalSize);
                resultList.add(res);
            }
            // 设置总体合计
            if (BmsConst.BUDGET_STATISTICS_TYPE_LV1.equals(statisticsType)) {
                Map<String, Object> resTotal = new HashMap<>();
                TitleVo titleVo = buildTitle("合计", "合计", "150", "", "allTotalAmount", 1);
                titleVo.setSummary(true);
                projVos.add(projVos.size(), titleVo);
                resTotal.put("orgName", "合计");
                resTotal.put("orgId", "合计");
                resTotal.put("allTotalAmount", allTotalAmount);
                resultList.add(resTotal);
            }
        }
    }

    /**
     * 根据收入类指标计算总药品费用
     */
    private Map<String, BigDecimal> buildDurgCost(Map<String, List<BmsExecuteBudgetDataVo>> listMapSr) {
        Map<String, BigDecimal> drugTotalFee = new HashMap<>();
        if (!ValidateUtil.isEmpty(listMapSr)) {
            // 存在收入及支出类指标
            BigDecimal westernSumdrugfee = new BigDecimal(0);
            BigDecimal chindesSumdrugfee = new BigDecimal(0);
            BigDecimal cyrcSize, mzrcSize, mzAvgdrugfeeYzb, zyWesternAvgdrugfeeYzb1, zyChindesAvgdrugfeeYzb2;
            for (Map.Entry<String, List<BmsExecuteBudgetDataVo>> entry : listMapSr.entrySet()) {
                Map<String, Object> res = new HashMap<>();
                String orgid = entry.getKey();
                List<BmsExecuteBudgetDataVo> value = entry.getValue();
                Map<String, List<BmsExecuteBudgetDataVo>> collected = value.stream().collect(Collectors.groupingBy(BmsExecuteBudgetDataVo::getBudgetCode));
                Map<String, BigDecimal> orgBudgetAmountMap = value.stream().collect(Collectors.toMap(BmsExecuteBudgetDataVo::getBudgetCode, BmsExecuteBudgetDataVo::getBudgetAmount));
                //获取平均药品费用支出预算以及门诊人次、住院人次
                cyrcSize = orgBudgetAmountMap.get(BmsConst.BUDGET_CODE_CYRC);
                mzrcSize = orgBudgetAmountMap.get(BmsConst.BUDGET_CODE_MZRC);
                mzAvgdrugfeeYzb = orgBudgetAmountMap.get(BmsConst.BUDGET_CODE_AVGDRUGFEE_YZB);
                zyWesternAvgdrugfeeYzb1 = orgBudgetAmountMap.get(BmsConst.BUDGET_CODE_AVGDRUGFEE_YZB1);
                zyChindesAvgdrugfeeYzb2 = orgBudgetAmountMap.get(BmsConst.BUDGET_CODE_AVGDRUGFEE_YZB2);
                if (!ValidateUtil.isEmpty(cyrcSize)) {
                    if (!ValidateUtil.isEmpty(zyWesternAvgdrugfeeYzb1)) {
                        //住院西医，住院人次*住院均次药品费用
//                        if (!orgid.startsWith(BmsConst.TRAD_CHINESE_DEPT_CODE)) {}
                            westernSumdrugfee = westernSumdrugfee.add(cyrcSize.multiply(zyWesternAvgdrugfeeYzb1));

                    }
                }
                if (!ValidateUtil.isEmpty(mzrcSize)) {
                    //西医
                    if (!ValidateUtil.isEmpty(mzAvgdrugfeeYzb)) {
                        //门诊人次*门诊均次药品费用
                        if (orgid.startsWith(BmsConst.TRAD_CHINESE_DEPT_CODE)) {
                            //中医科门诊数据设置为中草药费
                            //中医，门诊人次*门诊均次药品费用
                            chindesSumdrugfee = mzrcSize.multiply(mzAvgdrugfeeYzb).setScale(0, RoundingMode.HALF_UP);
                        }else {
                            westernSumdrugfee = westernSumdrugfee.add(mzrcSize.multiply(mzAvgdrugfeeYzb));
                        }
                    }

                }
            }
            drugTotalFee.put(BmsConst.BUDGET_TOTALDRUGFEE_WESTERN, westernSumdrugfee);
            drugTotalFee.put(BmsConst.BUDGET_TOTALDRUGFEE_CHINESE, chindesSumdrugfee);
        }
        return drugTotalFee;
    }

    private TitleVo buildTitle(String title, String header, String width, String fixed, String key, int order) {
        TitleVo titleVo = new TitleVo();
        titleVo.setTitle(title);
        titleVo.setHeader(header);
        titleVo.setOrder(order);
        titleVo.setWidth(width);
        titleVo.setFixed(fixed);
        titleVo.setKey(key);
        return titleVo;
    }


    private void getAllLeafNodes(List<TitleVo> all, List<TitleVo> leaves) {
        // 递归遍历所有子节点
        for (TitleVo titleVo : all) {
            List<TitleVo> children = titleVo.getChildren();
            if (Objects.isNull(children)) {
                leaves.add(titleVo);
            } else {
                getAllLeafNodes(children, leaves);
            }
        }

    }
}
