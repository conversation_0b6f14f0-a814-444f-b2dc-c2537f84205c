package com.jp.med.bms.modules.bmsExecute.service.read.impl;

import com.github.pagehelper.PageHelper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetTaskDto;
import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsBudgetTaskReadMapper;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetTaskVo;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetFlowDto;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetFlowReadMapper;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetFlowVo;
import com.jp.med.common.constant.MedConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsExecuteBudgetFillingReadMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetFillingDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetFillingVo;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsExecuteBudgetFillingReadService;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Transactional(readOnly = true)
@Service
public class BmsExecuteBudgetFillingReadServiceImpl extends ServiceImpl<BmsExecuteBudgetFillingReadMapper, BmsExecuteBudgetFillingDto> implements BmsExecuteBudgetFillingReadService {

    @Autowired
    private BmsExecuteBudgetFillingReadMapper bmsExecuteBudgetFillingReadMapper;

    @Autowired
    private BmsDisposeBudgetFlowReadMapper bmsDisposeBudgetFlowReadMapper;

    @Autowired
    private BmsBudgetTaskReadMapper bmsBudgetTaskReadMapper;

    @Override
    public List<BmsExecuteBudgetFillingVo> queryList(BmsExecuteBudgetFillingDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return bmsExecuteBudgetFillingReadMapper.queryList(dto);
    }

    @Override
    public Map<String, Object> initPage(BmsExecuteBudgetFillingDto dto) {
        Map<String, Object> resultMap = new HashMap<>();
        BmsBudgetTaskDto budgetTaskDto = new BmsBudgetTaskDto();
        List<BmsBudgetTaskVo> vos = bmsBudgetTaskReadMapper.queryList(budgetTaskDto);
        resultMap.put("task", vos);
        return resultMap;
    }

}
