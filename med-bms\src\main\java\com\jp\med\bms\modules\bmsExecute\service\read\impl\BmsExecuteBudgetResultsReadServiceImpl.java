package com.jp.med.bms.modules.bmsExecute.service.read.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetDataDto;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetResultsDto;
import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsExecuteBudgetDataReadMapper;
import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsExecuteBudgetResultsReadMapper;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsExecuteBudgetResultsReadService;
import com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetResultsVo;
import com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo;
import com.jp.med.bms.modules.config.dto.BmsBudgetDeptMappingDto;
import com.jp.med.bms.modules.config.mapper.read.BmsBudgetDeptMappingReadMapper;
import com.jp.med.bms.modules.config.vo.BmsBudgetDeptMappingVo;
import com.jp.med.bms.modules.dispose.vo.TitleVo;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.ecs.EcsReimItemToBudgCfgDto;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.ecs.EcsReimFeignService;
import com.jp.med.common.vo.ecs.EcsReimItemToBudgCfgVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
@Slf4j
public class BmsExecuteBudgetResultsReadServiceImpl extends ServiceImpl<BmsExecuteBudgetResultsReadMapper, BmsExecuteBudgetResultsDto> implements BmsExecuteBudgetResultsReadService {

    @Autowired
    private BmsExecuteBudgetResultsReadMapper bmsExecuteBudgetResultsReadMapper;

    @Autowired
    private BmsExecuteBudgetDataReadMapper bmsExecuteBudgetDataReadMapper;

    @Autowired
    private BmsBudgetDeptMappingReadMapper bmsBudgetDeptMappingReadMapper;

    @Autowired
    private EcsReimFeignService ecsReimFeignService;

    @Override
    public List<BmsExecuteBudgetResultsVo> queryList(BmsExecuteBudgetResultsDto dto) {
        return bmsExecuteBudgetResultsReadMapper.queryList(dto);
    }

    @Override
    public List<BmsExecuteBudgetResultsVo> queryListNew(BmsExecuteBudgetResultsDto dto) {
        return bmsExecuteBudgetResultsReadMapper.queryListNew(dto);
    }

    @Override
    public List<BmsExecuteBudgetResultsVo> queryDeptBudget(BmsExecuteBudgetResultsDto dto) {
        //查询预算科室映射表
        BmsBudgetDeptMappingDto deptMappingParam = new BmsBudgetDeptMappingDto();
        deptMappingParam.setYear(dto.getYear());
        deptMappingParam.setFlag(MedConst.TYPE_1);
        List<BmsBudgetDeptMappingVo> bmsBudgetDeptMappingVos = bmsBudgetDeptMappingReadMapper.queryList(deptMappingParam);
        //如果预算科室预算映射有映射关系，则将映射后的科室添加到查询科室中
        List<String> needAdd = new ArrayList<>();
        dto.getOrgIds().stream().forEach(orgId -> {
            Optional<BmsBudgetDeptMappingVo> first = bmsBudgetDeptMappingVos.stream().filter(map -> StringUtils.equals(map.getSourceDept(), orgId)).findFirst();
            if (first.isPresent()) {
                needAdd.add(first.get().getTargetDept());
            }
        });
        dto.getOrgIds().addAll(needAdd);
        //如果培训费预算项为 YYZLXRWPXJF KYXMPXJF 将对应的科室也添加到查询科室中
        if (CollectionUtil.isNotEmpty(dto.getBudgetCodes()) && (dto.getBudgetCodes().contains("YYZLXRWPXJF") || dto.getBudgetCodes().contains("KYXMPXJF"))) {
            dto.getOrgIds().add("515001");
        }
        //查询租车费预算，将对应汇总科室添加到查询科室中
        dto.getOrgIds().add("501001");

        //对于培训和差旅直接传入budgetCode,直接查询即可
        if (StringUtils.isNotEmpty(dto.getBudgetCode()) || CollectionUtil.isNotEmpty(dto.getBudgetCodes())) {
            return bmsExecuteBudgetResultsReadMapper.queryDeptBudget(dto);
        }
        //查询当年报销项目对应预算项目
        EcsReimItemToBudgCfgDto itb = new EcsReimItemToBudgCfgDto();
        itb.setYear(dto.getYear());
        itb.setType(MedConst.TYPE_2);
        // 设置为null 不分页，查全部
        CommonResult<List<EcsReimItemToBudgCfgVo>> itbResult = ecsReimFeignService.listNoPage(itb);
        if (Objects.isNull(itbResult) || CollectionUtil.isEmpty(itbResult.getData())) {
            log.error("当前年份报销项目对应预算项目数据为空，年份:{}", dto.getYear());
            throw new AppException(dto.getYear() + "年份报销项目对应预算项目数据为空");
        }
        List<EcsReimItemToBudgCfgVo> data = itbResult.getData();
        //筛选出需要查询预算的项目
        List<EcsReimItemToBudgCfgVo> itbList = data.stream().filter(item -> dto.getReimItemCodes().contains(item.getReimItemCode())).collect(Collectors.toList());
        //分组  1：汇总 0：不汇总
        Map<String, List<EcsReimItemToBudgCfgVo>> itbMap = itbList.stream().collect(Collectors.groupingBy(EcsReimItemToBudgCfgVo::getBgtSummary));
        List<BmsExecuteBudgetResultsVo> resultsVos = new ArrayList<>();
        for (String s : itbMap.keySet()) {
            List<EcsReimItemToBudgCfgVo> ecsReimItemToBudgCfgVos = itbMap.get(s);
            List<String> bcs = ecsReimItemToBudgCfgVos.stream().map(EcsReimItemToBudgCfgVo::getBudgetCode).collect(Collectors.toList());
            BmsExecuteBudgetResultsDto sumParam = new BmsExecuteBudgetResultsDto();
            sumParam.setYear(dto.getYear());
            sumParam.setBudgetCodes(bcs);
            sumParam.setOrgIds(dto.getOrgIds());
            if (StringUtils.equals(s, MedConst.TYPE_1)) {               //查询报销项目总的预算
                resultsVos.addAll(bmsExecuteBudgetResultsReadMapper.queryDeptBudget2(sumParam));
            } else {
                resultsVos.addAll(bmsExecuteBudgetResultsReadMapper.queryDeptBudget(sumParam));
            }
        }
        return resultsVos;
    }

    /**
     * 查询预算报表推进进度
     *
     * @param dto
     * @return
     */
    @Override
    public Map<String, Object> queryBudgetReport(BmsExecuteBudgetDataDto dto) {

        if (StringUtils.isBlank(dto.getBudgetTaskCode())) {
            return new HashMap<>();
        }
        dto.setSqlAutowiredHospitalCondition(true);
        //获取对应预算任务年度的预算项目预算
        List<BmsExecuteBudgetResultsVo> bmsPayDetailVos = bmsExecuteBudgetResultsReadMapper.queryBudgetReport(dto);

        List<String> depts = new ArrayList<>();
        //获取 科室叶子节点信息
        if (ObjectUtils.isNotEmpty(dto.getDeptQuery())) {
            List<BmsOrgVo> bmsOrgVos = bmsExecuteBudgetDataReadMapper.queryOrg(dto);
            depts = bmsOrgVos.stream().map(item -> item.getOrgId()).collect(Collectors.toList());
        }

        //预算信息科室分组
        Map<String, List<BmsExecuteBudgetResultsVo>> collect = bmsPayDetailVos.stream().collect(Collectors.groupingBy(BmsExecuteBudgetResultsVo::getOrgId));

        //部门 预算/实际发生金额 的行数据
        List<Map<String, String>> resultRows = new ArrayList<>();

        //表头数据
        List<TitleVo> titles = bmsExecuteBudgetResultsReadMapper.queryTitle(dto);

        //map遍历，每次遍历的数据为同一个科室
        for (Map.Entry<String, List<BmsExecuteBudgetResultsVo>> stringListEntry : collect.entrySet()) {
            String key = stringListEntry.getKey();
            //科室信息条件筛选数据
            if (ObjectUtils.isNotEmpty(depts) && !depts.contains(key)) {
                continue;
            }
            List<BmsExecuteBudgetResultsVo> rows = stringListEntry.getValue();
            Map<String, String> row = new HashMap<>();
            row.put("orgId", rows.get(0).getOrgId());
            row.put("orgName", rows.get(0).getOrgName());
            rows.stream().forEach(item -> {
                BigDecimal allAmount = item.getBudgetAmount();
                BigDecimal fromHead = item.getActigAmt();
                BigDecimal ratio;
                if (allAmount.compareTo(BigDecimal.ZERO) == 0) {
                    ratio = BigDecimal.ZERO;
                } else {
                    ratio = fromHead.divide(allAmount, 4, RoundingMode.HALF_UP);
                }
                NumberFormat percent = NumberFormat.getPercentInstance();
                row.put(item.getBudgetCode(), allAmount.toString());
                row.put(new StringBuilder().append(item.getBudgetCode()).append("-Y").toString(), fromHead.toString());
                row.put(new StringBuilder().append(item.getBudgetCode()).append("-R").toString(), percent.format(ratio));
            });
            resultRows.add(row);
        }

        //结果按科室排序
        Collections.sort(resultRows, (o1, o2) -> {
            String pre = o1.get("orgId");
            String suf = o2.get("orgId");
            return pre.compareTo(suf);
        });

        Map<String, Object> data = new HashMap<>();
        data.put("data", resultRows);
        data.put("title", titles);

        return data;
    }

    @Override
    public List<BmsExecuteBudgetResultsVo> queryJXJFRemainingBudget(BmsExecuteBudgetResultsDto dto) {
        // 确保年份参数存在
        if (StringUtils.isEmpty(dto.getYear())) {
            // 如果未提供年份，使用当前年份
            dto.setYear(String.valueOf(LocalDate.now().getYear()));
        }

        // 查询进修经费剩余预算
        List<BmsExecuteBudgetResultsVo> resultList = bmsExecuteBudgetResultsReadMapper.queryJXJFRemainingBudget(dto);

        // 处理结果，确保返回值不为null
        if (resultList == null) {
            return new ArrayList<>();
        }

        return resultList;
    }

}
