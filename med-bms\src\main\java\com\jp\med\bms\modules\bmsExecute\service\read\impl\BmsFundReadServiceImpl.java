package com.jp.med.bms.modules.bmsExecute.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsFundReadMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsFundDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsFundVo;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsFundReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class BmsFundReadServiceImpl extends ServiceImpl<BmsFundReadMapper, BmsFundDto> implements BmsFundReadService {

    @Autowired
    private BmsFundReadMapper bmsFundReadMapper;

    @Override
    public List<BmsFundVo> queryList(BmsFundDto dto) {
        return bmsFundReadMapper.queryList(dto);
    }

}
