package com.jp.med.bms.modules.bmsExecute.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetTaskDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetAllocationVo;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeFlowDetailVo;

import java.util.List;

/**
 * 预算任务表
 * <AUTHOR>
 * @email -
 * @date 2023-10-19 17:18:28
 */
public interface BmsBudgetTaskWriteService extends IService<BmsBudgetTaskDto> {

    /**
     *
     * @param flowDetailVo 流程执行的节点
     * @param dto 预算任务信息
     * @param allocationVos 预算项目信息
     */
    void getExecute(BmsDisposeFlowDetailVo flowDetailVo, BmsBudgetTaskDto dto, List<BmsDisposeBudgetAllocationVo> allocationVos);

}

