package com.jp.med.bms.modules.bmsExecute.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetAdjustDto;

import java.util.List;

/**
 * 预算调整表
 * <AUTHOR>
 * @email -
 * @date 2023-06-01 18:20:31
 */
public interface BmsExecuteBudgetAdjustWriteService extends IService<BmsExecuteBudgetAdjustDto> {
    /**
     * 批量写入预算审批表
     * @param list
     */
    void insertBatch(List<BmsExecuteBudgetAdjustDto> list);

    /**
     * 删除预算调整信息
     * @param dto
     */
    void deleteAdjust(BmsExecuteBudgetAdjustDto dto);
}

