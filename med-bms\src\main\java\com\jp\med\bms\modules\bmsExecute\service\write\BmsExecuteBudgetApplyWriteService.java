package com.jp.med.bms.modules.bmsExecute.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetApplyDto;

/**
 * 预算调整申请表
 * <AUTHOR>
 * @email -
 * @date 2023-06-01 14:47:42
 */
public interface BmsExecuteBudgetApplyWriteService extends IService<BmsExecuteBudgetApplyDto> {

    /**
     * 新增预算申请 (保存)
     * @param dto
     */
    void saveBudgetApply(BmsExecuteBudgetApplyDto dto);

    /**
     * 预算审批
     * @param dto
     */
    void adjustAudit(BmsExecuteBudgetApplyDto dto);

    void saveBudgetApplyNew(BmsExecuteBudgetApplyDto dto);

    void removeByIdNew(BmsExecuteBudgetApplyDto dto);

    void uploadAdjustAtt(BmsExecuteBudgetApplyDto dto);
}

