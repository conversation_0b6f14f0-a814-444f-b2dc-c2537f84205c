package com.jp.med.bms.modules.bmsExecute.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetDataDto;

import java.util.List;

/**
 * 预算编制数据
 * <AUTHOR>
 * @email -
 * @date 2023-04-26 15:40:51
 */
public interface BmsExecuteBudgetDataWriteService extends IService<BmsExecuteBudgetDataDto> {
    /**
     * 批量写入
     * @param list
     */
    void insertBatch(List<BmsExecuteBudgetDataDto> list);

    /**
     * 保存数据
     * @param dto
     */
    void updateData(BmsExecuteBudgetDataDto dto);


    void resetStatus(BmsExecuteBudgetDataDto dto);
}

