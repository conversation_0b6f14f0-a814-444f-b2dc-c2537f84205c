package com.jp.med.bms.modules.bmsExecute.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetFillingDto;

import java.util.List;

/**
 * 预算填报状态表
 * <AUTHOR>
 * @email -
 * @date 2023-04-28 10:35:42
 */
public interface BmsExecuteBudgetFillingWriteService extends IService<BmsExecuteBudgetFillingDto> {
    /**
     * 批量新增
     * @param list
     */
    void saveFillingBatch(List<BmsExecuteBudgetFillingDto> list);

    /**
     * 添加预算编制表填报状态
     * @param fillingDto
     * @param init
     */
    void saveBudgetFilling(BmsExecuteBudgetFillingDto fillingDto, Boolean init);
}

