package com.jp.med.bms.modules.bmsExecute.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsBudgetAssetWriteMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetAssetDto;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsBudgetAssetWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 医疗设备购置预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:19:45
 */
@Service
@Transactional(readOnly = false)
public class BmsBudgetAssetWriteServiceImpl extends ServiceImpl<BmsBudgetAssetWriteMapper, BmsBudgetAssetDto> implements BmsBudgetAssetWriteService {
}
