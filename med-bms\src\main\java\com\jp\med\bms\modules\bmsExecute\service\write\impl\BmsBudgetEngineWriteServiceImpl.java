package com.jp.med.bms.modules.bmsExecute.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsBudgetEngineWriteMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetEngineDto;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsBudgetEngineWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 信息化建设项目预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:28:01
 */
@Service
@Transactional(readOnly = false)
public class BmsBudgetEngineWriteServiceImpl extends ServiceImpl<BmsBudgetEngineWriteMapper, BmsBudgetEngineDto> implements BmsBudgetEngineWriteService {
}
