package com.jp.med.bms.modules.bmsExecute.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsBudgetEquipmentWriteMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetEquipmentDto;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsBudgetEquipmentWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 通用设备预算
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 14:06:20
 */
@Service
@Transactional(readOnly = false)
public class BmsBudgetEquipmentWriteServiceImpl extends ServiceImpl<BmsBudgetEquipmentWriteMapper, BmsBudgetEquipmentDto> implements BmsBudgetEquipmentWriteService {
}
