package com.jp.med.bms.modules.bmsExecute.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsBudgetGoodsWriteMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetGoodsDto;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsBudgetGoodsWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 库房物资采购预算
 * <AUTHOR>
 * @email -
 * @date 2023-11-16 11:22:49
 */
@Service
@Transactional(readOnly = false)
public class BmsBudgetGoodsWriteServiceImpl extends ServiceImpl<BmsBudgetGoodsWriteMapper, BmsBudgetGoodsDto> implements BmsBudgetGoodsWriteService {
}
