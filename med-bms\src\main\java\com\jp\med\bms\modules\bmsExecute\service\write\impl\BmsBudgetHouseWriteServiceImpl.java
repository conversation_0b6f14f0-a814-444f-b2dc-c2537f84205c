package com.jp.med.bms.modules.bmsExecute.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsBudgetHouseWriteMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetHouseDto;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsBudgetHouseWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 工程类政府采购预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:34:43
 */
@Service
@Transactional(readOnly = false)
public class BmsBudgetHouseWriteServiceImpl extends ServiceImpl<BmsBudgetHouseWriteMapper, BmsBudgetHouseDto> implements BmsBudgetHouseWriteService {
}
