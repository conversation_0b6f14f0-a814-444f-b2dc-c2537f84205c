package com.jp.med.bms.modules.bmsExecute.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsBudgetKeyWriteMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetKeyDto;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsBudgetKeyWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 重点学专科预算表
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:15:52
 */
@Service
@Transactional(readOnly = false)
public class BmsBudgetKeyWriteServiceImpl extends ServiceImpl<BmsBudgetKeyWriteMapper, BmsBudgetKeyDto> implements BmsBudgetKeyWriteService {
}
