package com.jp.med.bms.modules.bmsExecute.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsBudgetServiceWriteMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetServiceDto;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsBudgetServiceWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 服务类采购预算
 * <AUTHOR>
 * @email -
 * @date 2023-11-16 11:03:01
 */
@Service
@Transactional(readOnly = false)
public class BmsBudgetServiceWriteServiceImpl extends ServiceImpl<BmsBudgetServiceWriteMapper, BmsBudgetServiceDto> implements BmsBudgetServiceWriteService {
}
