package com.jp.med.bms.modules.bmsExecute.service.write.impl;
import com.jp.med.bms.constant.BmsConst;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetDataDto;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetFillingDto;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsExecuteBudgetDataWriteMapper;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsExecuteBudgetFillingWriteMapper;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetAllocationDto;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeFlowDetailDto;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetAllocationReadMapper;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeFlowDetailReadMapper;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetAllocationVo;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeFlowDetailVo;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.ULIDUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsBudgetTaskWriteMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetTaskDto;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsBudgetTaskWriteService;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 预算任务表
 * <AUTHOR>
 * @email -
 * @date 2023-10-19 17:18:28
 */
@Service
@Transactional(readOnly = false)
public class BmsBudgetTaskWriteServiceImpl extends ServiceImpl<BmsBudgetTaskWriteMapper, BmsBudgetTaskDto> implements BmsBudgetTaskWriteService {

    @Autowired
    private BmsBudgetTaskWriteMapper bmsBudgetTaskWriteMapper;

    @Autowired
    private BmsDisposeBudgetAllocationReadMapper budgetAllocationReadMapper;

    @Autowired
    private BmsDisposeFlowDetailReadMapper bmsDisposeFlowDetailReadMapper;


    @Override
    public boolean save(BmsBudgetTaskDto dto){
        dto.setBudgetTaskCode(ULIDUtil.generate());
        dto.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        bmsBudgetTaskWriteMapper.insert(dto);
        // 查询开始执行的步骤
        BmsDisposeFlowDetailDto flowDetailDto = new BmsDisposeFlowDetailDto();
        flowDetailDto.setOrganizationOrder(1);
        flowDetailDto.setBudgetFlowCode(dto.getBudgetFlowCode());
        List<BmsDisposeFlowDetailVo> detailVos = bmsDisposeFlowDetailReadMapper.queryFlowDetail(flowDetailDto);
        if (detailVos.isEmpty()){
            throw new AppException("无效的流程模板");
        }
        // 查询每一个科室的编制项目
        BmsDisposeBudgetAllocationDto allocationDto = new BmsDisposeBudgetAllocationDto();
        allocationDto.setBudgetTableId(dto.getBudgetTableId());
        List<BmsDisposeBudgetAllocationVo> allocationVos = budgetAllocationReadMapper.queryAllocation(allocationDto);
        if (allocationVos.isEmpty()){
            throw new AppException("无效的预算表");
        }
        // 判断执行者
        BmsDisposeFlowDetailVo flowDetailVo = detailVos.get(0);
        getExecute(flowDetailVo, dto, allocationVos);
        // 写入编制数据
        List<BmsExecuteBudgetDataDto> bmsExecuteBudgetDataDtos = new ArrayList<>();
        for (BmsDisposeBudgetAllocationVo allocationVo : allocationVos) {
            BmsExecuteBudgetDataDto dataDto = new BmsExecuteBudgetDataDto();
            dataDto.setBudgetAmount(BigDecimal.ZERO);
            dataDto.setBudgetTaskCode(dto.getBudgetTaskCode());
            dataDto.setExecuteDept(allocationVo.getExecuteDept());
            dataDto.setOrgId(allocationVo.getOrgId());
            dataDto.setFlowDetailCode(flowDetailVo.getFlowDetailCode());
            dataDto.setHospitalId(dto.getHospitalId());
            dataDto.setBudgetCode(allocationVo.getBudgetCode());
            bmsExecuteBudgetDataDtos.add(dataDto);
        }
        BatchUtil.batch(bmsExecuteBudgetDataDtos, BmsExecuteBudgetDataWriteMapper.class);
        return true;
    }

    /**
     * 分配预算填报任务给科室
     * @param flowDetailVo 流程执行的节点
     * @param dto 预算任务信息
     * @param allocationVos 预算项目信息
     */
    @Override
    public void getExecute(BmsDisposeFlowDetailVo flowDetailVo, BmsBudgetTaskDto dto, List<BmsDisposeBudgetAllocationVo> allocationVos){
        String ptr = flowDetailVo.getPtr();
        Map<String, List<BmsDisposeBudgetAllocationVo>> collect = new HashMap<>();
        if (BmsConst.PTR_1.equals(ptr)){
            collect = allocationVos.stream().collect(Collectors.groupingBy(BmsDisposeBudgetAllocationVo::getExecuteDept));
        } else if(BmsConst.PTR_2.equals(ptr)){
            collect = allocationVos.stream().collect(Collectors.groupingBy(BmsDisposeBudgetAllocationVo::getOrgId));
        } else if(BmsConst.PTR_3.equals(ptr)){
            collect = allocationVos.stream().collect(Collectors.groupingBy(BmsDisposeBudgetAllocationVo::getCentralizedDept));
        }else {
            collect.put("-",new ArrayList<>());
        }
        List<BmsExecuteBudgetFillingDto> fillingDtos = new ArrayList<>();
        for (String dept : collect.keySet()) {
            BmsExecuteBudgetFillingDto fillingDto = new BmsExecuteBudgetFillingDto();
            fillingDto.setBudgetTaskCode(dto.getBudgetTaskCode());
            fillingDto.setFlowDetailCode(flowDetailVo.getFlowDetailCode());
            fillingDto.setOrgId(dept);
            fillingDto.setStatus(BmsConst.FILLING_0);
            fillingDtos.add(fillingDto);
        }
        BatchUtil.batch(fillingDtos, BmsExecuteBudgetFillingWriteMapper.class);
    }
}
