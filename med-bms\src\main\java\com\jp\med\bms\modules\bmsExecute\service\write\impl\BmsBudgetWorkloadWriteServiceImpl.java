package com.jp.med.bms.modules.bmsExecute.service.write.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetWorkloadDto;
import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsBudgetWorkloadReadMapper;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsBudgetWorkloadWriteMapper;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsBudgetWorkloadWriteService;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgDeptCrspVo;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetWorkloadVo;
import com.jp.med.bms.util.ValidateUtil;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 预算项目工作量情况
 *
 * <AUTHOR>
 * @email -
 * @date 2024-07-27 15:16:25
 */
@Service
@Transactional(readOnly = false)
@Slf4j
public class BmsBudgetWorkloadWriteServiceImpl extends ServiceImpl<BmsBudgetWorkloadWriteMapper, BmsBudgetWorkloadDto> implements BmsBudgetWorkloadWriteService {

    @Autowired
    private BmsBudgetWorkloadReadMapper bmsBudgetWorkloadReadMapper;

    @Autowired
    private BmsBudgetWorkloadWriteMapper bmsBudgetWorkloadWriteMapper;

    @Override
    public void uploadSRReport(BmsBudgetWorkloadDto dto) {
        if (dto.getFiles().length == 0) {
            throw new AppException("上传文件不能为空");
        }
        try {
            MultipartFile file = dto.getFiles()[0];
            //查询业务量指标表头

            //查询表头数据
            dto.setSqlAutowiredHospitalCondition(true);
            List<BmsBudgetWorkloadVo> titles = bmsBudgetWorkloadReadMapper.queryExcelTitle(dto);
            if (titles.isEmpty()) {
                throw new AppException("表头获取失败");
            }
            BmsBudgDeptCrspVo deptCrspVo = new BmsBudgDeptCrspVo();
            //设置年度，根据上传月度获取
            deptCrspVo.setBudgYear(dto.getUploadMonth().substring(0, 4));
            List<BmsBudgDeptCrspVo> bmsBudgDeptCrspVoList = bmsBudgetWorkloadReadMapper.queryBudgetDeptCrspList(deptCrspVo);
            Map<String, String> bmsBudgDeptCrspMaps = new HashMap<>();
            if (!bmsBudgDeptCrspVoList.isEmpty()) {
                bmsBudgDeptCrspMaps = bmsBudgDeptCrspVoList.stream().collect(Collectors.toMap(BmsBudgDeptCrspVo::getBudgRptDeptName, BmsBudgDeptCrspVo::getDeptCodg, (o1, o2) -> o2));
            }
            Map<String, String> titleMaps = titles.stream().collect(Collectors.toMap(BmsBudgetWorkloadVo::getBudgetName, BmsBudgetWorkloadVo::getBudgetCode, (o1, o2) -> o2));
            Map<String, String> titleCNMaps = titles.stream().collect(Collectors.toMap(BmsBudgetWorkloadVo::getBudgetCode, BmsBudgetWorkloadVo::getBudgetName, (o1, o2) -> o2));
            Map<String, String> finalBmsBudgDeptCrspMaps = bmsBudgDeptCrspMaps;

            List<String> targetColumn = Arrays.asList("MZRCKZFY", "CJFY");

            EasyExcel.read(file.getInputStream(), new AnalysisEventListener<Map<Integer, String>>() {
                int ROW = 1;
                Map<String, Integer> header = new HashMap<>();
                List<BmsBudgetWorkloadDto> wlDtos = new ArrayList<>();

                @Override
                public void invoke(Map<Integer, String> data, AnalysisContext analysisContext) {
                    if (ROW == 1) {
                        for (Map.Entry<Integer, String> entry : data.entrySet()) {
                            if (entry.getKey() == 0) {
                                continue;
                            }
                            header.put(titleMaps.get(entry.getValue()), entry.getKey());
                        }
                    } else {

                        BigDecimal total = null;
                        for (Map.Entry<String, Integer> entry : header.entrySet()) {
                            if (!ValidateUtil.isEmpty(data.get(entry.getValue()))) {
                                if (ValidateUtil.isEmpty(total)) {
                                    total = new BigDecimal(data.get(entry.getValue()));
                                } else {
                                    total = total.multiply(new BigDecimal(data.get(entry.getValue())));
                                }
                            }
                        }

                        for (Map.Entry<String, Integer> entry : header.entrySet()) {
                            if (StringUtils.isEmpty(finalBmsBudgDeptCrspMaps.get(data.get(0)))) {
                                //科室对照无数据跳过
                                continue;
                            }

                            if (StringUtils.isEmpty(data.get(entry.getValue()))) {
                                //无数据跳过
                                continue;
                            }

                            BmsBudgetWorkloadDto item = new BmsBudgetWorkloadDto();
                            item.setBudgetTaskCode(dto.getBudgetTaskCode());
                            item.setMonth(StringUtils.substring(dto.getUploadMonth(), 5, 7));
                            item.setBudgetCode(entry.getKey());
                            item.setBudgetName(titleCNMaps.get(entry.getKey()));
                            item.setBudgetTypeCode("SR");
                            if (targetColumn.contains(entry.getKey())) {
                                //均次费用,存储总费用
                                item.setActualAmt(total);
                            } else {
                                item.setActualAmt(new BigDecimal(data.get(entry.getValue())));
                            }
                            //获取转换后的科室编码（bms）
                            item.setDeptCode(finalBmsBudgDeptCrspMaps.get(data.get(0)));
                            wlDtos.add(item);
                        }
                    }
                    ROW++;
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    //删除旧数据
                    bmsBudgetWorkloadWriteMapper.deleteWorkLoads(dto);
                    BatchUtil.batch("insertWorkLoad", wlDtos, BmsBudgetWorkloadWriteMapper.class);
                    log.info("-------------dtos---------:" + wlDtos);
                }
            }).sheet().headRowNumber(-1).doRead();
        } catch (Exception e) {
            log.error("文件解析失败", e);
            throw new AppException("文件解析失败");
        }
    }
}
