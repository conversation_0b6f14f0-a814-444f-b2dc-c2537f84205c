package com.jp.med.bms.modules.bmsExecute.service.write.impl;
import com.jp.med.common.util.BatchUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsExecuteBudgetAdjustWriteMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetAdjustDto;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsExecuteBudgetAdjustWriteService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 预算调整表
 * <AUTHOR>
 * @email -
 * @date 2023-06-01 18:20:31
 */
@Service
@Transactional(readOnly = false)
public class BmsExecuteBudgetAdjustWriteServiceImpl extends ServiceImpl<BmsExecuteBudgetAdjustWriteMapper, BmsExecuteBudgetAdjustDto> implements BmsExecuteBudgetAdjustWriteService {

    @Autowired
    private BmsExecuteBudgetAdjustWriteMapper bmsExecuteBudgetAdjustWriteMapper;

    @Override
    public void insertBatch(List<BmsExecuteBudgetAdjustDto> list) {
        BatchUtil.batch(list, BmsExecuteBudgetAdjustWriteMapper.class);
    }

    @Override
    public void deleteAdjust(BmsExecuteBudgetAdjustDto dto) {
        bmsExecuteBudgetAdjustWriteMapper.deleteAdjust(dto);
    }
}
