package com.jp.med.bms.modules.bmsExecute.service.write.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.bms.constant.BmsConst;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetAdjustDto;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetApplyDto;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsExecuteBudgetApplyWriteMapper;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsExecuteBudgetAdjustWriteService;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsExecuteBudgetApplyWriteService;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeFlowModelDto;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeFlowModelReadMapper;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeFlowModelVo;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.dto.bpm.BpmProcessInstanceCreateReqDTO;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.BpmProcessInstanceFeignApi;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.common.util.ULIDUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 预算调整申请表
 * <AUTHOR>
 * @email -
 * @date 2023-06-01 14:47:42
 */
@Service
@Transactional(readOnly = false)
@Slf4j
public class BmsExecuteBudgetApplyWriteServiceImpl extends ServiceImpl<BmsExecuteBudgetApplyWriteMapper, BmsExecuteBudgetApplyDto> implements BmsExecuteBudgetApplyWriteService {

    @Autowired
    private BmsDisposeFlowModelReadMapper bmsDisposeFlowModelReadMapper;

    @Autowired
    private BmsExecuteBudgetApplyWriteMapper bmsExecuteBudgetApplyWriteMapper;

    @Autowired
    private BmsExecuteBudgetAdjustWriteService bmsExecuteBudgetAdjustWriteService;

    @Autowired
    private BpmProcessInstanceFeignApi bpmProcessInstanceFeignApi;

    @Override
    public void saveBudgetApply(BmsExecuteBudgetApplyDto dto) {
        BmsDisposeFlowModelDto modelDto = new BmsDisposeFlowModelDto();
        modelDto.setFlowModelId(dto.getFlowModelId());
        List<BmsDisposeFlowModelVo> modelVos = bmsDisposeFlowModelReadMapper.queryList(modelDto);
        if (modelVos.size() != 1){
            throw new AppException("未查找到对应流程或查询到多个流程,请联系管理员");
        }
        List<BmsExecuteBudgetAdjustDto> adjustList = dto.getAdjustList();
        if (CollectionUtils.isEmpty(adjustList)){
//            throw new AppException("没有提交要调整的预算，无需申请");
        }
        dto.setStatus(BmsConst.FILLING_0);
        String generate = ULIDUtil.generate();
        if ("1".equals(dto.getOperationType())){
            dto.setBusinessKey(generate);
            dto.setStatus(BmsConst.FILLING_1);
        }
        try {
            if (Objects.isNull(dto.getBudgetApplyId())){
                bmsExecuteBudgetApplyWriteMapper.insert(dto);
            }else {
                bmsExecuteBudgetApplyWriteMapper.updateById(dto);
            }
            BmsExecuteBudgetAdjustDto adjustDto = new BmsExecuteBudgetAdjustDto();
            adjustDto.setBudgetApplyId(dto.getBudgetApplyId());
//            bmsExecuteBudgetAdjustWriteService.deleteAdjust(adjustDto);
//            adjustList.forEach(item -> {
//                item.setBudgetApplyId(dto.getBudgetApplyId());
//                if (!Objects.isNull(item.getFile())){
//                    OSSUtil.removeFile(OSSConst.BUCKET_BMS, StringUtils.isEmpty(item.getAttachment()) ? "1----" : item.getAttachment());
//                    String uploadFile = OSSUtil.uploadFile(OSSConst.BUCKET_BMS, "/", item.getFile());
//                    item.setAttachment(uploadFile);
//                }
//            });
            bmsExecuteBudgetAdjustWriteService.insertBatch(adjustList);


        } catch (Exception e){
            log.error(e.toString());
            throw new AppException("申请失败，请联系管理员");
        }
        if ("1".equals(dto.getOperationType())) {
//            ActivitiUtil.deployByOSS(OSSConst.BUCKET_ACTIVITI, modelVos.get(0).getModelCase(), "审批流程");
            HashMap<String, Object> hashMap = new HashMap<>();
//            hashMap.put("isValidate", "1");
//            hashMap.put("isDip", "1");
//            hashMap.put("isDrg", "1");
//            hashMap.put("isCd", "1");
//            hashMap.put("isScore", "1");
//            hashMap.put("user", "3");
//            ActivitiUtil.startProcess(generate, modelVos.get(0).getFlowModeCode(), hashMap);
        }
    }

    @Override
    public void saveBudgetApplyNew(BmsExecuteBudgetApplyDto dto) {
        List<BmsExecuteBudgetAdjustDto> adjustList = dto.getAdjustList();
        if (CollectionUtils.isEmpty(adjustList)){
            throw new AppException("没有提交要调整的预算，无需申请");
        }
        dto.setUsername(dto.getEmpName());
        String generate = ULIDUtil.generate();
        dto.setBusinessKey(generate);
        dto.setStatus(BmsConst.FILLING_1);
        try {
            if (Objects.isNull(dto.getBudgetApplyId())){
                bmsExecuteBudgetApplyWriteMapper.insert(dto);
            }else {
                bmsExecuteBudgetApplyWriteMapper.updateById(dto);
            }
            BmsExecuteBudgetAdjustDto adjustDto = new BmsExecuteBudgetAdjustDto();
            adjustDto.setBudgetApplyId(dto.getBudgetApplyId());
            bmsExecuteBudgetAdjustWriteService.deleteAdjust(adjustDto);
            adjustList.forEach(item -> {
                item.setBudgetApplyId(dto.getBudgetApplyId());
                if (!Objects.isNull(item.getFile())){
                    String uploadFile = OSSUtil.uploadFile(OSSConst.BUCKET_BMS, "/budget/apply/", item.getFile());
                    item.setAttachment(uploadFile);
                }
            });
            bmsExecuteBudgetAdjustWriteService.insertBatch(adjustList);
        } catch (Exception e){
            log.error(e.toString());
            throw new AppException("申请失败，请联系管理员");
        }

        // ------------BPM流程测试--------------
        // 发起BPM流程
        Map<String, Object> processInstanceVariables = new HashMap<>();
        // 获取参数 是否临床科室的培训报销 是否采管部门 是否职能科室 是否金额高于5W
        dto.getBpmParams().forEach((key, value) -> {
            processInstanceVariables.put(key, value);
        });
//        processInstanceVariables.put(OSSConst.APP_ATT_PATHS, dto.getAtt());
//        processInstanceVariables.put(OSSConst.APP_ATT_NAMES, dto.getAttName());
//        processInstanceVariables.put(OSSConst.APP_ATT_BUCKET_NAME, OSSConst.BUCKET_ECS);

        String userCode = dto.getSysUser().getHrmUser().getEmpCode();
        // 获取审核流程
//        String PROCESS_KEY = getProcessName(dto.getType(), dto.getBpmParams());
        String PROCESS_KEY = "BMS_BUDGET_APPLY";
        if (StringUtils.isEmpty(PROCESS_KEY)) {
            throw new AppException("未获取到对应审核流程！");
        }

        try {
            val bpmProcessInstanceCreateReqDTO = new BpmProcessInstanceCreateReqDTO();
            bpmProcessInstanceCreateReqDTO
                    .setUserId(userCode)
                    .setProcessDefinitionKey(PROCESS_KEY)
                    .setVariables(processInstanceVariables)
                    .setBusinessKey(String.valueOf(dto.getBudgetApplyId()));
            // .setStartUserSelectAssignees(dto.getStartUserSelectAssignees());
            CommonFeignResult processInstance = bpmProcessInstanceFeignApi
                    .createProcessInstance(bpmProcessInstanceCreateReqDTO);
            if (!StringUtils.equals(processInstance.get("code").toString(), "200")) {
                throw new AppException("生成BPM流程异常");
            }
            String processInstanceId = processInstance.get("data").toString();
            // 将工作流编号，更新到报销中
            LambdaUpdateWrapper<BmsExecuteBudgetApplyDto> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(BmsExecuteBudgetApplyDto::getProcessInstanceId, processInstanceId)
                    .eq(BmsExecuteBudgetApplyDto::getBudgetApplyId, dto.getBudgetApplyId());
            bmsExecuteBudgetApplyWriteMapper.update(null, updateWrapper);
        } catch (Exception e) {
            log.error("BPM流程生成失败", e);
            throw new AppException("BPM流程生成失败");
        }

    }

    @Override
    public void uploadAdjustAtt(BmsExecuteBudgetApplyDto dto) {



        // 附件
        if (CollectionUtil.isNotEmpty(dto.getAttFiles())) {
            List<List<String>> ossPaths = OSSUtil.getOSSPaths(dto.getAttFiles(), OSSConst.BUCKET_BMS, "apply/item/");
            BmsExecuteBudgetApplyDto bmsExecuteBudgetApplyDto = bmsExecuteBudgetApplyWriteMapper.selectById(dto.getBudgetApplyId());
            if (StrUtil.isNotBlank(bmsExecuteBudgetApplyDto.getAtt())){
                bmsExecuteBudgetApplyDto.setAtt(bmsExecuteBudgetApplyDto.getAtt()+","+String.join(",", ossPaths.get(0)));
                bmsExecuteBudgetApplyDto.setAttName(bmsExecuteBudgetApplyDto.getAttName()+","+String.join(",", ossPaths.get(1)));
            }else{
                bmsExecuteBudgetApplyDto.setAtt(String.join(",", ossPaths.get(0)));
                bmsExecuteBudgetApplyDto.setAttName(String.join(",", ossPaths.get(1)));
            }

            bmsExecuteBudgetApplyWriteMapper.updateById(bmsExecuteBudgetApplyDto);

        }
    }

    @Override
    public void removeByIdNew(BmsExecuteBudgetApplyDto dto) {
        bmsExecuteBudgetAdjustWriteService.remove(
                Wrappers.lambdaQuery(
                        BmsExecuteBudgetAdjustDto.class
                ).eq(BmsExecuteBudgetAdjustDto::getBudgetApplyId,dto.getBudgetApplyId())
        );
        bmsExecuteBudgetApplyWriteMapper.delete(
                Wrappers.lambdaQuery(BmsExecuteBudgetApplyDto.class)
                        .eq(BmsExecuteBudgetApplyDto::getBudgetApplyId,dto.getBudgetApplyId())
                        .eq(BmsExecuteBudgetApplyDto::getProcessInstanceId,dto.getProcessInstanceId())
        );
        //删除对应流程实例
        Map<String,Object> variables = new HashMap<>();
        variables.put("isRunning",StringUtils.equals(dto.getStatus(),"3")? MedConst.TYPE_0:MedConst.TYPE_1);
        val bpmProcessInstanceCreateReqDTO = new BpmProcessInstanceCreateReqDTO();
        bpmProcessInstanceCreateReqDTO
                .setUserId(dto.getSysUser().getHrmUser().getEmpCode())
                .setVariables(variables)
                .setProcessId(dto.getProcessInstanceId());
        CommonFeignResult processIns = bpmProcessInstanceFeignApi.deleteRunningProcessInstance(bpmProcessInstanceCreateReqDTO);
        if (!StringUtils.equals(processIns.get("code").toString(),"200")) {
            throw new AppException("删除BPM流程异常");
        }
    }

    @Override
    public void adjustAudit(BmsExecuteBudgetApplyDto dto) {
//        String user = ActivitiUtil.getNextTask(dto.getBusinessKey(), dto.getTaskId());
//        if (StringUtils.isEmpty(user)){
//            bmsExecuteBudgetApplyWriteMapper.updateAdjustResult(dto);
//        }
//        if (ActivitiUtil.isAssignee(dto.getTaskId(), dto.getUsername())){
//            ActivitiUtil.completeTaskById(dto.getTaskId());
//        }else {
//            throw new AppException("未拥有审核权限");
//        }
    }
}
