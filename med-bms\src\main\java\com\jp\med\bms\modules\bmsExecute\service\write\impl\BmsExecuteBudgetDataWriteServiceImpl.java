package com.jp.med.bms.modules.bmsExecute.service.write.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.bms.constant.BmsConst;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetTaskDto;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetDataDto;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetFillingDto;
import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsBudgetTaskReadMapper;
import com.jp.med.bms.modules.bmsExecute.mapper.read.BmsExecuteBudgetFillingReadMapper;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsExecuteBudgetDataWriteMapper;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsExecuteBudgetFillingWriteMapper;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsBudgetTaskWriteService;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsExecuteBudgetDataWriteService;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetTaskVo;
import com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetFillingVo;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetAllocationDto;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeFlowDetailDto;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetAllocationReadMapper;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeFlowDetailReadMapper;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetAllocationVo;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeFlowDetailVo;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import io.seata.common.util.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 预算编制数据
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-26 15:40:51
 */
@Service
@Transactional(readOnly = false)
public class BmsExecuteBudgetDataWriteServiceImpl extends ServiceImpl<BmsExecuteBudgetDataWriteMapper, BmsExecuteBudgetDataDto> implements BmsExecuteBudgetDataWriteService {

    @Autowired
    private BmsExecuteBudgetDataWriteMapper bmsExecuteBudgetDataWriteMapper;

    @Autowired
    private BmsExecuteBudgetFillingReadMapper bmsExecuteBudgetFillingReadMapper;

    @Autowired
    private BmsExecuteBudgetFillingWriteMapper bmsExecuteBudgetFillingWriteMapper;

    @Autowired
    private BmsDisposeFlowDetailReadMapper bmsDisposeFlowDetailReadMapper;

    @Autowired
    private BmsBudgetTaskReadMapper bmsBudgetTaskReadMapper;

    @Autowired
    private BmsBudgetTaskWriteService bmsBudgetTaskWriteService;

    @Autowired
    private BmsDisposeBudgetAllocationReadMapper budgetAllocationReadMapper;


    @Override
    public void insertBatch(List<BmsExecuteBudgetDataDto> list) {
        BatchUtil.batch(list, BmsExecuteBudgetDataWriteMapper.class);
    }

    @Override
    public void updateData(BmsExecuteBudgetDataDto dto) {
        //修改数据
        BatchUtil.batch("updateData", dto.getUpdateList(), BmsExecuteBudgetDataWriteMapper.class);
        //如果是保存则修改执行状态
        if ("2".equals(dto.getType())) {
            dto.setStatus(BmsConst.FILLING_3);
            bmsExecuteBudgetDataWriteMapper.updateFillingType(dto);
            //查询该流程节点是否已经执行完毕
            BmsExecuteBudgetFillingDto fillingDto = new BmsExecuteBudgetFillingDto();
            fillingDto.setFlowDetailCode(dto.getFlowDetailCode());
            fillingDto.setBudgetTaskCode(dto.getBudgetTaskCode());
            List<BmsExecuteBudgetFillingVo> fillingVos = bmsExecuteBudgetFillingReadMapper.queryImplementation(fillingDto);
            if (!Objects.isNull(fillingVos) && !fillingVos.isEmpty()){
                return;
            }
            // 查询任务
            BmsBudgetTaskDto budgetTaskDto = new BmsBudgetTaskDto();
            budgetTaskDto.setBudgetTaskCode(dto.getBudgetTaskCode());
            List<BmsBudgetTaskVo> vos = bmsBudgetTaskReadMapper.queryList(budgetTaskDto);
            BmsBudgetTaskDto taskDto = new BmsBudgetTaskDto();
            BeanUtils.copyProperties(vos.get(0), taskDto);
            //查询查询下一个步骤
            BmsDisposeFlowDetailDto flowDetailDto = new BmsDisposeFlowDetailDto();
            flowDetailDto.setBudgetFlowCode(vos.get(0).getBudgetFlowCode());    //78ef6bc8-1f8b-446c-84d3-585e227ff95d
            flowDetailDto.setOrganizationOrder(dto.getOrganizationOrder() + 1); //4
            List<BmsDisposeFlowDetailVo> detailVos = bmsDisposeFlowDetailReadMapper.queryFlowDetail(flowDetailDto);
            if (!Objects.isNull(detailVos) && !detailVos.isEmpty()) {
                // 查询每一个科室的编制项目
                BmsDisposeBudgetAllocationDto allocationDto = new BmsDisposeBudgetAllocationDto();
                allocationDto.setBudgetTableId(taskDto.getBudgetTableId()); //22
                List<BmsDisposeBudgetAllocationVo> allocationVos = budgetAllocationReadMapper.queryAllocation(allocationDto);
                dto.setFlowDetailCodeNext(detailVos.get(0).getFlowDetailCode());    //f21e6b23-e404-4dae-b3c9-460ae81ed5f9
                int nextNode = bmsExecuteBudgetDataWriteMapper.insertNextNode(dto);
                /*if (allocationVos.size() != nextNode){
                    throw new AppException("提交失败");
                }*/
                bmsBudgetTaskWriteService.getExecute(detailVos.get(0), taskDto, allocationVos);
            }
        }
    }

    @Override
    public void resetStatus(BmsExecuteBudgetDataDto dto) {
        //校验参数
        if (ObjectUtils.isNull(dto.getBudgetFillingId())) {
            throw new AppException("填报id不能为空");
        }

        //查询当前环节所有budgetFill 是否填报完成
        LambdaQueryWrapper<BmsExecuteBudgetFillingDto> budgetFillWrapper = Wrappers.lambdaQuery();
        budgetFillWrapper.eq(BmsExecuteBudgetFillingDto::getFlowDetailCode,dto.getFlowDetailCode())
                .eq(BmsExecuteBudgetFillingDto::getBudgetTaskCode,dto.getBudgetTaskCode());
        List<BmsExecuteBudgetFillingDto> fillingDtos = bmsExecuteBudgetFillingReadMapper.selectList(budgetFillWrapper);

        Optional<BmsExecuteBudgetFillingDto> first = fillingDtos.stream().filter(e -> e.getBudgetFillingId().equals(dto.getBudgetFillingId())).findFirst();
        if (first.isEmpty()) {
            throw new AppException("当前预算填报不存在");
        }

        BmsExecuteBudgetFillingDto curDto = first.get();
        if (StringUtils.equals(curDto.getStatus(),BmsConst.FILLING_0)) {
            throw new AppException("当前预算填报为可填报状态");
        }

        //存在未填报环节
        List<BmsExecuteBudgetFillingDto> collect = fillingDtos.stream()
                .filter(e -> StringUtils.equals(e.getStatus(), BmsConst.FILLING_0))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(collect)) {
            throw new AppException("当前填报已进入下一个环节，不能回退可填报状态");
        }

        //更新状态
        LambdaUpdateWrapper<BmsExecuteBudgetFillingDto> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(BmsExecuteBudgetFillingDto::getStatus,BmsConst.FILLING_0)
                .eq(BmsExecuteBudgetFillingDto::getBudgetFillingId,dto.getBudgetFillingId());
        bmsExecuteBudgetFillingWriteMapper.update(null,updateWrapper);
    }
}
