package com.jp.med.bms.modules.bmsExecute.service.write.impl;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetDataDto;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsExecuteBudgetDataWriteService;
import com.jp.med.bms.modules.bmsOrg.dto.BmsOrgDto;
import com.jp.med.bms.modules.bmsOrg.mapper.read.BmsOrgReadMapper;
import com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetAllocationDto;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetFlowDto;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetAllocationReadMapper;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetFlowReadMapper;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetAllocationVo;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetFlowVo;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.BatchUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsExecuteBudgetFillingWriteMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetFillingDto;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsExecuteBudgetFillingWriteService;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 预算填报状态表
 * <AUTHOR>
 * @email -
 * @date 2023-04-28 10:35:42
 */
@Service
@Transactional(readOnly = false)
public class BmsExecuteBudgetFillingWriteServiceImpl extends ServiceImpl<BmsExecuteBudgetFillingWriteMapper, BmsExecuteBudgetFillingDto> implements BmsExecuteBudgetFillingWriteService {


    @Autowired
    private BmsOrgReadMapper bmsOrgReadMapper;

    @Autowired
    private BmsDisposeBudgetFlowReadMapper bmsDisposeBudgetFlowReadMapper;

    @Autowired
    private BmsDisposeBudgetAllocationReadMapper bmsDisposeBudgetAllocationReadMapper;

    @Autowired
    private BmsExecuteBudgetDataWriteService bmsExecuteBudgetDataWriteService;

    @Override
    public void saveFillingBatch(List<BmsExecuteBudgetFillingDto> list) {
        BatchUtil.batch(list, BmsExecuteBudgetFillingWriteMapper.class);
    }

    @Override
    public void saveBudgetFilling(BmsExecuteBudgetFillingDto fillingDto, Boolean init) {
//        String deptType = "";
//        BmsOrgDto orgDto = new BmsOrgDto();
//        orgDto.setOrgId(fillingDto.getOrgId());
//        List<BmsOrgVo> orgVos = bmsOrgReadMapper.queryDept(orgDto);
//        //查询流程对应的编制表
//        BmsDisposeBudgetFlowDto flowDto = new BmsDisposeBudgetFlowDto();
//        flowDto.setBudgetFlowId(fillingDto.getBudgetFlowId());
//        List<BmsDisposeBudgetFlowVo> flowVos = bmsDisposeBudgetFlowReadMapper.queryList(flowDto);
//        //查询流程的编制项目
//        BmsDisposeBudgetAllocationDto allocationDto = new BmsDisposeBudgetAllocationDto();
////        allocationDto.setBudgetTableId(flowVos.get(0).getBudgetTableId());
//        List<BmsDisposeBudgetAllocationVo> allocationVos = bmsDisposeBudgetAllocationReadMapper.queryAllocation(allocationDto);
//        List<BmsExecuteBudgetFillingDto> fillingDtos = new ArrayList<>();
//        if (!Objects.isNull(orgVos) && orgVos.size() > 0){
//            deptType = orgVos.get(0).getDeptType();
//            //归口科室
//            Set<String> dept = new HashSet<>();
//            //预算科室
//            Set<String> org = new HashSet<>();
//            allocationVos.forEach(item -> {
//                dept.add(item.getCentralizedDept());
//                org.add(item.getOrgId());
//            });
//            //归口科室
//            if (MedConst.BUDGET_DEPT_TYPE_2.equals(deptType)) {
//                for (String s : org) {
//                    BmsExecuteBudgetFillingDto budgetFillingDto = new BmsExecuteBudgetFillingDto();
//                    budgetFillingDto.setStatus("0");
//                    budgetFillingDto.setFlowDetailId(fillingDto.getFlowDetailId());
//                    //预算科室填报状态添加
//                    budgetFillingDto.setOrgId(s);
//                    fillingDtos.add(budgetFillingDto);
//                }
//            }
//            //预算科室
//            if (MedConst.BUDGET_DEPT_TYPE_1.equals(deptType)) {
//                for (String s : dept) {
//                    BmsExecuteBudgetFillingDto budgetFillingDto = new BmsExecuteBudgetFillingDto();
//                    budgetFillingDto.setStatus("0");
//                    budgetFillingDto.setFlowDetailId(fillingDto.getFlowDetailId());
//                    //归口科室填报状态添加
//                    budgetFillingDto.setOrgId(s);
//                    fillingDtos.add(budgetFillingDto);
//                }
//            }
//        }else {
//            fillingDto.setStatus("0");
//            fillingDtos.add(fillingDto);
//        }
//
//        if (init) {
//            //写入预算编制数据
//            List<BmsExecuteBudgetDataDto> dataDtos = new ArrayList<>();
//            String finalDeptType = deptType;
//            allocationVos.forEach(item -> {
//                BmsExecuteBudgetDataDto dataDto = new BmsExecuteBudgetDataDto();
//                dataDto.setBudgetProjId(item.getBudgetProjId());
//                dataDto.setBudgetFlowId(fillingDto.getBudgetFlowId());
//                dataDto.setOrgId(item.getOrgId());
//                if (MedConst.BUDGET_DEPT_TYPE_1.equals(finalDeptType)) {
//                    dataDto.setExecuteDept(item.getCentralizedDept());
//                } else if (MedConst.BUDGET_DEPT_TYPE_2.equals(finalDeptType)) {
//                    dataDto.setExecuteDept(item.getOrgId());
//                } else {
//                    dataDto.setExecuteDept(fillingDto.getOrgId());
//                }
//                dataDto.setBudgetTableId(item.getBudgetTableId());
//                dataDto.setFlowDetailId(fillingDto.getFlowDetailId());
//                dataDto.setHospitalId(fillingDto.getHospitalId());
//                dataDtos.add(dataDto);
//            });
//            bmsExecuteBudgetDataWriteService.insertBatch(dataDtos);
//        }
//        saveFillingBatch(fillingDtos);
    }
}
