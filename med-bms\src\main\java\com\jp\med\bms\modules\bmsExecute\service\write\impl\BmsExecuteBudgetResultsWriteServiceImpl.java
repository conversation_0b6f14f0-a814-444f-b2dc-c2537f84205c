package com.jp.med.bms.modules.bmsExecute.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsExecuteBudgetResultsWriteMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetResultsDto;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsExecuteBudgetResultsWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 预算结果
 * <AUTHOR>
 * @email -
 * @date 2023-06-01 10:11:35
 */
@Service
@Transactional(readOnly = false)
public class BmsExecuteBudgetResultsWriteServiceImpl extends ServiceImpl<BmsExecuteBudgetResultsWriteMapper, BmsExecuteBudgetResultsDto> implements BmsExecuteBudgetResultsWriteService {
}
