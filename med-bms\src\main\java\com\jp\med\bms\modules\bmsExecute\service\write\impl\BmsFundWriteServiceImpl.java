package com.jp.med.bms.modules.bmsExecute.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsFundWriteMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsFundDto;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsFundWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 科研教学经费预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 19:57:30
 */
@Service
@Transactional(readOnly = false)
public class BmsFundWriteServiceImpl extends ServiceImpl<BmsFundWriteMapper, BmsFundDto> implements BmsFundWriteService {
}
