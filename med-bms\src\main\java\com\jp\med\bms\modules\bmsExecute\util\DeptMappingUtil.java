package com.jp.med.bms.modules.bmsExecute.util;

import com.jp.med.bms.modules.bmsExecute.model.DeptMapping;

import java.util.*;

/**
 * 科室映射工具类
 * 用于管理科室映射数据和提供相关方法
 */
public class DeptMappingUtil {

    /**
     * 科室映射数据
     */
    private static final List<DeptMapping> DEPT_MAPPINGS = initDeptMappings();
    
    /**
     * 科室映射缓存 - 预算科室名称 -> 映射对象
     */
    private static final Map<String, DeptMapping> DEPT_MAPPING_CACHE = initDeptMappingCache();
    
    /**
     * 初始化科室映射数据
     * @return 科室映射列表
     */
    private static List<DeptMapping> initDeptMappings() {
        List<DeptMapping> mappings = new ArrayList<>();
        
        // 添加所有科室映射
        mappings.add(new DeptMapping("口腔科", Arrays.asList("口腔科门诊"), Arrays.asList("门诊口腔科")));
        mappings.add(new DeptMapping("精神科", Arrays.asList("精神科门诊"), Arrays.asList("精神科门诊")));
        mappings.add(new DeptMapping("急诊科", Arrays.asList("急诊科"), Arrays.asList("急诊科")));
        mappings.add(new DeptMapping("健康体检科", Arrays.asList("健康体检科"), Arrays.asList("健康体检科")));
        mappings.add(new DeptMapping("便民门诊", Arrays.asList("便民门诊"), Arrays.asList("便民门诊")));
        mappings.add(new DeptMapping("门诊部", Arrays.asList("门诊部", "门诊办公室"), Arrays.asList("门诊部办公室")));
        mappings.add(new DeptMapping("肛肠科", Arrays.asList("肛肠科门诊"), Arrays.asList("门诊肛肠科")));
        mappings.add(new DeptMapping("发热门诊", Arrays.asList("发热门诊"), Arrays.asList("发热门诊")));
        mappings.add(new DeptMapping("全科医疗科", Arrays.asList("全科医疗科住院"), Arrays.asList("普内科(住院)")));
        mappings.add(new DeptMapping("呼吸与危重症医学科", Arrays.asList("呼吸与危重症医学科住院"), Arrays.asList("呼吸内科(住院)")));
        mappings.add(new DeptMapping("消化内科", Arrays.asList("消化内科住院"), Arrays.asList("消化内科(住院)")));
        mappings.add(new DeptMapping("神经内科", Arrays.asList("神经内科住院"), Arrays.asList("神经内科(住院)")));
        mappings.add(new DeptMapping("心血管内科", Arrays.asList("心血管内科住院"), Arrays.asList("心血管内科(住院)")));
        mappings.add(new DeptMapping("肾内、内分泌科", Arrays.asList("肾内、内分泌科住院"), Arrays.asList("肾内、内分泌科(住院)")));
        mappings.add(new DeptMapping("老年病科", Arrays.asList("老年病科住院"), Arrays.asList("老年病科(住院)")));
        mappings.add(new DeptMapping("大内、大外科", Arrays.asList("大内科", "大外科"), Arrays.asList("大内、外科")));
        mappings.add(new DeptMapping("胃肠外科", Arrays.asList("胃肠外科住院"), Arrays.asList("胃肠外科(住院)")));
        mappings.add(new DeptMapping("肝胆胰外科", Arrays.asList("肝胆胰外科"), Arrays.asList("肝胆胰外科(住院)")));
        mappings.add(new DeptMapping("神经外科", Arrays.asList("神经外科住院"), Arrays.asList("神经外科(住院)")));
        mappings.add(new DeptMapping("骨科", Arrays.asList("骨科一病区", "骨科二病区", "骨科三病区"), Arrays.asList("骨科1-脊柱外科(住院)")));
        mappings.add(new DeptMapping("泌尿外科", Arrays.asList("泌尿外科住院"), Arrays.asList("泌尿外科(住院)")));
        mappings.add(new DeptMapping("胸外科", Arrays.asList("胸外科住院"), Arrays.asList("胸外科(住院)")));
        mappings.add(new DeptMapping("妇产科", Arrays.asList("妇产科住院"), Arrays.asList("产科(住院)")));
        mappings.add(new DeptMapping("儿科", Arrays.asList("儿科住院"), Arrays.asList("儿科(住院)")));
        mappings.add(new DeptMapping("眼科", Arrays.asList("眼科住院"), Arrays.asList("眼科(住院)")));
        mappings.add(new DeptMapping("耳鼻咽喉头颈外科", Arrays.asList("耳鼻咽喉头颈外科（护理）"), Arrays.asList("耳鼻咽喉科(住院)")));
        mappings.add(new DeptMapping("眼耳鼻咽喉头颈外科住院", Arrays.asList("眼耳鼻咽喉头颈外科住院"), Arrays.asList()));
        mappings.add(new DeptMapping("皮肤科", Arrays.asList("皮肤科住院"), Arrays.asList("门诊皮肤科")));
        mappings.add(new DeptMapping("感染性疾病科", Arrays.asList("感染性疾病科住院"), Arrays.asList("感染性疾病科(住院)")));
        mappings.add(new DeptMapping("肿瘤科、安宁疗护科", Arrays.asList("肿瘤科、安宁疗护科住院"), Arrays.asList("肿瘤科（住院）")));
        mappings.add(new DeptMapping("康复医学科", Arrays.asList("康复医学科住院"), Arrays.asList("康复医学科(住院)")));
        mappings.add(new DeptMapping("重症医学科", Arrays.asList("重症医学科住院"), Arrays.asList("重症医学科(住院)")));
        mappings.add(new DeptMapping("疼痛科", Arrays.asList("疼痛科住院"), Arrays.asList("疼痛科")));
        mappings.add(new DeptMapping("中医科", Arrays.asList("中医科住院"), Arrays.asList("门诊中医科")));
        mappings.add(new DeptMapping("病理科", Arrays.asList("病理科"), Arrays.asList("病理科")));
        mappings.add(new DeptMapping("检验科", Arrays.asList("检验科"), Arrays.asList("检验科")));
        mappings.add(new DeptMapping("输血科", Arrays.asList("输血科"), Arrays.asList("输血科")));
        mappings.add(new DeptMapping("放射科", Arrays.asList("放射科"), Arrays.asList("放射科","普放室")));
        mappings.add(new DeptMapping("药剂科", Arrays.asList("药剂科"), Arrays.asList("药剂科(办)")));
        mappings.add(new DeptMapping("超声科", Arrays.asList("超声科"), Arrays.asList("医学影像二科")));
        mappings.add(new DeptMapping("手术室", Arrays.asList("手术室", "麻醉科"), Arrays.asList("麻醉科手术室")));
        mappings.add(new DeptMapping("消毒供应室", Arrays.asList("消毒供应室"), Arrays.asList("供应室")));
        mappings.add(new DeptMapping("病案科", Arrays.asList("病案科"), Arrays.asList("病案科")));
        mappings.add(new DeptMapping("医院办公室", Arrays.asList("医院办公室"), Arrays.asList("院办公室")));
        mappings.add(new DeptMapping("党委办公室", Arrays.asList("党委办公室"), Arrays.asList("党办")));
        mappings.add(new DeptMapping("宣传科", Arrays.asList("宣传科"), Arrays.asList("宣传科")));
        mappings.add(new DeptMapping("审计科", Arrays.asList("审计科"), Arrays.asList("审计科")));
        mappings.add(new DeptMapping("人力资源部", Arrays.asList("人力资源部"), Arrays.asList("人力资源部")));
        mappings.add(new DeptMapping("运管办", Arrays.asList("运管部"), Arrays.asList("运管部")));
        mappings.add(new DeptMapping("医务部", Arrays.asList("医务部"), Arrays.asList("医务部")));
        mappings.add(new DeptMapping("护理部", Arrays.asList("护理部"), Arrays.asList("护理部")));
        mappings.add(new DeptMapping("医院感染管理科", Arrays.asList("医院感染管理科"), Arrays.asList("院感科")));
        mappings.add(new DeptMapping("医院质量管理办公室", Arrays.asList("医院质量管理办公室"), Arrays.asList("质管办")));
        mappings.add(new DeptMapping("科教科", Arrays.asList("科教科"), Arrays.asList("科教科")));
        mappings.add(new DeptMapping("医疗保险与价格管理科", Arrays.asList("医疗保险与价格管理科"), Arrays.asList("医保办")));
        mappings.add(new DeptMapping("财务科", Arrays.asList("财务科"), Arrays.asList("财务科")));
        mappings.add(new DeptMapping("保卫科", Arrays.asList("保卫科"), Arrays.asList("保卫科")));
        mappings.add(new DeptMapping("信息科", Arrays.asList("信息科"), Arrays.asList("信息科")));
        mappings.add(new DeptMapping("工会", Arrays.asList("工会办公室"), Arrays.asList("工会")));
        mappings.add(new DeptMapping("总务科", Arrays.asList("总务科办公室"), Arrays.asList("总务科(办)","基建办")));
        mappings.add(new DeptMapping("医学装备与资产管理科", Arrays.asList("医学装备与资产管理科"), Arrays.asList("医学装备科")));
        mappings.add(new DeptMapping("采管科", Arrays.asList("采管科"), Arrays.asList("采管科")));
        mappings.add(new DeptMapping("服务部", Arrays.asList("服务部"), Arrays.asList("服务部")));
        mappings.add(new DeptMapping("临床药学科", Arrays.asList("临床药学科"), Arrays.asList("临床药学科")));
        mappings.add(new DeptMapping("血防科", Arrays.asList("血防科"), Arrays.asList("血防科")));
        mappings.add(new DeptMapping("预防保健科", Arrays.asList("预防保健科"), Arrays.asList("预保科")));
        mappings.add(new DeptMapping("艾滋病治疗管理办公室", Arrays.asList("艾滋病治疗管理办公室"), Arrays.asList("艾滋病防治办公室")));
        mappings.add(new DeptMapping("评审评价办", Arrays.asList("评审评价办公室"), Arrays.asList("评审办")));
        mappings.add(new DeptMapping("医院领导", Arrays.asList("医院领导"), Arrays.asList("医院领导")));
        
        return Collections.unmodifiableList(mappings);
    }
    
    /**
     * 初始化科室映射缓存
     * @return 科室映射缓存
     */
    private static Map<String, DeptMapping> initDeptMappingCache() {
        Map<String, DeptMapping> cache = new HashMap<>();
        for (DeptMapping mapping : DEPT_MAPPINGS) {
            cache.put(mapping.getBudgetDeptName(), mapping);
        }
        return Collections.unmodifiableMap(cache);
    }
    
    /**
     * 获取所有科室映射
     * @return 科室映射列表
     */
    public static List<DeptMapping> getAllDeptMappings() {
        return DEPT_MAPPINGS;
    }
    
    /**
     * 获取所有预算科室名称
     * @return 预算科室名称数组
     */
    public static String[] getAllBudgetDeptNames() {
        return DEPT_MAPPINGS.stream()
                .map(DeptMapping::getBudgetDeptName)
                .toArray(String[]::new);
    }
    
    /**
     * 根据预算科室名称获取科室映射
     * @param budgetDeptName 预算科室名称
     * @return 科室映射对象
     */
    public static DeptMapping getDeptMapping(String budgetDeptName) {
        return DEPT_MAPPING_CACHE.get(budgetDeptName);
    }
    
    /**
     * 获取预算科室到HRP科室的映射
     * @return 预算科室到HRP科室的映射
     */
    public static Map<String, List<String>> getBudgetToHrpDeptMap() {
        Map<String, List<String>> map = new HashMap<>();
        for (DeptMapping mapping : DEPT_MAPPINGS) {
            map.put(mapping.getBudgetDeptName(), mapping.getHrpDeptNames());
        }
        return map;
    }
    
    /**
     * 获取预算科室到用友科室的映射
     * @return 预算科室到用友科室的映射
     */
    public static Map<String, List<String>> getBudgetToYyDeptMap() {
        Map<String, List<String>> map = new HashMap<>();
        for (DeptMapping mapping : DEPT_MAPPINGS) {
            map.put(mapping.getBudgetDeptName(), mapping.getYyDeptNames());
        }
        return map;
    }
} 