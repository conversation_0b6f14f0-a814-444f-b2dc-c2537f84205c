package com.jp.med.bms.modules.bmsExecute.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 医疗设备购置预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:19:45
 */
@Data
public class BmsBudgetAssetVo {

	/** ID */
	private Integer id;

	/** 采购品目 */
	private String purcItem;

	/** 设备名称 */
	private String assetName;

	/** 计量单位 */
	private String unit;

	/** 单价 */
	private BigDecimal price;

	/** 数量 */
	private Integer cnt;

	/** 预算数 */
	private BigDecimal budgetAmount;

	/** 科室预算总和 */
	private BigDecimal budgetAmountSum;

	/** 科室 */
	private String dept;

	/** 科室名称 */
	private String orgName;

	/** 预算任务 */
	private String taskCode;

	/** 预算名称 */
	private String taskName;

	/** 审核状态(0:未审核 1:已审核) */
	private String chk;

	/** 耗材金额 */
	private BigDecimal mcsAmount;

	/** 耗材类型 */
	private String mcsType;

	/** 是否耗材 */
	private String isMcs;

	/** 类型 */
	private String assetType;

	/** 维保类型 */
	private String mntType;

	/** 类型 */
	private String type;

	/** 备注 */
	private String memo;

	/** 是否单一来源 **/
	private String singleSrc;

}
