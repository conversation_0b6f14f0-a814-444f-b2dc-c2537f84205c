package com.jp.med.bms.modules.bmsExecute.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 信息化建设项目预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:28:01
 */
@Data
public class BmsBudgetEngineVo {

	/** ID */
	private Integer id;

	/** 项目名称 */
	private String itemname;

	/** 采购方式 */
	private String purcWay;

	/** 预算数 */
	private BigDecimal budgetAmount;

	/** 科室预算总和 */
	private BigDecimal budgetAmountSum;

	/** 科室 */
	private String dept;

	/** 科室名称 */
	private String orgName;

	/** 备注 */
	private String memo;

	/** 预算任务 */
	private String taskCode;

	/** 预算名称 */
	private String taskName;

	/** 审核状态(0:未审核 1:已审核) */
	private String chk;

	/** 需求说明 */
	private String reqDscr;

	/** 实现效果 */
	private String implEfft;

	/** 单价 */
	private BigDecimal price;

	/** 数量 */
	private Integer cnt;

	/** 类型 */
	private String type;

	/** 名称*/
	private String engineName;

}
