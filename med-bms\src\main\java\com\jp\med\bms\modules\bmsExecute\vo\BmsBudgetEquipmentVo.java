package com.jp.med.bms.modules.bmsExecute.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 通用设备预算
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 14:06:20
 */
@Data
public class BmsBudgetEquipmentVo {

	/** ID */
	private Integer id;

	/** 通用设备名称 */
	private String equipmentName;

	/** 计量单位 */
	private String unit;

	/** 单价 */
	private BigDecimal price;

	/** 数量 */
	private Integer cnt;

	/** 预算数 */
	private BigDecimal budgetAmount;

	/** 科室预算总和 */
	private BigDecimal budgetAmountSum;

	/** 科室 */
	private String dept;

	/** 科室名称 */
	private String orgName;

	/** 预算任务 */
	private String taskCode;

	/** 预算名称 */
	private String taskName;

	/** 审核状态(0:未审核 1:已审核) */
	private String chk;

	/** 备注 */
	private String memo;
}
