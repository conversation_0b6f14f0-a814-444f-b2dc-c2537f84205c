package com.jp.med.bms.modules.bmsExecute.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 预算项目工作量情况
 * <AUTHOR>
 * @email -
 * @date 2024-07-27 15:16:25
 */
public class BmsBudgetSRVo {

	/** id */
	private Integer id;

	/** 预算任务编码 */
	private String budgetTaskCode;

	/** 月份 */
	private String month;

	/** 预算项目编码 */
	private String budgetCode;

	/** 门诊人次 */
	private String mzrc;

	/** 门诊均次费用 */
	private String  mzrckzfy;

	/** 门诊总费用 */
	private String  mzTotalFee;

	/** 出院人次 */
	private String  cyrc;

	/** 次均费用 */
	private String 	cjfy;

	/** 住院总费用 */
	private String  zyTotalFee;

	/** 部门编码 **/
	private String deptCode;

	/** 部门名称 **/
	private String deptName;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getBudgetTaskCode() {
		return budgetTaskCode;
	}

	public void setBudgetTaskCode(String budgetTaskCode) {
		this.budgetTaskCode = budgetTaskCode;
	}

	public String getMonth() {
		return month;
	}

	public void setMonth(String month) {
		this.month = month;
	}

	public String getBudgetCode() {
		return budgetCode;
	}

	public void setBudgetCode(String budgetCode) {
		this.budgetCode = budgetCode;
	}

	public String getMzrc() {
		return mzrc;
	}

	public void setMzrc(String mzrc) {
		this.mzrc = mzrc;
	}

	public String getMzrckzfy() {
		return mzrckzfy;
	}

	public void setMzrckzfy(String mzrckzfy) {
		this.mzrckzfy = mzrckzfy;
	}

	public String getMzTotalFee() {
		return mzTotalFee;
	}

	public void setMzTotalFee(String mzTotalFee) {
		this.mzTotalFee = mzTotalFee;
	}

	public String getCyrc() {
		return cyrc;
	}

	public void setCyrc(String cyrc) {
		this.cyrc = cyrc;
	}

	public String getCjfy() {
		return cjfy;
	}

	public void setCjfy(String cjfy) {
		this.cjfy = cjfy;
	}

	public String getZyTotalFee() {
		return zyTotalFee;
	}

	public void setZyTotalFee(String zyTotalFee) {
		this.zyTotalFee = zyTotalFee;
	}

	public String getDeptCode() {
		return deptCode;
	}

	public void setDeptCode(String deptCode) {
		this.deptCode = deptCode;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
}
