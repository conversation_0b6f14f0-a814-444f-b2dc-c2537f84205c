package com.jp.med.bms.modules.bmsExecute.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 服务类采购预算
 * <AUTHOR>
 * @email -
 * @date 2023-11-16 11:03:01
 */
@Data
public class BmsBudgetServiceVo {

	/** ID */
	private Integer id;

	/** 服务名称 */
	private String serviceName;

	/** 计量单位 */
	private String unit;

	/** 单价 */
	private BigDecimal price;

	/** 数量 */
	private Integer cnt;

	/** 预算数 */
	private BigDecimal budgetAmount;

	/** 科室 */
	private String dept;

	/** 预算任务 */
	private String taskCode;

	/** 审核状态(0:未审核 1:已审核) */
	private String chk;

	/** 科室名称 */
	private String orgName;


	private String taskName;

	/** 科室预算总和 */
	private BigDecimal budgetAmountSum;

	private String memo;
}
