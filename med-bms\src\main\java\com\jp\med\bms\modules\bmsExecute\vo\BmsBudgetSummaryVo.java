package com.jp.med.bms.modules.bmsExecute.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 预算项总和
 */
@Data
public class BmsBudgetSummaryVo {

    //科室代码
    private String orgId;
    //科室名称
    private String orgName;

    //牵头科室名称
    private String leadDeptCode;

    //牵头科室名称
    private String leadDeptName;
    //科目代码
    private String subCode;

    //科目名称
    private String subName;
    //预算编码
    private String budgetCode;
    //预算名称
    private String budgetName;

    //类型 1：会计科目 2：经济科目
    private String type;

    //是否由多个经济科目或者会计科目汇总显示的  1：是 0：否
    private String isSummary;
    //金额
    private BigDecimal actigAmt;

    //预算值
    private BigDecimal budgetAmt;

    //实际值
    private BigDecimal actualAmt;
}
