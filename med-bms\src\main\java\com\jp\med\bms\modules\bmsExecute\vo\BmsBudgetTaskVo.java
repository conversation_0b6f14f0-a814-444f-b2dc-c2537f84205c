package com.jp.med.bms.modules.bmsExecute.vo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 预算任务表
 * <AUTHOR>
 * @email -
 * @date 2023-10-19 17:18:28
 */
@Data
public class BmsBudgetTaskVo {

	/** ID */
	private Integer id;

	/** 预算任务编码 */
	private String budgetTaskCode;

	/** 预算任务名称 */
	private String budgetTaskName;

	/** 创建时间 */
	private String createTime;

	/** 有效标志 */
	private String activeFlag;

	/** 执行状态 */
	private String status;

	/** 备注 */
	private String remarks;

	/**  医疗机构编码 */
	private String hospitalId;

	/** 预算编制表 */
	private Integer budgetTableId;

	/** 编制表名称 */
	private String budgetTableName;

	/** 流程名称 */
	private String budgetFlowName;

	/** 流程编码 */
	private String budgetFlowCode;

	/** 执行明细编码 */
	private String flowDetailCdoe;

	/** 执行明细名称 */
	private String flowDetailName;

	/**  预算年度 */
	private String year;
}
