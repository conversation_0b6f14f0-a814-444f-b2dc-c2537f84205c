package com.jp.med.bms.modules.bmsExecute.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 预算项目工作量情况
 * <AUTHOR>
 * @email -
 * @date 2024-07-27 15:16:25
 */
@Data
public class BmsBudgetWorkloadVo {

	/** id */
	private Integer id;

	/** 预算任务编码 */
	private String budgetTaskCode;

	/** 月份 */
	private String month;

	/** 预算项目编码 */
	private String budgetCode;

	/** 预算项目名称 */
	private String budgetName;

	/** 预算编制类别  SR/ZCL */
	private String budgetTypeCode;

	/** 实际数量 */
	private BigDecimal actualAmt;

	/** 部门编码 **/
	private String deptCode;

	/** 部门名称 **/
	private String deptName;

	/** 年度总额 **/
	private BigDecimal yearAmt;

	/** 年度执行比例 **/
	private Float amtRatio;

	/** 预算总额 **/
	private BigDecimal budgetAmt;

	/** 经济科目编码 **/
	private String econSubCode;

	/** 经济科目名称 **/
	private String econSubName;

	/** 会计科目编码 **/
	private String actigSubCode;

	/** 会计科目名称 **/
	private String actigSubName;

	private String reimItemCode;

	private String reimItemName;

}
