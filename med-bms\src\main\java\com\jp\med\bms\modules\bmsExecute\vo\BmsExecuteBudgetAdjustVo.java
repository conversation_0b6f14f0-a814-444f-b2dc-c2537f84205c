package com.jp.med.bms.modules.bmsExecute.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 预算调整表
 * <AUTHOR>
 * @email -
 * @date 2023-06-01 18:20:31
 */
@Data
public class BmsExecuteBudgetAdjustVo {
	
	/** 预算调整表ID */
	private Integer budgetAdjustId;

	/** 预算调整申请ID */
	private BigDecimal budgetApplyId;

	/** 预算结果ID */
	private BigDecimal budgetResultsId;

	/** 附件 */
	private String attachment;

	/** 附件的外链 */
	private String attachmentUrl;

	/** 说明 */
	private String remark;

	/** 预算数(调整后) */
	private BigDecimal budgetAmount;

	/** 原始预算数 */
	private BigDecimal originalBudgetAmount;

	/** 调整预算数 */
	private BigDecimal adjustBudgetAmount;

	/** 预算编制项目编码 */
	private String budgetCode;

	/** 预算编制项目名称 */
	private String budgetName;

	/** 预算名称 */
	private String budgetFlowName;

	/** 单位 */
	private String unit;

	/** 预算科室名称 */
	private String orgName;

	/** 归口科室名称 */
	private String centralizedDeptName;

	private String budgetTakeName;


}
