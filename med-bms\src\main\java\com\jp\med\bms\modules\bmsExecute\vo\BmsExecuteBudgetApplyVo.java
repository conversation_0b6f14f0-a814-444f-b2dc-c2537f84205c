package com.jp.med.bms.modules.bmsExecute.vo;


import lombok.Data;

import java.util.Date;

/**
 * 预算调整申请表
 * <AUTHOR>
 * @email -
 * @date 2023-06-01 14:47:42
 */
@Data
public class BmsExecuteBudgetApplyVo {
	
	/** 预算调整申请表ID */
	private Long budgetApplyId;

	/** 申请类型 */
	private Long flowModelId;

	/** 创建时间 */
	private Date createTime;

	/** 创建人 */
	private String username;

	/** 说明 */
	private String remark;

	/** 附件 */
	private String attachment;

	/** 状态(0:已保存,1:已提交,2:已驳回,3:申请成功) */
	private String status;

	/** 医疗机构ID */
	private String hospitalId;

	/** 流程实例 */
	private String modelCase;

	/** 流程实例外链 */
	private String modelCaseUrl;

	/** 申请部门 */
	private String orgId;

	/** 流程名称*/
	private String flowModeName;

	/** 任务ID */
	private String taskId;

	/** 业务KEY */
	private String businessKey;

	/** 预算ID */
	private String budgetFlowId;

	private String processInstanceId;

}
