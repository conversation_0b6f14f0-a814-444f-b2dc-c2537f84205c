package com.jp.med.bms.modules.bmsExecute.vo;


import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * 预算编制数据
 * <AUTHOR>
 * @email -
 * @date 2023-04-26 15:40:51
 */
@Data
public class BmsExecuteBudgetDataVo {

	/** 预算编制项数据ID */
	private Integer budgetDataId;

	/** 预算执行节点 */
	private String flowDetailCode;

	/** 预算编制项 */
	private String budgetCode;

	/** 预算数 */
	private BigDecimal budgetAmount;

	/** 预算科室 */
	private String orgId;

	/** 预算编制任务 */
	private String budgetTaskCode;

	/** 预算单位 */
	private String hospitalId;

	/** 执行科室 */
	private String executeDept;


	/** 预算编制项名称 */
	private String budgetName;

	/** 预算编制项目全路径 */
	private String budgetNameAll;

	/** 主要内容 */
	private String cont;
	/** 说明 */
	private String dscr;

	/** 上级编制项编码 */
	private String budgetParentId;

	/** 初始预算数 */
	private BigDecimal lastBudgetAmount;

	/** 预算科室 */
	private String orgName;

	/** 归口科室 */
	private String centralizedDept;

	/** 单位 */
	private String unit;

	/** 预算编制表 */
	private Long budgetTableId;

	/** 唯一KEY */
	private String key;

	/** 是否为叶子节点 */
	private String isLeaf;

	/** 归口科室名称 */
	private String centralizedDeptName;

	/** 流程节点名称 */
	private String flowDetailName;

	/**总数*/
	private Integer total;

	/** 流程顺序 */
	private Integer organizationOrder;

	/** 是否进行计算 */
	private String cal;

	/** 计算公式 */
	private String formula;

	/** 计算公式说明 */
	private String formulaLabel;

	/** 是否审核 */
	private String chk;

	/** 编制项目类型 */
	private String budgetTypeCode;

	/**二级归类编码*/
	private String budgetStatisticsLv2Code;

	/**二级归类名称*/
	private String budgetStatisticsLv2Name;

	/**一级归类编码*/
	private String budgetStatisticsLv1Code ;

	/**一级归类名称*/
	private String budgetStatisticsLv1Name;

	/** 子节点数据 */
	private List<BmsExecuteBudgetDataVo> children;

	/** 拓展 */
	private Map<String,BigDecimal> ext;

}
