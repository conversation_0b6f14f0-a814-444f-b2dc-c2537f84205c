package com.jp.med.bms.modules.bmsExecute.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 预算填报状态表
 * <AUTHOR>
 * @email -
 * @date 2023-04-28 10:35:42
 */
@Data
public class BmsExecuteBudgetFillingVo {
	
	/** 预算填报状态ID */
	private Integer budgetFillingId;

	/** 预算编制详情 */
	private BigDecimal flowDetailId;

	/** 填报部门 */
	private String orgId;

	/** 填报状态(0:填报中,1:已提交,2:已驳回,3:填报完成) */
	private String status;

	/**  组织名称 */
	private String orgName;

	/** 预算任务编码 */
	private String budgetTaskCode;

	/** 编制名称 */
	private String flowDetailName;

	/** 编制编码 */
	private String flowDetailCode;

	/** 流程ID */
	private String budgetFlowId;

	/** 流程顺序 */
	private String organizationOrder;

	/** 任务名称 */
	private String budgetTaskName;

	/**科室类型*/
	private String deptType;

	/** 执行者 */
	private String ptr;



}
