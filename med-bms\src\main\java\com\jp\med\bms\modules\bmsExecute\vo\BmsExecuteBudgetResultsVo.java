package com.jp.med.bms.modules.bmsExecute.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 预算结果
 *
 * <AUTHOR>
 * @email -
 * @date 2023-05-31 17:55:21
 */
@Data
public class BmsExecuteBudgetResultsVo {

    /**
     * 预算结果ID
     */
    private Long budgetResultsId;

    /**
     * 预算执行节点
     **/
    private String flowDetailCode;

    /**
     * 预算编制项编码
     */
    private String budgetCode;

    /**
     * 预算编制项名称
     */
    private String budgetName;

    /**
     * 预算数
     */
    private BigDecimal budgetAmount;

    /**
     * 预算科室
     */
    private String orgId;

    /**
     * 预算科室
     */
    private String orgName;

    /**
     * 归口科室
     */
    private String centralizedDept;

    /**
     * 归口科室名称
     **/
    private String centralizedDeptName;

    /**
     * 预算任务
     */
    private String budgetTaskCode;

    /**
     * 预算单位
     */
    private String hospitalId;

    /**
     * 单位
     */
    private String unit;

    /**
     * 年度实际发生金额
     */
    private BigDecimal actigAmt;

    /**
     * 预算编制项父类编码
     */
    private String budgetParentId;

}
