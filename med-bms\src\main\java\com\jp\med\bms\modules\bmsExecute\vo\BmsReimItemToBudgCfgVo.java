package com.jp.med.bms.modules.bmsExecute.vo;

import lombok.Data;

/**
 * 报销项目对应预算项目
 */
@Data
public class BmsReimItemToBudgCfgVo {

	/** id */
	private Integer id;

	/** 报销费用项编码 */
	private String reimItemCode;

	/** 报销费用项名称 */
	private String reimItemName;

	/** 预算编制项编码 */
	private String budgetCode;

	/** 预算编制项 */
	private String budgetName;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 修改时间 */
	private String modiTime;

	/** 医疗机构id */
	private String hospitalId;

	/** 启用标志 */
	private String activeFlag;

	/** 年 */
	private String year;

	/** 类型 1：会计科目 2：经济科目 **/
	private String type;
}
