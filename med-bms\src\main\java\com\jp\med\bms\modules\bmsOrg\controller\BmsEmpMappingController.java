package com.jp.med.bms.modules.bmsOrg.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.bms.modules.bmsOrg.dto.BmsEmpMappingDto;
import com.jp.med.bms.modules.bmsOrg.service.read.BmsEmpMappingReadService;
import com.jp.med.bms.modules.bmsOrg.service.write.BmsEmpMappingWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 用户科室映射
 * <AUTHOR>
 * @email -
 * @date 2023-11-03 09:27:54
 */
@Api(value = "用户科室映射", tags = "用户科室映射")
@RestController
@RequestMapping("bmsEmpMapping")
public class BmsEmpMappingController {

    @Autowired
    private BmsEmpMappingReadService bmsEmpMappingReadService;

    @Autowired
    private BmsEmpMappingWriteService bmsEmpMappingWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询用户科室映射")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsEmpMappingDto dto){
        return CommonResult.paging(bmsEmpMappingReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增用户科室映射")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsEmpMappingDto dto){
        bmsEmpMappingWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改用户科室映射")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsEmpMappingDto dto){
        bmsEmpMappingWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除用户科室映射")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsEmpMappingDto dto){
        bmsEmpMappingWriteService.removeById(dto);
        return CommonResult.success();
    }

}
