package com.jp.med.bms.modules.bmsOrg.controller;

import com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.entity.emp.EmpEmployeeInfoEntity;
import com.jp.med.common.vo.SelectOptionVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.bms.modules.bmsOrg.dto.BmsOrgDto;
import com.jp.med.bms.modules.bmsOrg.service.read.BmsOrgReadService;
import com.jp.med.bms.modules.bmsOrg.service.write.BmsOrgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;


/**
 * 组织架构表
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-17 15:49:13
 */
@Api(value = "组织架构表", tags = "组织架构表")
@RestController
@RequestMapping("bmsOrg")
public class BmsOrgController {
    @Autowired
    private BmsOrgReadService bmsOrgReadService;

    @Autowired
    private BmsOrgWriteService bmsOrgWriteService;

    @ApiOperation("查询组织架构表")
    @PostMapping("/list")
    public CommonResult<List<BmsOrgVo>> list(@RequestBody BmsOrgDto dto) {
        return CommonResult.success(bmsOrgReadService.queryList(dto));
    }

    @ApiOperation("查询组织编码是否唯一")
    @PostMapping("/queryOrgOnly")
    public CommonResult<?> queryOrgOnly(@RequestBody BmsOrgDto dto) {
        bmsOrgReadService.queryOrgOnly(dto);
        return CommonResult.success();
    }

    @ApiOperation("查询组织架构下拉树")
    @PostMapping("/queryOrgTree")
    public CommonResult<List<BmsOrgVo>> queryOrgTree(@RequestBody BmsOrgDto dto) {
        return CommonResult.success(bmsOrgReadService.queryOrgTree(dto));
    }

    @ApiOperation("新增组织架构表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsOrgDto dto) {
        bmsOrgWriteService.saveOrg(dto);
        return CommonResult.success();
    }

    @ApiOperation("修改组织架构表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsOrgDto dto) {
        bmsOrgWriteService.updateOrg(dto);
        return CommonResult.success();
    }

    @ApiOperation("删除组织架构表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsOrgDto dto) {
        bmsOrgWriteService.deleteBmsOrg(dto);
        return CommonResult.success();
    }

    @ApiOperation("查询做预算的科室列表")
    @PostMapping("/queryDept")
    public CommonResult<List<BmsOrgVo>> queryDept(@RequestBody BmsOrgDto dto) {
        return CommonResult.success(bmsOrgReadService.queryDept(dto));
    }

    @ApiOperation("查询用户列表")
    @PostMapping("/queryUserOption")
    public CommonResult<List<SelectOptionVo>> queryUserOption(@RequestBody BmsOrgDto dto) {
        return CommonResult.success(bmsOrgReadService.queryUserOption(dto));
    }


    @ApiOperation("删除组织架构表")
    @PostMapping("/bmsGraphModelDataSave")
    public CommonResult<?> bmsGraphModelDataSave(@RequestBody BmsOrgDto dto) {
        bmsOrgWriteService.graphModelDataSave(dto);
        return CommonResult.success();
    }


    @ApiOperation("查询组织架构用户")
    @PostMapping("/queryOrgUser")
    public CommonResult<?> queryOrgUser(@RequestBody BmsOrgDto dto) {
        List<EmpEmployeeInfoEntity> list = bmsOrgReadService.queryOrgUser(dto);
        return CommonResult.success(list);
    }


    @ApiOperation("保存组织架构用户")
    @PostMapping("/saveOrgUser")
    public CommonResult<?> saveOrgUser(@RequestBody BmsOrgDto dto) {
        bmsOrgWriteService.saveOrgUser(dto);
        return CommonResult.success();
    }


    @ApiOperation("通过员工编号查询组织信息")
    @PostMapping("/queryOrgByEmpCode")
    public CommonFeignResult queryOrgByEmpCode(@RequestParam("empCode") String empCode) {
        return CommonFeignResult.build().put(CommonFeignResult.DATA_KEY, bmsOrgReadService.queryOrgByEmpCode(empCode));
    }

}
