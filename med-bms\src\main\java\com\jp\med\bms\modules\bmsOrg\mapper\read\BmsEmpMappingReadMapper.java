package com.jp.med.bms.modules.bmsOrg.mapper.read;

import com.jp.med.bms.modules.bmsOrg.dto.BmsEmpMappingDto;
import com.jp.med.bms.modules.bmsOrg.vo.BmsEmpMappingVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 用户科室映射
 * <AUTHOR>
 * @email -
 * @date 2023-11-03 09:27:54
 */
@Mapper
public interface BmsEmpMappingReadMapper extends BaseMapper<BmsEmpMappingDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsEmpMappingVo> queryList(BmsEmpMappingDto dto);
}
