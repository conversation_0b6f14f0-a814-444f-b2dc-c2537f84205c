package com.jp.med.bms.modules.bmsOrg.mapper.read;

import com.jp.med.bms.modules.bmsOrg.dto.BmsOrgDto;
import com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.entity.emp.EmpEmployeeInfoEntity;
import com.jp.med.common.vo.SelectOptionVo;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 组织架构表
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-17 15:49:13
 */
@Mapper
public interface BmsOrgReadMapper extends BaseMapper<BmsOrgDto> {
    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsOrgVo> queryList(BmsOrgDto dto);

    /**
     *查询组织架构下拉
     * @param dto
     * @return
     */
    List<BmsOrgVo> queryOrgTree(BmsOrgDto dto);

    /**
     * 查询组织架构用户
     * @param dto
     * @return
     */
    List<EmpEmployeeInfoEntity> queryOrgUser(BmsOrgDto dto);

    /**
     * 通过员工编号查询组织信息
     * @param empCode
     * @return
     */
    List<BmsOrgVo> queryOrgByEmpCode(String empCode);

    /**
     * 查询可做预算的科室
     * @param dto
     * @return
     */
    List<BmsOrgVo> queryDept(BmsOrgDto dto);

    /**
     * 查询用户
     * @param dto
     * @return
     */
    List<SelectOptionVo> queryUserOption(BmsOrgDto dto);

    /**
     * 查询所有科室
     * @param bmsOrgDto
     * @return
     */
    List<BmsOrgVo> queryAllDept(BmsOrgDto bmsOrgDto);

    /**
     * 查询非子集编制项目
     * @param bmsOrgDto
     */
    List<BmsOrgVo> queryParentOrg(BmsOrgDto bmsOrgDto);

    /**
     * 查询是否存在
     * @param dto
     * @return
     */
    List<BmsOrgVo> queryOrgOnly(BmsOrgDto dto);
}
