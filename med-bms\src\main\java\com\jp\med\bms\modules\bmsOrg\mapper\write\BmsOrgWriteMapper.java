package com.jp.med.bms.modules.bmsOrg.mapper.write;

import com.jp.med.bms.modules.bmsOrg.dto.BmsOrgDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 组织架构表
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-17 15:49:13
 */
@Mapper
public interface BmsOrgWriteMapper extends BaseMapper<BmsOrgDto> {
    /**
    * 修改
    * @param dto
    */
    void updateOrg(BmsOrgDto dto);

    /**
    * 新增
    * @param dto
    */
    void saveOrg(BmsOrgDto dto);

    /**
     * 级联删除当前数据及其子集
     * @param dto
     * @return
     */
    int deleteBmsOrg(BmsOrgDto dto);

    /**
     * 新增
     * @param dto
     */
    void deleteBmsOrgById(BmsOrgDto dto);

    /**
     * 保存组织架构用户
     * @param dto
     */
    void saveOrgUser(BmsOrgDto dto);

    /**
     * 删除组织架构用户
     * @param dto
     */
    void deleteOrgUser(BmsOrgDto dto);
}
