package com.jp.med.bms.modules.bmsOrg.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsOrg.dto.BmsEmpMappingDto;
import com.jp.med.bms.modules.bmsOrg.vo.BmsEmpMappingVo;

import java.util.List;

/**
 * 用户科室映射
 * <AUTHOR>
 * @email -
 * @date 2023-11-03 09:27:54
 */
public interface BmsEmpMappingReadService extends IService<BmsEmpMappingDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsEmpMappingVo> queryList(BmsEmpMappingDto dto);
}

