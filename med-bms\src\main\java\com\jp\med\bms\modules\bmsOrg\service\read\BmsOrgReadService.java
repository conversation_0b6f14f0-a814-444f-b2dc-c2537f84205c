package com.jp.med.bms.modules.bmsOrg.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsOrg.dto.BmsOrgDto;
import com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo;
import com.jp.med.common.entity.emp.EmpEmployeeInfoEntity;
import com.jp.med.common.vo.SelectOptionVo;

import java.util.List;

/**
 * 组织架构表
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-17 15:49:13
 */
public interface BmsOrgReadService extends IService<BmsOrgDto> {
    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsOrgVo> queryList(BmsOrgDto dto);

    /**
     * 查询组织机构下拉
     * @param dto
     * @return
     */
    List<BmsOrgVo> queryOrgTree(BmsOrgDto dto);

    /**
     * 查询组织架构用户
     * @param dto
     * @return
     */
    List<EmpEmployeeInfoEntity> queryOrgUser(BmsOrgDto dto);

    /**
     * 通过员工编号查询组织信息
     * @param empCode
     * @return
     */
    BmsOrgVo queryOrgByEmpCode(String empCode);

    /**
     * 查询可做预算的科室
     * @param dto
     * @return
     */
    List<BmsOrgVo> queryDept(BmsOrgDto dto);


    /**
     * 查询用户
     * @param dto
     * @return
     */
    List<SelectOptionVo> queryUserOption(BmsOrgDto dto);

    /**
     * 查询是否重复
     * @param dto
     * @return
     */
    void queryOrgOnly(BmsOrgDto dto);
}

