package com.jp.med.bms.modules.bmsOrg.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bms.modules.bmsOrg.mapper.read.BmsEmpMappingReadMapper;
import com.jp.med.bms.modules.bmsOrg.dto.BmsEmpMappingDto;
import com.jp.med.bms.modules.bmsOrg.vo.BmsEmpMappingVo;
import com.jp.med.bms.modules.bmsOrg.service.read.BmsEmpMappingReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class BmsEmpMappingReadServiceImpl extends ServiceImpl<BmsEmpMappingReadMapper, BmsEmpMappingDto> implements BmsEmpMappingReadService {

    @Autowired
    private BmsEmpMappingReadMapper bmsEmpMappingReadMapper;

    @Override
    public List<BmsEmpMappingVo> queryList(BmsEmpMappingDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return bmsEmpMappingReadMapper.queryList(dto);
    }

}
