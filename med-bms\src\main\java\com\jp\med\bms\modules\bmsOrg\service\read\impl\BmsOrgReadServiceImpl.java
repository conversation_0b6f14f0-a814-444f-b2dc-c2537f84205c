package com.jp.med.bms.modules.bmsOrg.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.bms.modules.bmsOrg.dto.BmsOrgDto;
import com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo;
import com.jp.med.bms.modules.bmsOrg.mapper.read.BmsOrgReadMapper;
import com.jp.med.bms.modules.bmsOrg.service.read.BmsOrgReadService;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.entity.emp.EmpEmployeeInfoEntity;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.vo.SelectOptionVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class BmsOrgReadServiceImpl extends ServiceImpl<BmsOrgReadMapper, BmsOrgDto> implements BmsOrgReadService {
    @Autowired
    private BmsOrgReadMapper bmsOrgReadMapper;

    @Override
    public List<BmsOrgVo> queryList(BmsOrgDto dto) {
        return bmsOrgReadMapper.queryList(dto);
    }

    /**
     *查 询组织架构
     * @param dto
     * @return
     */
    @Override
    public List<BmsOrgVo> queryOrgTree(BmsOrgDto dto) {
        return bmsOrgReadMapper.queryOrgTree(dto);
    }

    @Override
    public List<EmpEmployeeInfoEntity> queryOrgUser(BmsOrgDto dto) {
        return bmsOrgReadMapper.queryOrgUser(dto);
    }

    @Override
    public BmsOrgVo queryOrgByEmpCode(String empCode) {
        BmsOrgVo orgVo = new BmsOrgVo();
        List<BmsOrgVo> bmsOrgVos = bmsOrgReadMapper.queryOrgByEmpCode(empCode);
        if (!bmsOrgVos.isEmpty()) {
            Map<String, String> map = bmsOrgVos.stream().collect(Collectors.toMap(BmsOrgVo::getHrmOrgId, BmsOrgVo::getBmsOrgId, (o1, o2) -> o2));
            orgVo.setEmpCode(empCode);
            orgVo.setMapping(map);
        }
        return orgVo;
    }

    /**
     * 查询可做预算的科室
     * @param dto
     * @return
     */
    @Override
    public List<BmsOrgVo> queryDept(BmsOrgDto dto) {
        return bmsOrgReadMapper.queryDept(dto);
    }

    @Override
    public List<SelectOptionVo> queryUserOption(BmsOrgDto dto) {
        return bmsOrgReadMapper.queryUserOption(dto);
    }

    /**
     * 查询是否重复
     * @param dto
     * @return
     */
    @Override
    public void queryOrgOnly(BmsOrgDto dto) {
        List<BmsOrgVo> bmsOrgVos = bmsOrgReadMapper.queryOrgOnly(dto);
        if (!bmsOrgVos.isEmpty()){
            throw new AppException("编码已存在");
        }
    }
}
