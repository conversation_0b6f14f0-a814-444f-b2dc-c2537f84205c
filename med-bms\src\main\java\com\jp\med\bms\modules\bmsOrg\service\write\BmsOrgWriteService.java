package com.jp.med.bms.modules.bmsOrg.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsOrg.dto.BmsOrgDto;

/**
 * 组织架构表
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-17 15:49:13
 */
public interface BmsOrgWriteService extends IService<BmsOrgDto> {
    /**
     * 修改
     * @param dto
     */
    void updateOrg(BmsOrgDto dto);

    /**
     * 新增
     * @param dto
     */
    void saveOrg(BmsOrgDto dto);

    /**
     * 级联删除
     * @param dto
     */
    void deleteBmsOrg(BmsOrgDto dto);

    /**
     * 通过流程图新增
     * @param dto
     */
    void graphModelDataSave(BmsOrgDto dto);

    /**
     * 保存组织架构用户
     * @param dto
     */
    void saveOrgUser(BmsOrgDto dto);
}

