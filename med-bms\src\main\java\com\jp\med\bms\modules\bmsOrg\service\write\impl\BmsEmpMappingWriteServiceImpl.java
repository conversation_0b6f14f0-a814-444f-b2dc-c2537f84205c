package com.jp.med.bms.modules.bmsOrg.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.bms.modules.bmsOrg.mapper.write.BmsEmpMappingWriteMapper;
import com.jp.med.bms.modules.bmsOrg.dto.BmsEmpMappingDto;
import com.jp.med.bms.modules.bmsOrg.service.write.BmsEmpMappingWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户科室映射
 * <AUTHOR>
 * @email -
 * @date 2023-11-03 09:27:54
 */
@Service
@Transactional(readOnly = false)
public class BmsEmpMappingWriteServiceImpl extends ServiceImpl<BmsEmpMappingWriteMapper, BmsEmpMappingDto> implements BmsEmpMappingWriteService {
}
