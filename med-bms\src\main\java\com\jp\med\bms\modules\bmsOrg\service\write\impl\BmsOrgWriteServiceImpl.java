package com.jp.med.bms.modules.bmsOrg.service.write.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.bms.modules.bmsOrg.dto.BmsOrgDto;
import com.jp.med.bms.modules.bmsOrg.mapper.read.BmsOrgReadMapper;
import com.jp.med.bms.modules.bmsOrg.mapper.write.BmsOrgWriteMapper;
import com.jp.med.bms.modules.bmsOrg.service.write.BmsOrgWriteService;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Transactional(readOnly = false)
@Service
public class BmsOrgWriteServiceImpl extends ServiceImpl<BmsOrgWriteMapper, BmsOrgDto> implements BmsOrgWriteService {
    @Autowired
    private BmsOrgWriteMapper bmsOrgWriteMapper;

    @Autowired
    private BmsOrgReadMapper bmsOrgReadMapper;

    @Override
    public void updateOrg(BmsOrgDto dto) {
        bmsOrgWriteMapper.updateOrg(dto);
    }

    @Override
    public void saveOrg(BmsOrgDto dto) {
        dto.setHospitalId(dto.getHospitalId());
        bmsOrgWriteMapper.saveOrg(dto);
    }

    @Override
    public void deleteBmsOrg(BmsOrgDto dto) {
        int count = bmsOrgWriteMapper.deleteBmsOrg(dto);
        if (count < 1) {
            throw new AppException("删除失败");
        }
    }

    @Override
    public void graphModelDataSave(BmsOrgDto dto) {
        if (CollectionUtils.isNotEmpty(dto.getDeleteList())) {
            BatchUtil.batch("deleteBmsOrgById", dto.getDeleteList(), BmsOrgWriteMapper.class);
        }
        if (CollectionUtils.isNotEmpty(dto.getAddList())) {
            for (BmsOrgDto bmsOrgDto : dto.getAddList()) {
                bmsOrgDto.setHospitalId(dto.getHospitalId());
            }
            BatchUtil.batch("saveOrg", dto.getAddList(), BmsOrgWriteMapper.class);
        }
    }

    @Override
    public void saveOrgUser(BmsOrgDto dto) {
        if (CollectionUtils.isNotEmpty(dto.getOriEmpCodes())) {
            bmsOrgWriteMapper.deleteOrgUser(dto);
        }
        if (CollectionUtils.isNotEmpty(dto.getEmpCodes())) {
            bmsOrgWriteMapper.saveOrgUser(dto);
        }
    }
}
