package com.jp.med.bms.modules.bmsOrg.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 用户科室映射
 * <AUTHOR>
 * @email -
 * @date 2023-11-03 09:27:54
 */
@Data
public class BmsEmpMappingVo {

	/** ID */
	private Integer id;

	/** 员工编号 */
	private String empCode;

	/** 人力资源科室ID */
	private String hrmOrgId;

	/** 人力资源科室名称 */
	private String hrmOrgName;

	/** 预算管理科室ID */
	private String bmsOrgId;

	/** 预算管理科室名称 */
	private String bmsOrgName;

	/** 医疗机构编码 */
	private String hospitalId;

}
