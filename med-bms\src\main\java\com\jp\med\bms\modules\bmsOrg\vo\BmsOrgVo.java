package com.jp.med.bms.modules.bmsOrg.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

import java.util.Map;

/**
 * 组织架构表
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-04-17 15:49:13
 */
@Data
public class BmsOrgVo {

	/** 组织架构ID */
	private String orgId;

	/** 父组织架构ID */
	private String orgParentId;

	/** 组织架构名称 */
	private String orgName;

	/** 医疗机构代码 */
	private String hospitalId;

	/**
	 * 是否可选
	 */
	private Boolean disabled;

	/** 父级组织名称*/
	private String orgParentName;

	private String label;

	private String value;

	/**科室类型*/
	private String deptType;

	/** 用户编码 */
	private String empCode;

	/** 人力资源科室 */
	private String hrmOrgId;

	/** 预算管理科室 */
	private String bmsOrgId;

	/** 科室映射关系 */
	private Map<String,String> mapping;

}
