package com.jp.med.bms.modules.config.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.bms.modules.config.dto.BmsReportsCfgDto;
import com.jp.med.bms.modules.config.service.read.BmsReportsCfgReadService;
import com.jp.med.bms.modules.config.service.write.BmsReportsCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 预算报表配置
 * <AUTHOR>
 * @email -
 * @date 2023-12-20 18:10:55
 */
@Api(value = "预算报表配置", tags = "预算报表配置")
@RestController
@RequestMapping("bmsReportsCfg")
public class BmsReportsCfgController {

    @Autowired
    private BmsReportsCfgReadService bmsReportsCfgReadService;

    @Autowired
    private BmsReportsCfgWriteService bmsReportsCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询预算报表配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsReportsCfgDto dto){
        return CommonResult.paging(bmsReportsCfgReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增预算报表配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsReportsCfgDto dto){
        bmsReportsCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改预算报表配置")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsReportsCfgDto dto){
        bmsReportsCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除预算报表配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsReportsCfgDto dto){
        bmsReportsCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

}
