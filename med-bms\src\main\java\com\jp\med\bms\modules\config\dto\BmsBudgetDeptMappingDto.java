package com.jp.med.bms.modules.config.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

/**
 * 预算科室映射
 * <AUTHOR>
 * @email -
 * @date 2025-03-25 11:00:18
 */
@Data
@TableName("bms_budget_dept_mapping")
public class BmsBudgetDeptMappingDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 源科室编码 */
    @TableField("source_dept")
    private String sourceDept;

    /** 映射后科室编码 */
    @TableField("target_dept")
    private String targetDept;

    /** 年度 */
    @TableField("year")
    private String year;

    /** 启用标志 1：启用  0：禁用 */
    @TableField("flag")
    private String flag;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 映射后科室类型 **/
    @TableField("dept_type")
    private String deptType;

    /** 映射前预算项 **/
    @TableField("source_bgt_code")
    private String sourceBgtCode;

    /** 映射后预算项 **/
    @TableField("target_bgt_code")
    private String targetBgtCode;

}
