package com.jp.med.bms.modules.config.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 预算报表配置
 * <AUTHOR>
 * @email -
 * @date 2023-12-20 18:10:55
 */
@Data
@TableName("bms_reports_cfg" )
public class BmsReportsCfgDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 报表名称 */
    @TableField(value = "reports_name",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String reportsName;

    /** 报表字段编码 */
    @TableField(value = "col_title_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String colTitleCode;

    /** 报表字段标题 */
    @TableField(value = "col_title",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String colTitle;

    /** 报表值 */
    @TableField(value = "col_val",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String colVal;

    /** 是否计算 */
    @TableField(value = "cal",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String cal;

    /** 创建时间 */
    @TableField(value = "creat_time",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String creatTime;

    /** 有效标志 */
    @TableField(value = "active_flag",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String activeFlag;

    /** 医疗机构编码 */
    @TableField(value = "hospital_id",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String hospitalId;

}
