package com.jp.med.bms.modules.config.mapper.read;

import com.jp.med.bms.modules.config.dto.BmsBudgetDeptMappingDto;
import com.jp.med.bms.modules.config.vo.BmsBudgetDeptMappingVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 预算科室映射
 * <AUTHOR>
 * @email -
 * @date 2025-03-25 11:00:18
 */
@Mapper
public interface BmsBudgetDeptMappingReadMapper extends BaseMapper<BmsBudgetDeptMappingDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsBudgetDeptMappingVo> queryList(BmsBudgetDeptMappingDto dto);
}
