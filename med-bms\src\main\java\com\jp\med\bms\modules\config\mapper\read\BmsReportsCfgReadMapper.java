package com.jp.med.bms.modules.config.mapper.read;

import com.jp.med.bms.modules.config.dto.BmsReportsCfgDto;
import com.jp.med.bms.modules.config.vo.BmsReportsCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 预算报表配置
 * <AUTHOR>
 * @email -
 * @date 2023-12-20 18:10:55
 */
@Mapper
public interface BmsReportsCfgReadMapper extends BaseMapper<BmsReportsCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsReportsCfgVo> queryList(BmsReportsCfgDto dto);
}
