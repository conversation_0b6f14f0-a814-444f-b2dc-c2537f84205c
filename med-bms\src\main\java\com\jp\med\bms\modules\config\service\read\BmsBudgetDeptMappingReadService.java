package com.jp.med.bms.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.config.dto.BmsBudgetDeptMappingDto;
import com.jp.med.bms.modules.config.vo.BmsBudgetDeptMappingVo;

import java.util.List;

/**
 * 预算科室映射
 * <AUTHOR>
 * @email -
 * @date 2025-03-25 11:00:18
 */
public interface BmsBudgetDeptMappingReadService extends IService<BmsBudgetDeptMappingDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsBudgetDeptMappingVo> queryList(BmsBudgetDeptMappingDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<BmsBudgetDeptMappingVo> queryPageList(BmsBudgetDeptMappingDto dto);
}

