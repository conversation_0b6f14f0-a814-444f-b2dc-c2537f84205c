package com.jp.med.bms.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.config.dto.BmsReportsCfgDto;
import com.jp.med.bms.modules.config.vo.BmsReportsCfgVo;

import java.util.List;

/**
 * 预算报表配置
 * <AUTHOR>
 * @email -
 * @date 2023-12-20 18:10:55
 */
public interface BmsReportsCfgReadService extends IService<BmsReportsCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsReportsCfgVo> queryList(BmsReportsCfgDto dto);
}

