package com.jp.med.bms.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bms.modules.config.mapper.read.BmsBudgetDeptMappingReadMapper;
import com.jp.med.bms.modules.config.dto.BmsBudgetDeptMappingDto;
import com.jp.med.bms.modules.config.vo.BmsBudgetDeptMappingVo;
import com.jp.med.bms.modules.config.service.read.BmsBudgetDeptMappingReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class BmsBudgetDeptMappingReadServiceImpl extends ServiceImpl<BmsBudgetDeptMappingReadMapper, BmsBudgetDeptMappingDto> implements BmsBudgetDeptMappingReadService {

    @Autowired
    private BmsBudgetDeptMappingReadMapper bmsBudgetDeptMappingReadMapper;

    @Override
    public List<BmsBudgetDeptMappingVo> queryList(BmsBudgetDeptMappingDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return bmsBudgetDeptMappingReadMapper.queryList(dto);
    }

    @Override
    public List<BmsBudgetDeptMappingVo> queryPageList(BmsBudgetDeptMappingDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return bmsBudgetDeptMappingReadMapper.queryList(dto);
    }

}
