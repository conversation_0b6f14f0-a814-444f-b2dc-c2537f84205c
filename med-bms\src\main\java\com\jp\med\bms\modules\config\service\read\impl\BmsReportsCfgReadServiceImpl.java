package com.jp.med.bms.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bms.modules.config.mapper.read.BmsReportsCfgReadMapper;
import com.jp.med.bms.modules.config.dto.BmsReportsCfgDto;
import com.jp.med.bms.modules.config.vo.BmsReportsCfgVo;
import com.jp.med.bms.modules.config.service.read.BmsReportsCfgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class BmsReportsCfgReadServiceImpl extends ServiceImpl<BmsReportsCfgReadMapper, BmsReportsCfgDto> implements BmsReportsCfgReadService {

    @Autowired
    private BmsReportsCfgReadMapper bmsReportsCfgReadMapper;

    @Override
    public List<BmsReportsCfgVo> queryList(BmsReportsCfgDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return bmsReportsCfgReadMapper.queryList(dto);
    }

}
