package com.jp.med.bms.modules.config.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.bms.modules.config.mapper.write.BmsReportsCfgWriteMapper;
import com.jp.med.bms.modules.config.dto.BmsReportsCfgDto;
import com.jp.med.bms.modules.config.service.write.BmsReportsCfgWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 预算报表配置
 * <AUTHOR>
 * @email -
 * @date 2023-12-20 18:10:55
 */
@Service
@Transactional(readOnly = false)
public class BmsReportsCfgWriteServiceImpl extends ServiceImpl<BmsReportsCfgWriteMapper, BmsReportsCfgDto> implements BmsReportsCfgWriteService {
}
