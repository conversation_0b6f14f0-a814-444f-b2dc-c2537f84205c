package com.jp.med.bms.modules.config.vo;

import lombok.Data;

/**
 * 预算科室映射
 * <AUTHOR>
 * @email -
 * @date 2025-03-25 11:00:18
 */
@Data
public class BmsBudgetDeptMappingVo {

    /** id */
    private Integer id;

    /** 源科室编码 */
    private String sourceDept;

    /** 原科室编码 **/
    private String sourceDeptName;

    /** 映射后科室编码 */
    private String targetDept;

    /** 映射后科室编码 **/
    private String targetDeptName;

    /** 年度 */
    private String year;

    /** 启用标志 1：启用  0：禁用 */
    private String flag;

    /** 创建人 */
    private String crter;

    /** 创建时间 */
    private String createTime;

    /** 映射后科室类型 **/
    private String deptType;

    /** 映射前预算项 **/
    private String sourceBgtCode;

    /** 映射后预算项 **/
    private String targetBgtCode;

    }
