package com.jp.med.bms.modules.config.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 预算报表配置
 * <AUTHOR>
 * @email -
 * @date 2023-12-20 18:10:55
 */
@Data
public class BmsReportsCfgVo {

	/** ID */
	private Integer id;

	/** 报表名称 */
	private String reportsName;

	/** 报表字段编码 */
	private String colTitleCode;

	/** 报表字段标题 */
	private String colTitle;

	/** 报表值 */
	private String colVal;

	/** 是否计算 */
	private String cal;

	/** 创建时间 */
	private String creatTime;

	/** 有效标志 */
	private String activeFlag;

	/** 医疗机构编码 */
	private String hospitalId;

}
