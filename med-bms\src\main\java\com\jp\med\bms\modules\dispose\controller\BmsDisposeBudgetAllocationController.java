package com.jp.med.bms.modules.dispose.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetAllocationDto;
import com.jp.med.bms.modules.dispose.service.read.BmsDisposeBudgetAllocationReadService;
import com.jp.med.bms.modules.dispose.service.write.BmsDisposeBudgetAllocationWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.Map;

/**
 * 预算编制项目分配
 * <AUTHOR>
 * @email -
 * @date 2023-04-25 14:22:29
 */
@Api(value = "预算编制项目分配", tags = "预算编制项目分配")
@RestController
@RequestMapping("dispose/budgetAllocation")
public class BmsDisposeBudgetAllocationController {

    @Autowired
    private BmsDisposeBudgetAllocationReadService disposeBudgetAllocationReadService;

    @Autowired
    private BmsDisposeBudgetAllocationWriteService disposeBudgetAllocationWriteService;

    @ApiOperation("查询预算编制项目分配")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsDisposeBudgetAllocationDto dto){
        return CommonResult.success(disposeBudgetAllocationReadService.queryList(dto));
    }

    @ApiOperation("查询预算编制项目分配")
    @PostMapping("/queryParamSelect")
    public CommonResult<Map<String, Object>> queryParamSelect(@RequestBody BmsDisposeBudgetAllocationDto dto){
        return CommonResult.success(disposeBudgetAllocationReadService.queryParamSelect(dto));
    }

    @ApiOperation("新增预算编制项目分配")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsDisposeBudgetAllocationDto dto){
        disposeBudgetAllocationWriteService.save(dto);
        return CommonResult.success();
    }

    @ApiOperation("修改预算编制项目分配")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsDisposeBudgetAllocationDto dto){
        disposeBudgetAllocationWriteService.updateById(dto);
        return CommonResult.success();
    }

    @ApiOperation("删除预算编制项目分配")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsDisposeBudgetAllocationDto dto){
        disposeBudgetAllocationWriteService.removeById(dto);
        return CommonResult.success();
    }

    @ApiOperation("修改编制项的编制科室")
    @PutMapping("/updateBudgetDept")
    public CommonResult<?> updateBudgetDept(@RequestBody BmsDisposeBudgetAllocationDto dto){
        disposeBudgetAllocationWriteService.updateBudgetDept(dto);
        return CommonResult.success();
    }

    @ApiOperation("查询科室配置")
    @PostMapping("/queryConfig")
    public CommonResult<?> queryConfig(@RequestBody BmsDisposeBudgetAllocationDto dto){
        return CommonResult.success(disposeBudgetAllocationReadService.queryConfig(dto));
    }

    @ApiOperation("查询预算项目的编制科室")
    @PostMapping("/queryAllocation")
    public CommonResult<?> queryAllocation(@RequestBody BmsDisposeBudgetAllocationDto dto){
        return CommonResult.success(disposeBudgetAllocationReadService.queryAllocation(dto));
    }

    @ApiOperation("查询所有编制的项目")
    @PostMapping("/queryAllProj")
    public CommonResult<?> queryAllProj(@RequestBody BmsDisposeBudgetAllocationDto dto){
        return CommonResult.success(disposeBudgetAllocationReadService.queryAllProj(dto));
    }

}
