package com.jp.med.bms.modules.dispose.controller;

import com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetFlowDto;
import com.jp.med.bms.modules.dispose.service.read.BmsDisposeBudgetFlowReadService;
import com.jp.med.bms.modules.dispose.service.write.BmsDisposeBudgetFlowWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;
import java.util.Map;


/**
 * 预算编制流程
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-19 17:21:14
 */
@Api(value = "预算编制流程", tags = "预算编制流程")
@RestController
@RequestMapping("dispose/budgetFlow")
public class BmsDisposeBudgetFlowController {
    @Autowired
    private BmsDisposeBudgetFlowReadService bmsDisposeBudgetFlowReadService;

    @Autowired
    private BmsDisposeBudgetFlowWriteService bmsDisposeBudgetFlowWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询预算编制流程")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsDisposeBudgetFlowDto dto) {
        return CommonResult.success(bmsDisposeBudgetFlowReadService.queryList(dto));
    }


    /**
     * 保存
     */
    @ApiOperation("新增预算编制流程")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsDisposeBudgetFlowDto dto) {
        bmsDisposeBudgetFlowWriteService.saveBudgetFlow(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改预算编制流程")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsDisposeBudgetFlowDto dto) {
        bmsDisposeBudgetFlowWriteService.updateBudgetFlow(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除预算编制流程")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsDisposeBudgetFlowDto dto) {
        bmsDisposeBudgetFlowWriteService.deleteBudgetFlow(dto);
        return CommonResult.success();
    }


    @ApiOperation("查询组织架构")
    @PostMapping("/queryBmsOrg")
    public CommonResult<List<BmsOrgVo>> queryBmsOrg(@RequestBody BmsDisposeBudgetFlowDto dto) {
        return CommonResult.success(bmsDisposeBudgetFlowReadService.queryBmsOrg(dto));
    }

    @ApiOperation("查询流程启动所需")
    @PostMapping("/initProcess")
    public CommonResult<Map<String, Object>> initProcess(@RequestBody BmsDisposeBudgetFlowDto dto) {
        return CommonResult.success(bmsDisposeBudgetFlowReadService.initProcess(dto));
    }

    @ApiOperation("流程启动")
    @PostMapping("/initiateProcess")
    public CommonResult<?> initiateProcess(@RequestBody BmsDisposeBudgetFlowDto dto) {
        bmsDisposeBudgetFlowWriteService.initiateProcess(dto);
        return CommonResult.success();
    }


    @ApiOperation("查询流程明细")
    @PostMapping("/queryFlowDetail")
    public CommonResult<?> queryFlowDetail(@RequestBody BmsDisposeBudgetFlowDto dto) {
        return CommonResult.success(bmsDisposeBudgetFlowReadService.queryFlowDetail(dto));
    }



    /**
     * 列表
     */
    @ApiOperation("查询预算编制任务")
    @PostMapping("/listTask")
    public CommonResult<?> listTask(@RequestBody BmsDisposeBudgetFlowDto dto) {
        return CommonResult.success(bmsDisposeBudgetFlowReadService.queryListTask(dto));
    }
}
