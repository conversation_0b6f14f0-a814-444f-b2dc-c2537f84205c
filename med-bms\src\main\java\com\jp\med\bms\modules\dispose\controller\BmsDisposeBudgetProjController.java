package com.jp.med.bms.modules.dispose.controller;

import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetProjDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetProjVo;
import com.jp.med.bms.modules.dispose.service.read.BmsDisposeBudgetProjReadService;
import com.jp.med.bms.modules.dispose.service.write.BmsDisposeBudgetProjWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * 预算编制项
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-10 15:11:57
 */
@Api(value = "预算编制项", tags = "预算编制项")
@RestController
@RequestMapping("dispose/bmsBudgetProj")
public class BmsDisposeBudgetProjController {

    @Autowired
    private BmsDisposeBudgetProjReadService disposeBmsBudgetProjReadService;

    @Autowired
    private BmsDisposeBudgetProjWriteService disposeBudgetProjWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询预算编制项")
    @PostMapping("/list")
    public CommonResult<List<BmsDisposeBudgetProjVo>> list(@RequestBody BmsDisposeBudgetProjDto dto){
        return CommonResult.success(disposeBmsBudgetProjReadService.queryList(dto));
    }

    @ApiOperation("查询年度预算编制项目")
    @PostMapping("/queryByYear")
    public CommonResult<List<BmsDisposeBudgetProjVo>> queryByYear(@RequestBody BmsDisposeBudgetProjDto dto){
        return CommonResult.success(disposeBmsBudgetProjReadService.queryByYear(dto));
    }

    @ApiOperation("校验编制项是否存在")
    @PostMapping("/checkBudgetProj")
    public CommonResult<?> checkBudgetProj(@RequestBody BmsDisposeBudgetProjDto dto){
        disposeBmsBudgetProjReadService.checkBudgetProj(dto);
        return CommonResult.success();
    }

    @ApiOperation("新增编制项")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsDisposeBudgetProjDto dto){
        disposeBudgetProjWriteService.saveBudgetProj(dto);
        return CommonResult.success();
    }

    @ApiOperation("修改编制项")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsDisposeBudgetProjDto dto){
        disposeBudgetProjWriteService.updateBudgetProj(dto);
        return CommonResult.success();
    }

    @ApiOperation("删除编制项")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsDisposeBudgetProjDto dto){
        disposeBudgetProjWriteService.deleteBudgetProj(dto);
        return CommonResult.success();
    }

    @ApiOperation("查询归口科室")
    @PostMapping("/queryCentralizedDept")
    public CommonResult<?> queryCentralizedDept(@RequestBody BmsDisposeBudgetProjDto dto){
        return CommonResult.success(disposeBmsBudgetProjReadService.queryCentralizedDept(dto));
    }

    @ApiOperation("上传文件")
    @PostMapping("/upload")
    public CommonResult<?> upload(BmsDisposeBudgetProjDto dto, MultipartFile[] files){
        disposeBudgetProjWriteService.upload(dto, files);
        return CommonResult.success();
    }
}
