package com.jp.med.bms.modules.dispose.controller;

import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTableDto;
import com.jp.med.bms.modules.dispose.service.read.BmsDisposeBudgetTableReadService;
import com.jp.med.bms.modules.dispose.service.write.BmsDisposeBudgetTableWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 预算编制表
 * <AUTHOR>
 * @email -
 * @date 2023-04-21 11:42:32
 */
@Api(value = "预算编制表", tags = "预算编制表")
@RestController
@RequestMapping("dispose/budgetTable")
public class BmsDisposeBudgetTableController {

    @Autowired
    private BmsDisposeBudgetTableReadService bmsDisposeBudgetTableReadService;

    @Autowired
    private BmsDisposeBudgetTableWriteService bmsDisposeBudgetTableWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询预算编制表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsDisposeBudgetTableDto dto){
        return CommonResult.paging(bmsDisposeBudgetTableReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增预算编制表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsDisposeBudgetTableDto dto){
        bmsDisposeBudgetTableWriteService.saveTableDetail(dto);
        return CommonResult.success();
    }

    /**
     * 保存
     */
    @ApiOperation("复制预算编制表")
    @PostMapping("/copyBudgetTable")
    public CommonResult<?> copyBudgetTable(@RequestBody BmsDisposeBudgetTableDto dto){
        bmsDisposeBudgetTableWriteService.copyBudgetTable(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改预算编制表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsDisposeBudgetTableDto dto){
        bmsDisposeBudgetTableWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除预算编制表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsDisposeBudgetTableDto dto){
        bmsDisposeBudgetTableWriteService.delete(dto);
        return CommonResult.success();
    }

    @ApiOperation("查询预算编制明细")
    @PostMapping("/queryTableInit")
    public CommonResult<?> queryTableInit(@RequestBody BmsDisposeBudgetTableDto dto){
        return CommonResult.success(bmsDisposeBudgetTableReadService.queryTableInit(dto));
    }

    @ApiOperation("修改预算编制项目")
    @PostMapping("/updateTableProj")
    public CommonResult<?> updateTableProj(@RequestBody BmsDisposeBudgetTableDto dto){
        bmsDisposeBudgetTableWriteService.updateTableProj(dto);
        return CommonResult.success();
    }

}
