package com.jp.med.bms.modules.dispose.controller;

import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTypeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTypeDto;
import com.jp.med.bms.modules.dispose.service.read.BmsDisposeBudgetTypeReadService;
import com.jp.med.bms.modules.dispose.service.write.BmsDisposeBudgetTypeWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * 预算编制项类别
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-10 15:11:57
 */
@Api(value = "预算编制项类别", tags = "预算编制项类别")
@RestController
@RequestMapping("dispose/bmsBudgetType")
public class BmsDisposeBudgetTypeController {
    @Autowired
    private BmsDisposeBudgetTypeReadService bmsDisposeBudgetTypeReadService;

    @Autowired
    private BmsDisposeBudgetTypeWriteService bmsDisposeBudgetTypeWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询预算编制项类别")
    @PostMapping("/list")
    public CommonResult<List<BmsDisposeBudgetTypeVo>> list(@RequestBody BmsDisposeBudgetTypeDto dto){
        return CommonResult.success(bmsDisposeBudgetTypeReadService.queryList(dto));
    }

    @ApiOperation("查询预算编制项类别")
    @PostMapping("/querySelectTree")
    public CommonResult<List<BmsDisposeBudgetTypeVo>> querySelectTree(@RequestBody BmsDisposeBudgetTypeDto dto){
        return CommonResult.success(bmsDisposeBudgetTypeReadService.querySelectTree(dto));
    }

    @ApiOperation("通过编码查询编制项")
    @PostMapping("/queryByCode")
    public CommonResult<List<BmsDisposeBudgetTypeVo>> queryByCode(@RequestBody BmsDisposeBudgetTypeDto dto){
        return CommonResult.success(bmsDisposeBudgetTypeReadService.queryByCode(dto));
    }


    /**
     * 保存
     */
    @ApiOperation("新增预算编制项类别")
    @PostMapping("/save")
    public CommonResult<BmsDisposeBudgetTypeVo> save(@RequestBody BmsDisposeBudgetTypeDto dto){
        bmsDisposeBudgetTypeWriteService.saveBmsBudgetType(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改预算编制项类别")
    @PutMapping("/update")
    public CommonResult<BmsDisposeBudgetTypeVo> update(@RequestBody BmsDisposeBudgetTypeDto dto){
        bmsDisposeBudgetTypeWriteService.updateBmsBudgetType(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除预算编制项类别")
    @DeleteMapping("/delete")
    public CommonResult<BmsDisposeBudgetTypeVo> delete(@RequestBody BmsDisposeBudgetTypeDto dto){
        bmsDisposeBudgetTypeWriteService.deleteBmsBudgetType(dto);
        return CommonResult.success();
    }

    /**
     * 文件导入
     */
    @ApiOperation("文件导入")
    @PostMapping("/upload")
    public CommonResult<BmsDisposeBudgetTypeVo> upload(BmsDisposeBudgetTypeDto dto, MultipartFile[] files){
        bmsDisposeBudgetTypeWriteService.uploadFile(dto, files);
        return CommonResult.success();
    }

}
