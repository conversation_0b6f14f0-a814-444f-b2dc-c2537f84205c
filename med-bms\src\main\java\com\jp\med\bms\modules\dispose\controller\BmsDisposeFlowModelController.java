package com.jp.med.bms.modules.dispose.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.bms.modules.dispose.dto.BmsDisposeFlowModelDto;
import com.jp.med.bms.modules.dispose.service.read.BmsDisposeFlowModelReadService;
import com.jp.med.bms.modules.dispose.service.write.BmsDisposeFlowModelWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 流程模型
 * <AUTHOR>
 * @email -
 * @date 2023-05-25 19:07:55
 */
@Api(value = "流程模型", tags = "流程模型")
@RestController
@RequestMapping("dispose/flowModel")
public class BmsDisposeFlowModelController {

    @Autowired
    private BmsDisposeFlowModelReadService disposeFlowModelReadService;

    @Autowired
    private BmsDisposeFlowModelWriteService disposeFlowModelWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询流程模型")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsDisposeFlowModelDto dto){
        return CommonResult.paging(disposeFlowModelReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增流程模型")
    @PostMapping("/save")
    public CommonResult<?> save(BmsDisposeFlowModelDto dto){
        disposeFlowModelWriteService.saveModel(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改流程模型")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsDisposeFlowModelDto dto){
        disposeFlowModelWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除流程模型")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsDisposeFlowModelDto dto){
        disposeFlowModelWriteService.deleteById(dto);
        return CommonResult.success();
    }

}
