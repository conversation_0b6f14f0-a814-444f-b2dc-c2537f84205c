package com.jp.med.bms.modules.dispose.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;
import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * 预算编制项目分配
 * <AUTHOR>
 * @email -
 * @date 2023-04-25 14:22:29
 */
@Data
@TableName("bms_budget_allocation" )
public class BmsDisposeBudgetAllocationDto extends CommonQueryDto {

    /** 预算编制项目分配ID */
    @TableId("budget_allocation_id")
    private Integer budgetAllocationId;

    /** 预算编制项目 */
    @TableField("budget_code")
    private String budgetCode;

    /** 编制科室 */
    @TableField("org_id")
    private String orgId;

    /** 预算编制表 */
    @TableField("budget_table_id")
    private Integer budgetTableId;

    /** 预算编制表ID */
    @TableField(exist = false)
    private Integer budgetTableIdNew;

    /** 编制科室 */
    @TableField("execute_dept")
    private String executeDept;

    /** 计算公式 */
    @TableField("formula")
    private String formula;

    /** 计算公式说明 */
    @TableField("formula_label")
    private String formulaLabel;

    /** 是否计算 */
    @TableField("cal")
    private String cal;

    /** 预算项目类别 */
    @TableField(exist = false)
    private String budgetTypeCode;

    /** 编制项名称 */
    @TableField(exist = false)
    private String budgetName;

    /** 原始的分配科室 */
    @TableField(exist = false)
    private String[] deptList;

    /** 新的分配科室 */
    @TableField(exist = false)
    private String[] newDeptList;

    /** 下级数据 */
    @TableField(exist = false)
    private List<BmsDisposeBudgetAllocationDto> children;

    /** 分配信息 */
    @TableField(exist = false)
    private List<BmsDisposeBudgetAllocationDto> list;

    /** 是否是叶子节点  1：是 */
    @TableField(exist = false)
    private String isLeaf;

    @TableField(exist = false)
    private Integer startPage;

    @TableField(exist = false)
    private Integer endPage;


}
