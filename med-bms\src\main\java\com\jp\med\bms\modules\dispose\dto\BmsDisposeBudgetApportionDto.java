package com.jp.med.bms.modules.dispose.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 预算编制项分配表
 * <AUTHOR>
 * @email -
 * @date 2023-04-21 18:02:22
 */
@Data
@TableName("bms_budget_apportion" )
public class BmsDisposeBudgetApportionDto extends CommonQueryDto {

    /** 预算编制项分配表ID */
    @TableId("id")
    private Integer id;

    /** 预算编制项分配名称 */
    @TableField("budget_apportion_name")
    private String budgetApportionName;

    /** 备注 */
    @TableField("remark")
    private String remark;

    /** 状态（1：启用，0：不启用） */
    @TableField("flag")
    private String flag;

    /** 医疗机构编码 */
    @TableField("hospital_id")
    private String hospitalId;

    /** 创建时间 */
    @TableField("createtime")
    private String createtime;

    /** 默认状态（1：默认，0：非默认） */
    @TableField("default_flag")
    private String defaultFlag;

}
