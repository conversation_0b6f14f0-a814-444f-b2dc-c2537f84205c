package com.jp.med.bms.modules.dispose.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.util.List;

/**
 * 预算编制流程
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-19 17:21:14
 */
@Data
@TableName("bms_budget_flow" )
public class BmsDisposeBudgetFlowDto extends CommonQueryDto {

    /** 流程ID */
    @TableId(value = "budget_flow_id", type = IdType.AUTO )
    private Integer budgetFlowId;

    /** 预算编制流程编码 */
    @TableField("budget_flow_code")
    private String budgetFlowCode;

    /** 预算编制流程名称 */
    @TableField("budget_flow_name")
    private String budgetFlowName;

    /** 医疗机构编码 */
    @TableField("hospital_id")
    private String hospitalId;

    /** 备注 */
    @TableField("remarks")
    private String remarks;

    /** 状态 */
    @TableField("flag")
    private String flag;

    /**创建时间*/
    @TableField("create_time")
    private String createTime;

    /** 流程节点信息*/
    @TableField(exist = false)
    List<BmsDisposeFlowDetailDto> detail;

}
