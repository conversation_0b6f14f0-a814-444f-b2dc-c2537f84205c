package com.jp.med.bms.modules.dispose.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Getter;
import lombok.Setter;

/**
 * 预算编制项类别
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-10 15:11:57
 */
@Getter
@Setter
public class BmsDisposeBudgetProjDto extends CommonQueryDto {

    /** 预算编制项类别ID */
    @TableId
    private Integer budgetProjId;

    /** 预算编制项编码 */
    @Excel(name = "预算编制项编码")
    private String budgetCode;

    /** 预算编制项名称 */
    @Excel(name = "预算编制项名称")
    private String budgetName;

    /** 上级编码 */
    @Excel(name = "上级编码")
    private String budgetParentId;

    /**预算编制项类别*/
    @Excel(name = "编制项类别编码")
    private String budgetTypeCode;

    /**归口科室*/
    @Excel(name = "归口科室")
    private String centralizedDept;

    /** 启用状态 */
    private String flag;

    /**计量单位**/
    @Excel(name = "计量单位")
    private String unit;

    /** 主要内容 */
    private String cont;
    /** 说明 */
    private String dscr;

    /** 预算任务编码 */
    @TableField(exist = false)
    private String budgetTaskCode;

    @TableField(exist = false)
    private Long budgetFlowId;

    /** 年度 */
    private String year;

    /** 预算年度 */
    private String budgetYear;

}
