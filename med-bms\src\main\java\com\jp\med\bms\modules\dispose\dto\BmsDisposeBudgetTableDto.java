package com.jp.med.bms.modules.dispose.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.util.List;

/**
 * 预算编制表
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-21 11:42:32
 */
@Data
@TableName("bms_budget_table")
public class BmsDisposeBudgetTableDto extends CommonQueryDto {

    /**
     * 预算编制表ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 预算编制表名称
     */
    @TableField("budget_table_name")
    private String budgetTableName;

    /**
     * 备注
     */
    @TableField(value = "remark", updateStrategy = FieldStrategy.IGNORED, insertStrategy = FieldStrategy.IGNORED)
    private String remark;

    /**
     * 状态
     */
    @TableField("flag")
    private String flag;

    /**
     * 医疗机构编码
     */
    @TableField("hospital_id")
    private String hospitalId;

    /**
     * 编制项目
     */
    @TableField(exist = false)
    private List<String> proj;

    /**
     * 编制项目(带父级项目)
     */
    @TableField(exist = false)
    private List<String> allProjKey;

    /**
     * 预算编制明细
     */
    @TableField(exist = false)
    private List<BmsDisposeBudgetTableProjDto> tableProjDtoList;

    /**
     * 预算年度
     */
    @TableField("budget_year")
    private String budgetYear;

}
