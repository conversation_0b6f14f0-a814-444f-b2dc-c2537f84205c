package com.jp.med.bms.modules.dispose.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;
import java.util.List;

import lombok.Data;

/**
 * 预算对应编制项目
 * <AUTHOR>
 * @email -
 * @date 2023-05-31 11:35:42
 */
@Data
@TableName("bms_budget_table_proj" )
public class BmsDisposeBudgetTableProjDto extends CommonQueryDto {

    /** 预算编制ID */
    @TableField("budget_proj_id")
    private Integer budgetProjId;

    /** 预算编制项编码 */
    @TableField("budget_code")
    private String budgetCode;

    /** 预算编制项名称 */
    @TableField("budget_name")
    private String budgetName;

    /** 上级编制项编码 */
    @TableField("budget_parent_id")
    private String budgetParentId;

    /** 预算编制类别 */
    @TableField("budget_type_code")
    private String budgetTypeCode;

    /** 归口科室 */
    @TableField("centralized_dept")
    private String centralizedDept;

    /** 计量单位 */
    @TableField("unit")
    private String unit;

    /** 预算编制表ID */
    @TableField("budget_table_id")
    private Integer budgetTableId;

    /** 预算编制表ID */
    @TableField(exist = false)
    private Integer budgetTableIdNew;

    /** 主要内容 */
    @TableField("cont")
    private String cont;

    /** 说明 */
    @TableField("dscr")
    private String dscr;

    /** 是否经济科目 */
    @TableField("econ_sub")
    private String econSub;

    @TableField(exist = false)
    List<Integer> budgetTableIds;

}
