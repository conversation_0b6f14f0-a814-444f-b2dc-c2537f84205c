package com.jp.med.bms.modules.dispose.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.annotations.DateFormat;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 预算编制项类别
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-10 15:11:57
 */
@Data
@TableName("bms_budget_type" )
public class BmsDisposeBudgetTypeDto extends CommonQueryDto {

    /** 预算编制项类别ID */
    @TableId("budget_type_id")
    private Integer budgetTypeId;

    /** 预算编制项类别编码 */
    @TableField("budget_type_code")
    @Excel(name = "编制项类别编码")
    private String budgetTypeCode;

    /** 预算编制项类别名称 */
    @TableField("budget_type_name")
    @Excel(name = "编制项类别名称")
    private String budgetTypeName;

    /** 上级编码 */
    @TableField("budget_type_parent_id")
    @Excel(name = "上级编码")
    private String budgetTypeParentId;

    /** 启用状态 */
    @TableField("flag")
    @Excel(name = "启用状态")
    private String flag;

    @Excel(name = "时间")
    @DateFormat(value = "yyyy/mm/dd")
    private Date date;

    /**医疗机构编码*/
    @TableField("hospital_id")
    private String hospitalId;

    /**医疗机构编码*/
    @TableField(exist = false)
    private String key;

}
