package com.jp.med.bms.modules.dispose.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;
import java.math.BigDecimal;

import lombok.Data;

/**
 * 预算编制流程详情
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-21 16:08:13
 */
@Data
@TableName("bms_flow_detail" )
public class BmsDisposeFlowDetailDto extends CommonQueryDto {

    /** 预算编制流程详情ID */
    @TableId("flow_detail_id")
    private Integer flowDetailId;

    /** 编制节点编码 */
    @TableField("flow_detail_code")
    private String flowDetailCode;

    /** 编制节点名称 */
    @TableField("flow_detail_name")
    private String flowDetailName;

    /** 流程顺序 */
    @TableField("organization_order")
    private Integer organizationOrder;

    /** 执行者 */
    @TableField("ptr")
    private String ptr;

    /** 执行标准 */
    @TableField("exestd")
    private String exestd;

    /** 预算编制流程 */
    @TableField("budget_flow_code")
    private String budgetFlowCode;

    /** 概要说明 */
    @TableField("abst")
    private String abst;



}
