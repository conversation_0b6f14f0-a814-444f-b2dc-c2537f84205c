package com.jp.med.bms.modules.dispose.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import java.util.Date;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 流程模型
 * <AUTHOR>
 * @email -
 * @date 2023-05-25 19:07:55
 */
@Data
@TableName("bms_flow_model" )
public class BmsDisposeFlowModelDto extends CommonQueryDto {

    /** 流程模型ID */
    @TableId("flow_model_id")
    private Long flowModelId;

    /** 流程模板编码 */
    @TableField("flow_mode_code")
    private String flowModeCode;

    /** 流程模板名称 */
    @TableField("flow_mode_name")
    private String flowModeName;

    /** 创建人 */
    @TableField("username")
    private String username;

    /** 创建时间 */
    @TableField("create_time")
    private Date createTime;

    /** 流程图例 */
    @TableField("model_case")
    private String modelCase;

    /** 启用状态 */
    @TableField("flag")
    private String flag;

    /** 医疗机构编码 */
    @TableField("hospital_id")
    private String hospitalId;

    /** 流程实例文件*/
    @TableField(exist = false)
    private MultipartFile file;

}
