package com.jp.med.bms.modules.dispose.mapper.read;

import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetAllocationDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetAllocationVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 预算编制项目分配
 * <AUTHOR>
 * @email -
 * @date 2023-04-25 14:22:29
 */
@Mapper
public interface BmsDisposeBudgetAllocationReadMapper extends BaseMapper<BmsDisposeBudgetAllocationDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsDisposeBudgetAllocationVo> queryList(BmsDisposeBudgetAllocationDto dto);

    /**
     * 通过编制表查询编制项目明细
     * @param dto
     * @return
     */
    List<BmsDisposeBudgetAllocationVo> queryAllocation(BmsDisposeBudgetAllocationDto dto);
}
