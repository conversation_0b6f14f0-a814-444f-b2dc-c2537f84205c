package com.jp.med.bms.modules.dispose.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetApportionDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetApportionVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 预算编制项分配表
 * <AUTHOR>
 * @email -
 * @date 2023-04-21 18:02:22
 */
@Mapper
public interface BmsDisposeBudgetApportionReadMapper extends BaseMapper<BmsDisposeBudgetApportionDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsDisposeBudgetApportionVo> queryList(BmsDisposeBudgetApportionDto dto);
}
