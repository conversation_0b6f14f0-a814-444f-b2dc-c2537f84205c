package com.jp.med.bms.modules.dispose.mapper.read;

import com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetFlowDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetFlowVo;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 预算编制流程
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-04-19 17:21:14
 */
@Mapper
public interface BmsDisposeBudgetFlowReadMapper extends BaseMapper<BmsDisposeBudgetFlowDto> {
    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsDisposeBudgetFlowVo> queryList(BmsDisposeBudgetFlowDto dto);

    /**
     * 查询组织架构
     * @param dto
     * @return
     */
    List<BmsOrgVo> queryBmsOrg(BmsDisposeBudgetFlowDto dto);

    List<BmsDisposeBudgetFlowVo> queryListTask(BmsDisposeBudgetFlowDto dto);
}
