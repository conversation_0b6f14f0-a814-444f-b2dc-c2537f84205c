package com.jp.med.bms.modules.dispose.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetDataDto;
import com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetProjDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetProjVo;
import com.jp.med.bms.modules.dispose.vo.TitleVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 预算编制项类别
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-10 15:11:57
 */
@Mapper
public interface BmsDisposeBudgetProjReadMapper extends BaseMapper<BmsDisposeBudgetProjDto> {
    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsDisposeBudgetProjVo> queryList(BmsDisposeBudgetProjDto dto);

    /**
     * 查询归口科室
     * @param dto
     * @return
     */
    List<BmsOrgVo> queryCentralizedDept(BmsDisposeBudgetProjDto dto);


    /**
     * 查询上级编制项下拉菜单
     *
     * @param dto
     * @return
     */
    List<BmsDisposeBudgetProjVo> queryProjTree(BmsDisposeBudgetProjDto dto);

    /**
     * 查询所有叶子节点数据
     * @param dto
     * @return
     */
    List<BmsDisposeBudgetProjVo> queryLeafProj(BmsDisposeBudgetProjDto dto);

    /**
     * 查询非叶子节点数据
     * @param dto
     * @return
     */
    List<BmsDisposeBudgetProjVo> queryParentProj(BmsDisposeBudgetProjDto dto);

    /**
     * 查询表头
     * @param dto
     * @return
     */
    List<TitleVo> queryTitle(BmsExecuteBudgetDataDto dto);

    /**
     * 查询基准表头
     * @param dto
     * @return
     */
    List<TitleVo> queryNormalTitle(BmsExecuteBudgetDataDto dto);

    /**
     * 校验编制项存在
     * @param dto
     * @return
     */
    List<BmsDisposeBudgetProjVo> queryBudgetProj(BmsDisposeBudgetProjDto dto);

    /**
     * 查询预算编制项
     * @param dto
     * @return
     */
    List<BmsDisposeBudgetProjVo> queryByYear(BmsDisposeBudgetProjDto dto);

    /**
     * 查询Excel的表头对应
     * @param dto
     * @return
     */
    List<BmsDisposeBudgetProjVo> queryExcelTitle(BmsExecuteBudgetDataDto dto);
}
