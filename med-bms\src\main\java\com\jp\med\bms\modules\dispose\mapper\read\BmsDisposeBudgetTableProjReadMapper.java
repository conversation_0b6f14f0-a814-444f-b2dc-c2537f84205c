package com.jp.med.bms.modules.dispose.mapper.read;

import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTableProjDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTableProjVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 预算对应编制项目
 * <AUTHOR>
 * @email -
 * @date 2023-05-31 11:35:42
 */
@Mapper
public interface BmsDisposeBudgetTableProjReadMapper extends BaseMapper<BmsDisposeBudgetTableProjDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsDisposeBudgetTableProjVo> queryList(BmsDisposeBudgetTableProjDto dto);

    /**
     * 查询非叶子节点
     * @param dto
     * @return
     */
    List<BmsDisposeBudgetTableProjVo> queryParent(BmsDisposeBudgetTableProjDto dto);

    /**
     * 根据ID查询
     * @param dto
     * @return
     */
    List<BmsDisposeBudgetTableProjVo> queryById(BmsDisposeBudgetTableProjDto dto);

    /**
     * 查询所有叶子
     * @param dto
     * @return
     */
    List<BmsDisposeBudgetTableProjVo> queryLeafById(BmsDisposeBudgetTableProjDto dto);



}
