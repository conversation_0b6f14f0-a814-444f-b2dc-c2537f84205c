package com.jp.med.bms.modules.dispose.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTableDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTableVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 预算编制表
 * <AUTHOR>
 * @email -
 * @date 2023-04-21 11:42:32
 */
@Mapper
public interface BmsDisposeBudgetTableReadMapper extends BaseMapper<BmsDisposeBudgetTableDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsDisposeBudgetTableVo> queryList(BmsDisposeBudgetTableDto dto);
}
