package com.jp.med.bms.modules.dispose.mapper.read;

import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetFlowDto;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeFlowDetailDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeFlowDetailVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 预算编制流程详情
 * <AUTHOR>
 * @email -
 * @date 2023-04-26 16:44:07
 */
@Mapper
public interface BmsDisposeFlowDetailReadMapper extends BaseMapper<BmsDisposeFlowDetailDto> {

    /**
     * 查询流程执行的最小值
     * @param dto
     * @return
    */
    List<BmsDisposeFlowDetailVo> queryFirstOrder(BmsDisposeFlowDetailDto dto);

    /**
     * 查询流程明细情况
     * @param dto
     * @return
     */
    List<BmsDisposeFlowDetailVo> queryFlowDetail(BmsDisposeFlowDetailDto dto);

    /**
     * 通过编码查询流程信息
     * @param dto
     * @return
     */
    BmsDisposeFlowDetailVo queryByCode(BmsDisposeFlowDetailDto dto);
}
