package com.jp.med.bms.modules.dispose.mapper.read;

import com.jp.med.bms.modules.dispose.dto.BmsDisposeFlowModelDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeFlowModelVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 流程模型
 * <AUTHOR>
 * @email -
 * @date 2023-05-25 19:07:55
 */
@Mapper
public interface BmsDisposeFlowModelReadMapper extends BaseMapper<BmsDisposeFlowModelDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsDisposeFlowModelVo> queryList(BmsDisposeFlowModelDto dto);
}
