package com.jp.med.bms.modules.dispose.mapper.write;

import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetAllocationDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 预算编制项目分配
 * <AUTHOR>
 * @email -
 * @date 2023-04-25 14:22:29
 */
@Mapper
public interface BmsDisposeBudgetAllocationWriteMapper extends BaseMapper<BmsDisposeBudgetAllocationDto> {

    /**
     * 删除已配置的编制科室
     * @return
     */
    int deleteBudgetDept(BmsDisposeBudgetAllocationDto dto);

    /**
     * 通过预算编制表删除分配信息
     * @param dto
     * @return
     */
    int deleteByTableId(BmsDisposeBudgetAllocationDto dto);

    /**
     * 查询写入
     * @param dto
     */
    void insertBySelect(BmsDisposeBudgetAllocationDto dto);
}
