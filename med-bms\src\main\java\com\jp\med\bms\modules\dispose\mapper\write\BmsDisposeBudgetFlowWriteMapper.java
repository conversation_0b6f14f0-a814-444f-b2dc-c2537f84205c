package com.jp.med.bms.modules.dispose.mapper.write;

import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetFlowDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 预算编制流程
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-04-19 17:21:14
 */
@Mapper
public interface BmsDisposeBudgetFlowWriteMapper extends BaseMapper<BmsDisposeBudgetFlowDto> {
    /**
    * 修改
    * @param dto
    */
    void updateBudgetFlow(BmsDisposeBudgetFlowDto dto);

    /**
    * 新增
    * @param dto
    */
    int saveBudgetFlow(BmsDisposeBudgetFlowDto dto);
}
