package com.jp.med.bms.modules.dispose.mapper.write;

import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetProjDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 预算编制项
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-04-18 11:42:16
 */
@Mapper
public interface BmsDisposeBudgetProjWriteMapper extends BaseMapper<BmsDisposeBudgetProjDto> {
    /**
    * 修改
    * @param dto
    */
    void updateBudgetProj(BmsDisposeBudgetProjDto dto);

    /**
    * 新增
    * @param dto
    */
    void saveBudgetProj(BmsDisposeBudgetProjDto dto);

    /**
     * 删除
     * @param dto
     * @return
     */
    int deleteBudgetProj(BmsDisposeBudgetProjDto dto);
}
