package com.jp.med.bms.modules.dispose.mapper.write;

import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTypeDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 预算编制项类别
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-04-10 15:11:57
 */
@Mapper
public interface BmsDisposeBudgetTypeWriteMapper extends BaseMapper<BmsDisposeBudgetTypeDto> {
    /**
    * 修改
    * @param dto
    */
    void updateBmsBudgetType(BmsDisposeBudgetTypeDto dto);

    /**
    * 新增
    * @param dto
    */
    void saveBmsBudgetType(BmsDisposeBudgetTypeDto dto);

    /**
     * 删除
     * @param dto
     */
    int deleteBmsBudgetType(BmsDisposeBudgetTypeDto dto);
}
