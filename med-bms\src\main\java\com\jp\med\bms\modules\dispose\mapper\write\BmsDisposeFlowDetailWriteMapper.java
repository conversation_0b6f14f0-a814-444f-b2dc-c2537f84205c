package com.jp.med.bms.modules.dispose.mapper.write;

import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetDataDto;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeFlowDetailDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 预算编制流程详情
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-04-21 16:08:13
 */
@Mapper
public interface BmsDisposeFlowDetailWriteMapper extends BaseMapper<BmsDisposeFlowDetailDto> {
    /**
     * 删除流程明细表
     * @param dto
     * @return
     */
    int deleteByFlow(BmsDisposeFlowDetailDto dto);

    /**
     * 修改流程节点状态
     * @param dto
     * @return
     */
    int updateByFlow(BmsDisposeFlowDetailDto dto);


    /**
     * 修改流程节点执行状态
     * @param dto
     */
    int updateFlowDetailStatus(BmsDisposeFlowDetailDto dto);
}
