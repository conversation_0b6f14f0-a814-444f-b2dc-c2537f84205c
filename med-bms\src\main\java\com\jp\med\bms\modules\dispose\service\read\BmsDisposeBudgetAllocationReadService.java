package com.jp.med.bms.modules.dispose.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetAllocationDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetAllocationVo;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTableProjVo;

import java.util.List;
import java.util.Map;

/**
 * 预算编制项目分配
 * <AUTHOR>
 * @email -
 * @date 2023-04-25 14:22:29
 */
public interface BmsDisposeBudgetAllocationReadService extends IService<BmsDisposeBudgetAllocationDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsDisposeBudgetAllocationVo> queryList(BmsDisposeBudgetAllocationDto dto);

    /**
     * 查询页面下拉选
     * @param dto
     * @return
     */
    Map<String, Object> queryParamSelect(BmsDisposeBudgetAllocationDto dto);

    Map<String,List<BmsOrgVo>> queryConfig(BmsDisposeBudgetAllocationDto dto);

    /**
     * 查询预算项目的编制科室
     * @param dto
     * @return
     */
    List<BmsDisposeBudgetAllocationVo> queryAllocation(BmsDisposeBudgetAllocationDto dto);

    /**
     * 查询所有编制的项目
     * @param dto
     * @return
     */
    List<BmsDisposeBudgetTableProjVo> queryAllProj(BmsDisposeBudgetAllocationDto dto);
}

