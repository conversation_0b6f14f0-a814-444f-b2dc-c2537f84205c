package com.jp.med.bms.modules.dispose.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetApportionDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetApportionVo;

import java.util.List;

/**
 * 预算编制项分配表
 * <AUTHOR>
 * @email -
 * @date 2023-04-21 18:02:22
 */
public interface BmsDisposeBudgetApportionReadService extends IService<BmsDisposeBudgetApportionDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsDisposeBudgetApportionVo> queryList(BmsDisposeBudgetApportionDto dto);
}

