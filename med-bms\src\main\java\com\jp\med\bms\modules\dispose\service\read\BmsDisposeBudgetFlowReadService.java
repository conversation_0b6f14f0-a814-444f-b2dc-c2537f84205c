package com.jp.med.bms.modules.dispose.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetFlowDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetFlowVo;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeFlowDetailVo;

import java.util.List;
import java.util.Map;

/**
 * 预算编制流程
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-19 17:21:14
 */
public interface BmsDisposeBudgetFlowReadService extends IService<BmsDisposeBudgetFlowDto> {
    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsDisposeBudgetFlowVo> queryList(BmsDisposeBudgetFlowDto dto);

    /**
     * 查询组织架构
     * @param dto
     * @return
     */
    List<BmsOrgVo> queryBmsOrg(BmsDisposeBudgetFlowDto dto);

    /**
     * 初始化查询
     * @param dto
     * @return
     */
    Map<String, Object> initProcess(BmsDisposeBudgetFlowDto dto);

    /**
     * 查询流程明细情况
     * @param dto
     * @return
     */
    List<BmsDisposeFlowDetailVo> queryFlowDetail(BmsDisposeBudgetFlowDto dto);

    /**
     * 查询列表
     * @param dto
     * @return
     */
    List<BmsDisposeBudgetFlowVo> queryListTask(BmsDisposeBudgetFlowDto dto);
}

