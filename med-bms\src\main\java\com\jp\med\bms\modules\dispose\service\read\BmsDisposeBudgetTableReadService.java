package com.jp.med.bms.modules.dispose.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTableDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTableVo;

import java.util.List;
import java.util.Map;

/**
 * 预算编制表
 * <AUTHOR>
 * @email -
 * @date 2023-04-21 11:42:32
 */
public interface BmsDisposeBudgetTableReadService extends IService<BmsDisposeBudgetTableDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsDisposeBudgetTableVo> queryList(BmsDisposeBudgetTableDto dto);

    /**
     * 查询新增页面的初始化数据
     * @param dto
     * @return
     */
    Map<String, Object> queryTableInit(BmsDisposeBudgetTableDto dto);
}

