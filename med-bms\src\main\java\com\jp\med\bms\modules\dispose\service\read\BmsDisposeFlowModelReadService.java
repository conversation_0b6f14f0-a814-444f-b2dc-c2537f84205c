package com.jp.med.bms.modules.dispose.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeFlowModelDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeFlowModelVo;

import java.util.List;

/**
 * 流程模型
 * <AUTHOR>
 * @email -
 * @date 2023-05-25 19:07:55
 */
public interface BmsDisposeFlowModelReadService extends IService<BmsDisposeFlowModelDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsDisposeFlowModelVo> queryList(BmsDisposeFlowModelDto dto);
}

