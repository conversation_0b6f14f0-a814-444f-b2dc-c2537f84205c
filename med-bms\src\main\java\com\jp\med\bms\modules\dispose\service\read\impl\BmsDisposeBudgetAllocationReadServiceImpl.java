package com.jp.med.bms.modules.dispose.service.read.impl;

import com.jp.med.bms.modules.bmsOrg.dto.BmsOrgDto;
import com.jp.med.bms.modules.bmsOrg.mapper.read.BmsOrgReadMapper;
import com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTableDto;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTableProjDto;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetTableProjReadMapper;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetTableReadMapper;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTableProjVo;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTableVo;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.TreeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetAllocationReadMapper;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetAllocationDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetAllocationVo;
import com.jp.med.bms.modules.dispose.service.read.BmsDisposeBudgetAllocationReadService;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class BmsDisposeBudgetAllocationReadServiceImpl extends ServiceImpl<BmsDisposeBudgetAllocationReadMapper, BmsDisposeBudgetAllocationDto> implements BmsDisposeBudgetAllocationReadService {

    @Autowired
    private BmsDisposeBudgetAllocationReadMapper disposeBudgetAllocationReadMapper;

    @Autowired
    private BmsDisposeBudgetTableReadMapper bmsDisposeBudgetTableReadMapper;

    @Autowired
    private BmsDisposeBudgetTableProjReadMapper bmsDisposeBudgetTableProjReadMapper;

    @Autowired
    private BmsOrgReadMapper bmsOrgReadMapper;

    @Override
    public List<BmsDisposeBudgetAllocationVo> queryList(BmsDisposeBudgetAllocationDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        List<BmsDisposeBudgetAllocationVo> vos = disposeBudgetAllocationReadMapper.queryList(dto);
        if (vos.isEmpty()){
            return new ArrayList<>();
        }
        return TreeUtil.getTreeNode(vos, "budgetCode", "budgetParentId");
    }

    /**
     * 查询页面下拉选
     *
     * @param dto
     * @return
     */
    @Override
    public Map<String, Object> queryParamSelect(BmsDisposeBudgetAllocationDto dto) {
        Map<String, Object> map = new HashMap<>();
        List<BmsDisposeBudgetTableVo> tableVos = bmsDisposeBudgetTableReadMapper.queryList(new BmsDisposeBudgetTableDto());
        List<BmsOrgVo> bmsOrgVos = bmsOrgReadMapper.queryDept(new BmsOrgDto());
        if (!Objects.isNull(bmsOrgVos)) {
            bmsOrgVos = bmsOrgVos.stream().filter(bmsOrgVo -> MedConst.BUDGET_DEPT_TYPE_1.equals(bmsOrgVo.getDeptType())).collect(Collectors.toList());
        }
        map.put("dept", bmsOrgVos);
        map.put("table", tableVos);
        return map;
    }

    @Override
    public Map<String, List<BmsOrgVo>> queryConfig(BmsDisposeBudgetAllocationDto dto) {
        List<BmsOrgVo> orgVos = bmsOrgReadMapper.queryAllDept(new BmsOrgDto());
        List<BmsOrgVo> bmsOrgVos = bmsOrgReadMapper.queryParentOrg(new BmsOrgDto());
        Map<String, List<BmsOrgVo>> resultMap = new HashMap<>();
        resultMap.put("all",orgVos);
        resultMap.put("parent",bmsOrgVos);
        return resultMap;
    }

    @Override
    public List<BmsDisposeBudgetAllocationVo> queryAllocation(BmsDisposeBudgetAllocationDto dto) {
        return disposeBudgetAllocationReadMapper.queryAllocation(dto);
    }

    @Override
    public List<BmsDisposeBudgetTableProjVo> queryAllProj(BmsDisposeBudgetAllocationDto dto) {
        BmsDisposeBudgetTableProjDto tableProjDto = new BmsDisposeBudgetTableProjDto();
        tableProjDto.setBudgetTableId(dto.getBudgetTableId());
        return bmsDisposeBudgetTableProjReadMapper.queryLeafById(tableProjDto);
    }

}
