package com.jp.med.bms.modules.dispose.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetApportionDto;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetApportionReadMapper;
import com.jp.med.bms.modules.dispose.service.read.BmsDisposeBudgetApportionReadService;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetApportionVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional(readOnly = true)
@Service
public class BmsDisposeBudgetApportionReadServiceImpl extends ServiceImpl<BmsDisposeBudgetApportionReadMapper, BmsDisposeBudgetApportionDto> implements BmsDisposeBudgetApportionReadService {

    @Autowired
    private BmsDisposeBudgetApportionReadMapper bmsDisposeBudgetApportionReadMapper;

    @Override
    public List<BmsDisposeBudgetApportionVo> queryList(BmsDisposeBudgetApportionDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return bmsDisposeBudgetApportionReadMapper.queryList(dto);
    }

}
