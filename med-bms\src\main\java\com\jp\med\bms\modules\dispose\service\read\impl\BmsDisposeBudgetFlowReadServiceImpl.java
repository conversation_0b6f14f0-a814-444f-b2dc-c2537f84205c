package com.jp.med.bms.modules.dispose.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetFlowDto;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTableDto;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeFlowDetailDto;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetFlowReadMapper;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetTableReadMapper;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeFlowDetailReadMapper;
import com.jp.med.bms.modules.dispose.service.read.BmsDisposeBudgetFlowReadService;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetFlowVo;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTableVo;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeFlowDetailVo;
import com.jp.med.common.constant.MedConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Transactional(readOnly = true)
@Service
public class BmsDisposeBudgetFlowReadServiceImpl extends ServiceImpl<BmsDisposeBudgetFlowReadMapper, BmsDisposeBudgetFlowDto> implements BmsDisposeBudgetFlowReadService {

    @Autowired
    private BmsDisposeBudgetFlowReadMapper bmsDisposeBudgetFlowReadMapper;

    @Autowired
    private BmsDisposeBudgetTableReadMapper bmsDisposeBudgetTableReadMapper;

    @Autowired
    private BmsDisposeFlowDetailReadMapper bmsDisposeFlowDetailReadMapper;

    @Override
    public List<BmsDisposeBudgetFlowVo> queryList(BmsDisposeBudgetFlowDto dto) {
        return bmsDisposeBudgetFlowReadMapper.queryList(dto);
    }

    @Override
    public List<BmsDisposeBudgetFlowVo> queryListTask(BmsDisposeBudgetFlowDto dto) {
        return bmsDisposeBudgetFlowReadMapper.queryListTask(dto);
    }

    @Override
    public List<BmsOrgVo> queryBmsOrg(BmsDisposeBudgetFlowDto dto) {
        return bmsDisposeBudgetFlowReadMapper.queryBmsOrg(dto);
    }

    @Override
    public Map<String, Object> initProcess(BmsDisposeBudgetFlowDto dto) {
        Map<String, Object> resultMap = new HashMap<>();
        dto.setFlag(MedConst.ACTIVE_FLAG_0);
        List<BmsDisposeBudgetFlowVo> flowVos = bmsDisposeBudgetFlowReadMapper.queryList(dto);
        BmsDisposeBudgetTableDto tableDto = new BmsDisposeBudgetTableDto();
        tableDto.setFlag(MedConst.ACTIVE_FLAG_1);
        List<BmsDisposeBudgetTableVo> tableVos = bmsDisposeBudgetTableReadMapper.queryList(tableDto);
        resultMap.put("flow", flowVos);
        resultMap.put("flowTable", tableVos);
        return resultMap;
    }

    @Override
    public List<BmsDisposeFlowDetailVo> queryFlowDetail(BmsDisposeBudgetFlowDto dto) {
        BmsDisposeFlowDetailDto detailDto = new BmsDisposeFlowDetailDto();
        detailDto.setBudgetFlowCode(dto.getBudgetFlowCode());
        return bmsDisposeFlowDetailReadMapper.queryFlowDetail(detailDto);
    }

}