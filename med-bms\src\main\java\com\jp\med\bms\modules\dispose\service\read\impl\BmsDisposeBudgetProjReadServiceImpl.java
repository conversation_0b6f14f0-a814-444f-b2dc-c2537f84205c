package com.jp.med.bms.modules.dispose.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetProjDto;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTypeDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetProjVo;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTypeVo;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetProjReadMapper;
import com.jp.med.bms.modules.dispose.service.read.BmsDisposeBudgetProjReadService;
import com.jp.med.bms.modules.dispose.service.read.BmsDisposeBudgetTypeReadService;
import com.jp.med.common.exception.AppException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Transactional(readOnly = true)
@Service
public class BmsDisposeBudgetProjReadServiceImpl extends ServiceImpl<BmsDisposeBudgetProjReadMapper, BmsDisposeBudgetProjDto> implements BmsDisposeBudgetProjReadService {
    @Autowired
    private BmsDisposeBudgetProjReadMapper bmsDisposeBudgetProjReadMapper;

    @Autowired
    private BmsDisposeBudgetTypeReadService bmsDisposeBudgetTypeReadService;

    /**
     * 查询预算编制项
     * @param dto
     * @return
     */
    @Override
    public List<BmsDisposeBudgetProjVo> queryList(BmsDisposeBudgetProjDto dto) {
        dto.setCentralizedDept(dto.getCurSysOrgId());
        return bmsDisposeBudgetProjReadMapper.queryList(dto);
    }

    /**
     * 查询归口科室列表
     * @param dto
     * @return
     */
    @Override
    public Map<String, List<?>> queryCentralizedDept(BmsDisposeBudgetProjDto dto) {
        BmsDisposeBudgetTypeDto disposeBudgetTypeDto = new BmsDisposeBudgetTypeDto();
        BeanUtils.copyProperties(dto, disposeBudgetTypeDto);
        List<BmsDisposeBudgetTypeVo> list = bmsDisposeBudgetTypeReadService.querySelectTree(disposeBudgetTypeDto);
        List<BmsOrgVo> bmsOrgEntities = bmsDisposeBudgetProjReadMapper.queryCentralizedDept(dto);
        List<BmsDisposeBudgetProjVo> projEntities = bmsDisposeBudgetProjReadMapper.queryProjTree(dto);
        Map<String, List<?>> map = new HashMap<>();
        map.put("typeList", list);
        map.put("dept", bmsOrgEntities);
        map.put("parent", projEntities);
        return map;
    }

    @Override
    public void checkBudgetProj(BmsDisposeBudgetProjDto dto) {
        List<BmsDisposeBudgetProjVo> projVos = bmsDisposeBudgetProjReadMapper.queryBudgetProj(dto);
        if (!projVos.isEmpty()){
            throw new AppException("编制项已存在");
        }
    }

    @Override
    public List<BmsDisposeBudgetProjVo> queryByYear(BmsDisposeBudgetProjDto dto) {
        return bmsDisposeBudgetProjReadMapper.queryByYear(dto);
    }
}
