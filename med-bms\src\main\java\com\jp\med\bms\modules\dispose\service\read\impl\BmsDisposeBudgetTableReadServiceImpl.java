package com.jp.med.bms.modules.dispose.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetProjDto;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTableDto;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetProjReadMapper;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetTableReadMapper;
import com.jp.med.bms.modules.dispose.service.read.BmsDisposeBudgetTableReadService;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetProjVo;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTableVo;
import com.jp.med.common.constant.MedConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Transactional(readOnly = true)
@Service
public class BmsDisposeBudgetTableReadServiceImpl extends ServiceImpl<BmsDisposeBudgetTableReadMapper, BmsDisposeBudgetTableDto> implements BmsDisposeBudgetTableReadService {

    @Autowired
    private BmsDisposeBudgetTableReadMapper bmsDisposeBudgetTableReadMapper;

    @Autowired
    private BmsDisposeBudgetProjReadMapper bmsDisposeBudgetProjReadMapper;

    @Override
    public List<BmsDisposeBudgetTableVo> queryList(BmsDisposeBudgetTableDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return bmsDisposeBudgetTableReadMapper.queryList(dto);
    }

    @Override
    public Map<String, Object> queryTableInit(BmsDisposeBudgetTableDto dto) {
        Map<String, Object> map = new HashMap<>();
        BmsDisposeBudgetProjDto budgetProjDto = new BmsDisposeBudgetProjDto();
        budgetProjDto.setFlag(MedConst.ACTIVE_FLAG_1);
        budgetProjDto.setBudgetYear(dto.getBudgetYear());
        List<BmsDisposeBudgetProjVo> projVos = bmsDisposeBudgetProjReadMapper.queryList(budgetProjDto);
        List<BmsDisposeBudgetProjVo> leafProj = bmsDisposeBudgetProjReadMapper.queryLeafProj(budgetProjDto);
        map.put("proj", projVos);
        map.put("leafProj", leafProj);
        return map;
    }

}
