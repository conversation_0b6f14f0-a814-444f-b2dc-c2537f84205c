package com.jp.med.bms.modules.dispose.service.read.impl;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetTypeReadMapper;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTypeDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTypeVo;
import com.jp.med.bms.modules.dispose.service.read.BmsDisposeBudgetTypeReadService;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class BmsDisposeBudgetTypeReadServiceImpl extends ServiceImpl<BmsDisposeBudgetTypeReadMapper, BmsDisposeBudgetTypeDto> implements BmsDisposeBudgetTypeReadService {
    @Autowired
    private BmsDisposeBudgetTypeReadMapper bmsDisposeBudgetTypeReadMapper;

    @Override
    public List<BmsDisposeBudgetTypeVo> queryList(BmsDisposeBudgetTypeDto dto) {
        return bmsDisposeBudgetTypeReadMapper.queryList(dto);
    }

    @Override
    public List<BmsDisposeBudgetTypeVo> querySelectTree(BmsDisposeBudgetTypeDto dto) {
        return bmsDisposeBudgetTypeReadMapper.querySelectTree(dto);
    }

    @Override
    public List<BmsDisposeBudgetTypeVo> queryByCode(BmsDisposeBudgetTypeDto dto) {
        if (StringUtils.isEmpty(dto.getBudgetTypeCode())){
            return new ArrayList<>();
        }
        return bmsDisposeBudgetTypeReadMapper.queryByCode(dto);
    }

}