package com.jp.med.bms.modules.dispose.service.read.impl;

import com.github.pagehelper.PageHelper;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.util.OSSUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeFlowModelReadMapper;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeFlowModelDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeFlowModelVo;
import com.jp.med.bms.modules.dispose.service.read.BmsDisposeFlowModelReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
@Slf4j
public class BmsDisposeFlowModelReadServiceImpl extends ServiceImpl<BmsDisposeFlowModelReadMapper, BmsDisposeFlowModelDto> implements BmsDisposeFlowModelReadService {

    @Autowired
    private BmsDisposeFlowModelReadMapper disposeFlowModelReadMapper;

    @Override
    public List<BmsDisposeFlowModelVo> queryList(BmsDisposeFlowModelDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<BmsDisposeFlowModelVo> modelVos = disposeFlowModelReadMapper.queryList(dto);
        try {
            for (BmsDisposeFlowModelVo flowModelVo : modelVos) {
                flowModelVo.setModelCaseUrl(OSSUtil.getPresignedObjectUrl(OSSConst.BUCKET_ACTIVITI,flowModelVo.getModelCase()));
            }
        } catch (Exception e) {
            log.error(e.toString());
        }
        return modelVos;
    }



}
