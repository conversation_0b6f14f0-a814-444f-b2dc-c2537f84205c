package com.jp.med.bms.modules.dispose.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetProjDto;
import org.springframework.web.multipart.MultipartFile;

/**
 * 预算编制项
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-18 11:42:16
 */
public interface BmsDisposeBudgetProjWriteService extends IService<BmsDisposeBudgetProjDto> {
    /**
     * 修改
     * @param dto
     */
    void updateBudgetProj(BmsDisposeBudgetProjDto dto);

    /**
     * 新增
     * @param dto
     */
    void saveBudgetProj(BmsDisposeBudgetProjDto dto);

    /**
     * 删除
     * @param dto
     */
    void deleteBudgetProj(BmsDisposeBudgetProjDto dto);

    /**
     * 上传编制项
     * @param dto
     * @param files
     */
    void upload(BmsDisposeBudgetProjDto dto, MultipartFile[] files);
}

