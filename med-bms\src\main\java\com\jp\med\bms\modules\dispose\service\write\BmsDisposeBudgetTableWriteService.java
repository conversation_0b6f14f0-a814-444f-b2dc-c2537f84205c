package com.jp.med.bms.modules.dispose.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTableDto;

/**
 * 预算编制表
 * <AUTHOR>
 * @email -
 * @date 2023-04-21 11:42:32
 */
public interface BmsDisposeBudgetTableWriteService extends IService<BmsDisposeBudgetTableDto> {
    /**
     * 新增预算编制表并加入明细
     * @param dto
     */
    void saveTableDetail(BmsDisposeBudgetTableDto dto);

    /**
     * 删除预算编制表
     * @param dto
     */
    void delete(BmsDisposeBudgetTableDto dto);

    /**
     * 复制预算编制表
     * @param dto
     */
    void copyBudgetTable(BmsDisposeBudgetTableDto dto);

    /**
     * 修改预算编制表项目明细
     * @param dto
     */
    void updateTableProj(BmsDisposeBudgetTableDto dto);
}

