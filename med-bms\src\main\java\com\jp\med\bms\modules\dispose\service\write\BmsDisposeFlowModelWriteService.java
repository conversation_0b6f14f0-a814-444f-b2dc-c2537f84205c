package com.jp.med.bms.modules.dispose.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeFlowModelDto;

/**
 * 流程模型
 * <AUTHOR>
 * @email -
 * @date 2023-05-25 19:07:55
 */
public interface BmsDisposeFlowModelWriteService extends IService<BmsDisposeFlowModelDto> {
    /**
     * 保存流程
     * @param dto
     */
    void saveModel(BmsDisposeFlowModelDto dto);

    /**
     * 删除流程
     * @param dto
     */
    void deleteById(BmsDisposeFlowModelDto dto);
}

