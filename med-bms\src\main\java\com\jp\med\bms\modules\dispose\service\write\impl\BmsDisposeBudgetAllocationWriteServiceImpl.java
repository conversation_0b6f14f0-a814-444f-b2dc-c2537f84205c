package com.jp.med.bms.modules.dispose.service.write.impl;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.TreeUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.bms.modules.dispose.mapper.write.BmsDisposeBudgetAllocationWriteMapper;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetAllocationDto;
import com.jp.med.bms.modules.dispose.service.write.BmsDisposeBudgetAllocationWriteService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 预算编制项目分配
 * <AUTHOR>
 * @email -
 * @date 2023-04-25 14:22:29
 */
@Service
@Transactional(readOnly = false)
public class BmsDisposeBudgetAllocationWriteServiceImpl extends ServiceImpl<BmsDisposeBudgetAllocationWriteMapper, BmsDisposeBudgetAllocationDto> implements BmsDisposeBudgetAllocationWriteService {

    @Resource
    private BmsDisposeBudgetAllocationWriteMapper bmsDisposeBudgetAllocationWriteMapper;

    @Override
    public void updateBudgetDept(BmsDisposeBudgetAllocationDto dto) {
        List<BmsDisposeBudgetAllocationDto> list = dto.getList();
        for (BmsDisposeBudgetAllocationDto allocationDto : list) {
            allocationDto.setBudgetCode(dto.getBudgetCode());
            allocationDto.setBudgetTableId(dto.getBudgetTableId());
        }
        bmsDisposeBudgetAllocationWriteMapper.deleteBudgetDept(dto);
        BatchUtil.batch(list, BmsDisposeBudgetAllocationWriteMapper.class);
    }


    private void setData(List<BmsDisposeBudgetAllocationDto> list, BmsDisposeBudgetAllocationDto dto,List<BmsDisposeBudgetAllocationDto> result){
        for (BmsDisposeBudgetAllocationDto child : list) {
            String[] deptList = child.getNewDeptList();
            if (!Objects.isNull(deptList)){
                for (String s : deptList) {
                    BmsDisposeBudgetAllocationDto allocationDto = new BmsDisposeBudgetAllocationDto();
                    allocationDto.setBudgetCode(dto.getBudgetCode());
                    allocationDto.setOrgId(s);
                    allocationDto.setExecuteDept(child.getOrgId());
                    allocationDto.setBudgetTableId(dto.getBudgetTableId());
                    result.add(allocationDto);
                }
            }
        }
    }
}
