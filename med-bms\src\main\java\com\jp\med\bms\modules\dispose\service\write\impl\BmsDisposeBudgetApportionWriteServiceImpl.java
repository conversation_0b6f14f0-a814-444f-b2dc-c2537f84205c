package com.jp.med.bms.modules.dispose.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetApportionDto;
import com.jp.med.bms.modules.dispose.mapper.write.BmsDisposeBudgetApportionWriteMapper;
import com.jp.med.bms.modules.dispose.service.write.BmsDisposeBudgetApportionWriteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Transactional(readOnly = false)
@Service
public class BmsDisposeBudgetApportionWriteServiceImpl extends ServiceImpl<BmsDisposeBudgetApportionWriteMapper, BmsDisposeBudgetApportionDto> implements BmsDisposeBudgetApportionWriteService {
}
