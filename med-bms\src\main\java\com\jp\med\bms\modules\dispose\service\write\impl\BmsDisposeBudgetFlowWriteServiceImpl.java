package com.jp.med.bms.modules.dispose.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsExecuteBudgetDataWriteService;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsExecuteBudgetFillingWriteService;
import com.jp.med.bms.modules.bmsOrg.mapper.read.BmsOrgReadMapper;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetFlowDto;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeFlowDetailDto;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetAllocationReadMapper;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeFlowDetailReadMapper;
import com.jp.med.bms.modules.dispose.mapper.write.BmsDisposeBudgetFlowWriteMapper;
import com.jp.med.bms.modules.dispose.mapper.write.BmsDisposeFlowDetailWriteMapper;
import com.jp.med.bms.modules.dispose.service.write.BmsDisposeBudgetFlowWriteService;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.message.SysMessageDto;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.SysMessageFeignService;
import com.jp.med.common.util.BatchUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Transactional(readOnly = false)
@Service
@Slf4j
public class BmsDisposeBudgetFlowWriteServiceImpl extends ServiceImpl<BmsDisposeBudgetFlowWriteMapper, BmsDisposeBudgetFlowDto> implements BmsDisposeBudgetFlowWriteService {
    @Autowired
    private BmsDisposeBudgetFlowWriteMapper bmsDisposeBudgetFlowWriteMapper;

    @Autowired
    private BmsDisposeFlowDetailWriteMapper bmsDisposeFlowDetailWriteMapper;

    @Autowired
    private BmsDisposeBudgetAllocationReadMapper bmsDisposeBudgetAllocationReadMapper;

    @Autowired
    private BmsExecuteBudgetDataWriteService bmsExecuteBudgetDataWriteService;

    @Autowired
    private BmsDisposeFlowDetailReadMapper bmsDisposeFlowDetailReadMapper;

    @Autowired
    public SysMessageFeignService sysMessageFeignService;

    @Autowired
    private BmsExecuteBudgetFillingWriteService bmsExecuteBudgetFillingWriteService;

    @Autowired
    private BmsOrgReadMapper bmsOrgReadMapper;

    @Override
    public void updateBudgetFlow(BmsDisposeBudgetFlowDto dto) {
        bmsDisposeBudgetFlowWriteMapper.updateBudgetFlow(dto);
    }

    /**
     * 启动流程
     *
     * @param dto
     */
    public void updateDetail(BmsDisposeBudgetFlowDto dto) {
//        BmsDisposeFlowDetailDto detailDto = new BmsDisposeFlowDetailDto();
//        detailDto.setBudgetFlowId(dto.getBudgetFlowId());
//        //修改节点状态
//        bmsDisposeFlowDetailWriteMapper.updateByFlow(detailDto);
//        //查询流程详情
//        BmsDisposeFlowDetailDto flowDetailDto = new BmsDisposeFlowDetailDto();
//        flowDetailDto.setBudgetFlowId(dto.getBudgetFlowId());
//        flowDetailDto.setStatus("0");
//        flowDetailDto.setOrganizationOrder(1);
//        List<BmsDisposeFlowDetailVo> detailVos = bmsDisposeFlowDetailReadMapper.queryFirstOrder(flowDetailDto);
//        // 将流程第一个节点的状态改为开始执行
//        detailDto.setFlowDetailId(detailVos.get(0).getFlowDetailId());
//        detailDto.setStatus(MedConst.FLOW_STATUS_1);
//        bmsDisposeFlowDetailWriteMapper.updateFlowDetailStatus(detailDto);
//        //添加数据填报状态
//        BmsExecuteBudgetFillingDto fillingDto = new BmsExecuteBudgetFillingDto();
//        fillingDto.setFlowDetailId(detailVos.get(0).getFlowDetailId());
//        fillingDto.setOrgId(detailVos.get(0).getOrgId());
//        fillingDto.setBudgetFlowId(dto.getBudgetFlowId());
//        fillingDto.setHospitalId(dto.getHospitalId());
//        bmsExecuteBudgetFillingWriteService.saveBudgetFilling(fillingDto, true);
//        //调用服务，向用户发送信息
//        List<EmpEmployeeInfoEntity> entities = bmsOrgReadMapper.queryOrgUser(new BmsOrgDto());
//        if (!Objects.isNull(entities)) {
//            SysMessageDto messageDto = new SysMessageDto();
//            List<String> userList = new ArrayList<>();
//            entities.forEach(item -> userList.add(item.getEmpCode()));
//            BeanUtils.copyProperties(dto, messageDto);
//            messageDto.setUsers(userList.toArray(new String[]{}));
//            messageDto.setTitle("系统通知");
//            messageDto.setPushText("预算任务`" + dto.getBudgetFlowName() + "`已经开始,请在系统内查看");
//            sendMessage(messageDto);
//        }
    }

    @Override
    public void saveBudgetFlow(BmsDisposeBudgetFlowDto dto) {
        List<BmsDisposeFlowDetailDto> detail = dto.getDetail();
        if (!Objects.isNull(dto.getBudgetFlowId())) {
            bmsDisposeBudgetFlowWriteMapper.updateBudgetFlow(dto);
        } else {
            dto.setFlag(MedConst.ACTIVE_FLAG_1);
            int insert = bmsDisposeBudgetFlowWriteMapper.insert(dto);
            if (insert != 1) {
                throw new AppException("写入失败");
            }
        }
        for (int i = 0; i < detail.size(); i++) {
            detail.get(i).setBudgetFlowCode(dto.getBudgetFlowCode());
            detail.get(i).setOrganizationOrder(i + 1);
        }
        BmsDisposeFlowDetailDto bmsDto = new BmsDisposeFlowDetailDto();
        bmsDto.setBudgetFlowCode(dto.getBudgetFlowCode());
        bmsDisposeFlowDetailWriteMapper.deleteByFlow(bmsDto);
        BatchUtil.batch(detail, BmsDisposeFlowDetailWriteMapper.class);
    }

    @Override
    public void deleteBudgetFlow(BmsDisposeBudgetFlowDto dto) {
//        BmsDisposeFlowDetailDto bmsDto = new BmsDisposeFlowDetailDto();
//        bmsDto.setBudgetFlowId(dto.getBudgetFlowId());
//        bmsDisposeFlowDetailWriteMapper.deleteByFlow(bmsDto);
//        bmsDisposeBudgetFlowWriteMapper.deleteById(dto);
    }

    /**
     * 流程启动
     *
     * @param dto
     */
    @Override
    public void initiateProcess(BmsDisposeBudgetFlowDto dto) {
        dto.setFlag(MedConst.ACTIVE_FLAG_1);
        updateBudgetFlow(dto);
        updateDetail(dto);
    }


    /**
     * 远程调用，向用户发送信息
     *
     * @param dto
     */
    public void sendMessage(SysMessageDto dto) {
        try {
            sysMessageFeignService.sendMessage(dto);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("消息发送失败");
        }
    }
}
