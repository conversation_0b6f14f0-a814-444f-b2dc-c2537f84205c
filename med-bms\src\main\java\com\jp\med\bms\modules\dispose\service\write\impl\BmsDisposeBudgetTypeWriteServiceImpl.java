package com.jp.med.bms.modules.dispose.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTypeDto;
import com.jp.med.bms.modules.dispose.mapper.write.BmsDisposeBudgetTypeWriteMapper;
import com.jp.med.bms.modules.dispose.service.write.BmsDisposeBudgetTypeWriteService;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.EasyPoiUtil;
import com.jp.med.common.util.TreeUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Transactional(readOnly = false)
@Service
public class BmsDisposeBudgetTypeWriteServiceImpl extends ServiceImpl<BmsDisposeBudgetTypeWriteMapper, BmsDisposeBudgetTypeDto> implements BmsDisposeBudgetTypeWriteService {
    @Autowired
    private BmsDisposeBudgetTypeWriteMapper disposeBmsBudgetTypeWriteMapper;

    @Override
    public void updateBmsBudgetType(BmsDisposeBudgetTypeDto dto) {
        disposeBmsBudgetTypeWriteMapper.updateBmsBudgetType(dto);
    }

    @Override
    public void saveBmsBudgetType(BmsDisposeBudgetTypeDto dto) {
        disposeBmsBudgetTypeWriteMapper.saveBmsBudgetType(dto);
    }

    /**
     * 删除该节点下所有信息
     *
     * @param dto
     */
    @Override
    public void deleteBmsBudgetType(BmsDisposeBudgetTypeDto dto) {
        int count = disposeBmsBudgetTypeWriteMapper.deleteBmsBudgetType(dto);
        if (count < 1) {
            throw new AppException("删除失败");
        }
    }

    @Override
    public void uploadFile(BmsDisposeBudgetTypeDto dto, MultipartFile[] files) {
        try {
            List<BmsDisposeBudgetTypeDto> typeDtos = EasyPoiUtil.importExcel(files[0], BmsDisposeBudgetTypeDto.class);
            TreeUtil.checkRing(typeDtos, "budgetTypeCode", "budgetTypeParentId");
            BatchUtil.batch(typeDtos, BmsDisposeBudgetTypeWriteMapper.class);
        }catch (IOException e){
            throw new AppException("文件解析失败");
        } catch (InvocationTargetException | NoSuchMethodException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }
}
