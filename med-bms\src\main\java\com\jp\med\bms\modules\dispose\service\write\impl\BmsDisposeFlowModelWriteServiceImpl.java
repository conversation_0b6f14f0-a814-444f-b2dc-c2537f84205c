package com.jp.med.bms.modules.dispose.service.write.impl;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.OSSUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.bms.modules.dispose.mapper.write.BmsDisposeFlowModelWriteMapper;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeFlowModelDto;
import com.jp.med.bms.modules.dispose.service.write.BmsDisposeFlowModelWriteService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.Objects;

/**
 * 流程模型
 * <AUTHOR>
 * @email -
 * @date 2023-05-25 19:07:55
 */
@Service
@Transactional(readOnly = false)
public class BmsDisposeFlowModelWriteServiceImpl extends ServiceImpl<BmsDisposeFlowModelWriteMapper, BmsDisposeFlowModelDto> implements BmsDisposeFlowModelWriteService {

    @Autowired
    private BmsDisposeFlowModelWriteMapper bmsDisposeFlowModelWriteMapper;

    @Override
    public void saveModel(BmsDisposeFlowModelDto dto) {
        MultipartFile file = dto.getFile();
        OSSUtil.removeFile(OSSConst.BUCKET_ACTIVITI, StringUtils.isEmpty(dto.getModelCase()) ? "-" : dto.getModelCase());
        String uploadFile = OSSUtil.uploadFile(OSSConst.BUCKET_ACTIVITI, "bms/", file);
        dto.setModelCase(uploadFile);
        if(Objects.isNull(dto.getFlowModelId())){
            bmsDisposeFlowModelWriteMapper.insert(dto);
        }else {
            bmsDisposeFlowModelWriteMapper.updateById(dto);
        }

    }

    @Override
    public void deleteById(BmsDisposeFlowModelDto dto) {
        OSSUtil.removeFile(OSSConst.BUCKET_ACTIVITI, dto.getModelCase());
        int count = bmsDisposeFlowModelWriteMapper.deleteById(dto);
        if (count != 1){
            throw new AppException("删除失败");
        }
    }
}
