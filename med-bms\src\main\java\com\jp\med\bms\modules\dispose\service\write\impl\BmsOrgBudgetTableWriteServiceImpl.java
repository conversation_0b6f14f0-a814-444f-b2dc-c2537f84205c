package com.jp.med.bms.modules.dispose.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetAllocationDto;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetProjDto;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTableDto;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTableProjDto;
import com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetProjReadMapper;
import com.jp.med.bms.modules.dispose.mapper.write.BmsDisposeBudgetAllocationWriteMapper;
import com.jp.med.bms.modules.dispose.mapper.write.BmsDisposeBudgetTableProjWriteMapper;
import com.jp.med.bms.modules.dispose.mapper.write.BmsDisposeBudgetTableWriteMapper;
import com.jp.med.bms.modules.dispose.service.write.BmsDisposeBudgetTableWriteService;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetProjVo;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Transactional(readOnly = false)
@Service
public class BmsOrgBudgetTableWriteServiceImpl extends ServiceImpl<BmsDisposeBudgetTableWriteMapper, BmsDisposeBudgetTableDto> implements BmsDisposeBudgetTableWriteService {

    @Autowired
    private BmsDisposeBudgetTableWriteMapper bmsDisposeBudgetTableWriteMapper;
    @Autowired
    private BmsDisposeBudgetProjReadMapper bmsDisposeBudgetProjReadMapper;
    @Autowired
    private BmsDisposeBudgetAllocationWriteMapper bmsDisposeBudgetAllocationWriteMapper;
    @Autowired
    private BmsDisposeBudgetTableProjWriteMapper bmsDisposeBudgetTableProjWriteMapper;

    /**
     * 新增预算编制表并加入明细
     * @param dto
     */
    @Override
    public void saveTableDetail(BmsDisposeBudgetTableDto dto) {
        dto.setFlag("1");
        bmsDisposeBudgetTableWriteMapper.insert(dto);
        List<BmsDisposeBudgetAllocationDto> list = new ArrayList<>();
        List<BmsDisposeBudgetTableProjDto> projList = new ArrayList<>();
        BmsDisposeBudgetProjDto projDto = new BmsDisposeBudgetProjDto();
        projDto.setFlag(MedConst.ACTIVE_FLAG_1);
        projDto.setBudgetYear(dto.getBudgetYear());
        List<BmsDisposeBudgetProjVo> projVos = bmsDisposeBudgetProjReadMapper.queryProjTree(projDto);
        List<String> proj = dto.getProj();
        List<String> allProjKey = dto.getAllProjKey();
        for (BmsDisposeBudgetProjVo bmsDisposeBudgetProjVo : projVos) {
            if (proj.contains(bmsDisposeBudgetProjVo.getBudgetCode())) {
                BmsDisposeBudgetAllocationDto data = new BmsDisposeBudgetAllocationDto();
                data.setBudgetCode(bmsDisposeBudgetProjVo.getBudgetCode());
                data.setBudgetTableId(dto.getId());
                //默认是归口科室进行编制
                data.setOrgId(bmsDisposeBudgetProjVo.getCentralizedDept());
                data.setExecuteDept(bmsDisposeBudgetProjVo.getCentralizedDept());
                list.add(data);
            }
            // 设置当前使用的编制项目(包括其父级项目)
            if (allProjKey.contains(bmsDisposeBudgetProjVo.getBudgetCode())){
                BmsDisposeBudgetTableProjDto tableProjDto = new BmsDisposeBudgetTableProjDto();
                BeanUtils.copyProperties(bmsDisposeBudgetProjVo, tableProjDto);
                tableProjDto.setBudgetTableId(dto.getId());
                projList.add(tableProjDto);
            }
        }
        BatchUtil.batch(projList, BmsDisposeBudgetTableProjWriteMapper.class);
        BatchUtil.batch(list, BmsDisposeBudgetAllocationWriteMapper.class);
    }

    @Override
    public void delete(BmsDisposeBudgetTableDto dto) {
        int count = bmsDisposeBudgetTableWriteMapper.deleteById(dto);
        if (count != 1){
            throw new AppException("删除失败");
        }
        BmsDisposeBudgetAllocationDto allocationDto = new BmsDisposeBudgetAllocationDto();
        allocationDto.setBudgetTableId(dto.getId());
        int i = bmsDisposeBudgetAllocationWriteMapper.deleteByTableId(allocationDto);
        if (i < 1){
            throw new AppException("删除失败");
        }
    }

    @Override
    public void copyBudgetTable(BmsDisposeBudgetTableDto dto) {
        BmsDisposeBudgetTableDto budgetTableDto = new BmsDisposeBudgetTableDto();
        BeanUtils.copyProperties(dto, budgetTableDto);
        budgetTableDto.setId(null);
        budgetTableDto.setFlag(MedConst.ACTIVE_FLAG_1);
        bmsDisposeBudgetTableWriteMapper.insert(budgetTableDto);
        BmsDisposeBudgetTableProjDto tableProjDto = new BmsDisposeBudgetTableProjDto();
        tableProjDto.setBudgetTableId(dto.getId());
        tableProjDto.setBudgetTableIdNew(budgetTableDto.getId());
        bmsDisposeBudgetTableProjWriteMapper.insertTableProjBySelect(tableProjDto);
        BmsDisposeBudgetAllocationDto budgetAllocationDto = new BmsDisposeBudgetAllocationDto();
        budgetAllocationDto.setBudgetTableId(dto.getId());
        budgetAllocationDto.setBudgetTableIdNew(budgetTableDto.getId());
        bmsDisposeBudgetAllocationWriteMapper.insertBySelect(budgetAllocationDto);
    }

    @Override
    public void updateTableProj(BmsDisposeBudgetTableDto dto) {
        List<BmsDisposeBudgetTableProjDto> dtoList = dto.getTableProjDtoList();
        BatchUtil.batch("updateTableProj",dtoList, BmsDisposeBudgetTableProjWriteMapper.class);
    }
}
