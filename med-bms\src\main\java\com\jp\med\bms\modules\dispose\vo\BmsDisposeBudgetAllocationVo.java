package com.jp.med.bms.modules.dispose.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * 预算编制项目分配
 * <AUTHOR>
 * @email -
 * @date 2023-04-25 14:22:29
 */
@Data
public class BmsDisposeBudgetAllocationVo {
	
	/** 预算编制项目分配ID */
	private Integer budgetAllocationId;

	/** 预算科室 */
	private String orgId;

	/** 预算科室名称 */
	private String orgName;

	/** 预算编制表 */
	private Integer budgetTableId;

	/** 预算编制表名称 */
	private String budgetTableName;

	/** 预算编制流程编码 */
	private String flowDetailCode;

	/** 预算编制项名称 */
	private String budgetName;

	/** 编制科室 */
	private String budgetCode;

	/** 归口科室名称 */
	private String centralizedDept;

	/** 预算科室数量 */
	private Integer deptCount;

	/** 全部预算科室 */
	private String depts;

	/** 预算编制编码 */
	private String executeDept;

	/** 上级预算编制编码 */
	private String budgetParentId;

	/** 表格唯一ID */
	private String key;

	/** 预算科室列表 */
	private String[] deptList;

	/** 是否叶子节点 */
	private String isLeaf;

	/** 状态 */
	private String flag;

	/** 总数 */
	private Integer total;

	/** 计算公式 */
	private String formula;

	/** 计算公式说明 */
	private String formulaLabel;

	/** 是否计算 */
	private String cal;

	private List<BmsDisposeBudgetAllocationVo> children;

}
