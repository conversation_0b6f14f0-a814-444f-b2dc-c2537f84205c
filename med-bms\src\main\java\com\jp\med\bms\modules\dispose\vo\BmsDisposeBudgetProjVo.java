package com.jp.med.bms.modules.dispose.vo;

import lombok.Data;

import java.util.List;

/**
 * 预算编制项
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-10 15:11:57
 */
@Data
public class BmsDisposeBudgetProjVo {

    /**
     * 预算编制项类别ID
     */
    private Integer budgetProjId;
    /**
     * 预算编制项类别编码
     */
    private String budgetCode;
    /**
     * 主要内容
     */
    private String cont;
    /**
     * 说明
     */
    private String dscr;
    /**
     * 预算编制项类别名称
     */
    private String budgetName;
    /**
     * 上级编码
     */
    private String budgetParentId;
    /**
     * 预算编制项类别
     */
    private String budgetTypeCode;
    /**
     * 预算编制项类别（名称）
     */
    private String budgetTypeName;
    /**
     * 归口科室
     */
    private String centralizedDept;
    /**
     * 归口科室
     */
    private String orgId;
    /**
     * 启用状态
     */
    private String flag;
    /**
     * 计量单位
     **/
    private String unit;
    /**
     * 归口科室名称
     */
    private String deptName;
    /**
     * 上级名称
     */
    private String parentBudgetName;
    /**
     * 组织名称
     */
    private String orgName;
    /**
     * 是否可选
     */
    private boolean disabled;
    /**
     * 键
     */
    private String key;
    /**
     * 唯一ID
     */
    private String id;
    private String title;
    /**
     * 子集
     */
    List<BmsDisposeBudgetProjVo> children;
    private String value;
    private String label;
    /**
     * 编制年度
     */
    private String budgetYear;
}
