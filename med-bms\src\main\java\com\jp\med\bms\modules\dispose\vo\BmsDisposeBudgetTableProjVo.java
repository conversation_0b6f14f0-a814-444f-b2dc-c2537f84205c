package com.jp.med.bms.modules.dispose.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 预算对应编制项目
 * <AUTHOR>
 * @email -
 * @date 2023-05-31 11:35:42
 */
@Data
public class BmsDisposeBudgetTableProjVo {
	
	/** 预算编制项编码 */
	private String budgetCode;

	/** 预算编制项名称 */
	private String budgetName;

	/** 上级编制项编码 */
	private String budgetParentId;

	/** 预算编制类别 */
	private Long budgetTypeId;

	/** 归口科室 */
	private String centralizedDept;

	/** 计量单位 */
	private String unit;

	/** 预算编制表ID */
	private Integer budgetTableId;

}
