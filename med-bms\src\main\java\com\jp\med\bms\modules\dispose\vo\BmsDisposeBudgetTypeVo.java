package com.jp.med.bms.modules.dispose.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 预算编制项类别
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-04-10 15:11:57
 */
@Data
public class BmsDisposeBudgetTypeVo {

	/** 预算编制项类别ID */
	private Integer budgetTypeId;

	/** 预算编制项类别编码 */
	private String budgetTypeCode;

	/** 预算编制项类别名称 */
	private String budgetTypeName;

	/** 上级编码 */
	private String budgetTypeParentId;

	/** 启用状态 */
	private String flag;

	/**上级编制项名称*/
	private String budgetTypeParentName;

	/**按钮是否禁用*/
	private Boolean disabled;

}
