package com.jp.med.bms.modules.dispose.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 预算编制流程详情
 * <AUTHOR>
 * @email -
 * @date 2023-04-26 16:44:07
 */
@Data
public class BmsDisposeFlowDetailVo {
	
	/** 预算编制流程详情ID */
	private Integer flowDetailId;

	/** 编制节点名称 */
	private String flowDetailCode;

	/** 编制节点名称 */
	private String flowDetailName;

	/** 流程顺序 */
	private Integer organizationOrder;

	/** 执行者 */
	private String ptr;

	/** 执行标准 */
	private String exestd;

	/** 概要说明 */
	private String abst;

	/** 执行状态(0:未开始,1:执行中2:执行结束) */
	private String status;

	/** 是否按照经济科目汇总 */
	private String econSub;

	/** 预算填报状态ID */
	private Integer budgetFillingId;

}
