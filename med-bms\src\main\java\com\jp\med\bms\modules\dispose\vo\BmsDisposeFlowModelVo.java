package com.jp.med.bms.modules.dispose.vo;


import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * 流程模型
 * <AUTHOR>
 * @email -
 * @date 2023-05-25 19:07:55
 */
@Data
public class BmsDisposeFlowModelVo {
	
	/** 流程模型ID */
	private Integer flowModelId;

	/** 流程模板名称 */
	private String flowModeCode;

	/** 流程模板名称 */
	private String flowModeName;

	/** 创建人 */
	private String username;

	/** 创建时间 */
	private Date createTime;

	/** 流程图例 */
	private String modelCase;

	/** 启用状态 */
	private String flag;

	/** 流程图例存放位置 */
	private String modelCaseUrl;

	/** 医疗机构编码 */
	private String hospitalId;

}
