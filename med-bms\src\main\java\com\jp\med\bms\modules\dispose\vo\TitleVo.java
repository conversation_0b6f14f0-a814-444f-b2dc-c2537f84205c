package com.jp.med.bms.modules.dispose.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jp.med.common.interceptors.BaseTree;
import lombok.Getter;
import lombok.Setter;

import java.util.*;

/**
 * @BelongsProject: mednback
 * @BelongsPackage: com.jp.med.bms.modules.dispose.vo
 * @Author: artist
 * @CreateTime: 2023-05-06
 * @Description: 前端表头类
 * @Version: 1.0
 */
@Getter
@Setter
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class TitleVo implements BaseTree<String, TitleVo> {

    private String key;
    private String header;
    private String title;
    private String parentId;
    /**
     * 固定列
     */
    private String fixed;
    private Boolean ellipsis = false;
    private Boolean resizable = true;
    /**
     * 是否合计
     */
    private Boolean summary;
    /**
     * 排序
     */
    private String sorter;
    private Integer order;
    /**
     * 宽度
     */
    private String width;

    /**
     * 列解释
     */
    private String titleExplain;
    private List<TitleVo> children = null;

    @Override
    public String getCode() {
        return this.key;
    }

    @Override
    public void setCode(String code) {
        this.key = code;
    }

    @Override
    public String getPid() {
        return this.parentId;
    }

    @Override
    public void setPid(String pid) {
        this.parentId = pid;
    }

    @Override
    public void addChild(TitleVo node) {
        if (Objects.isNull(this.getChildren())) {
            this.children = new ArrayList<>();
        }
        if (!this.children.isEmpty()) {
            int index = Collections.binarySearch(this.children, node, Comparator.comparing(TitleVo::getOrder));
            if (index < 0) {
                index = -(index + 1);
            }
            // 在找到的位置插入新对象
            this.children.add(index, node);
        } else {
            this.children.add(node);
        }
    }
}
