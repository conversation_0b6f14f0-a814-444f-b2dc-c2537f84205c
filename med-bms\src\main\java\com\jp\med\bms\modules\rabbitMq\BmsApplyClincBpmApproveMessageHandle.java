package com.jp.med.bms.modules.rabbitMq;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetAdjustDto;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetApplyDto;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetResultsDto;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsExecuteBudgetAdjustWriteMapper;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsExecuteBudgetApplyWriteMapper;
import com.jp.med.bms.modules.bmsExecute.mapper.write.BmsExecuteBudgetResultsWriteMapper;
import com.jp.med.common.dto.bpm.BpmProcessInstanceStatus;
import com.jp.med.common.messsage.AbstractBpmApproveMessageHandle;
import com.rabbitmq.client.Channel;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
@Getter
@Setter
public class BmsApplyClincBpmApproveMessageHandle extends AbstractBpmApproveMessageHandle {


    @Autowired
    private BmsExecuteBudgetApplyWriteMapper bmsExecuteBudgetApplyWriteMapper;

    @Autowired
    private BmsExecuteBudgetAdjustWriteMapper bmsExecuteBudgetAdjustWriteMapper;
    @Autowired
    private BmsExecuteBudgetResultsWriteMapper bmsExecuteBudgetResultsWriteMapper;

    public String[] processIdentifier = {"BMS_BUDGET_APPLY"};

    @Override
    @RabbitListener(queues = {"BMS_BUDGET_APPLY"})
    public void onMessage(BpmProcessInstanceStatus msg, Message message, Channel channel) throws Exception {
        receiveMessage0(msg);
    }

    /**
     * 处理创建的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleCreate(BpmProcessInstanceStatus message) {

    }

    /**
     * 处理审批通过的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleApproved(BpmProcessInstanceStatus message) {

        String businessKey = message.getBusinessKey();
        //更新当前状态为已通过
        LambdaUpdateWrapper<BmsExecuteBudgetApplyDto> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(BmsExecuteBudgetApplyDto::getStatus, "2")
                .eq(BmsExecuteBudgetApplyDto::getBudgetApplyId,Long.valueOf(businessKey));
        updateBudget(businessKey);
        bmsExecuteBudgetApplyWriteMapper.update(null,updateWrapper);
    }

    private void updateBudget(String businessKey) {
        List<BmsExecuteBudgetAdjustDto> bmsExecuteBudgetAdjustDtos = bmsExecuteBudgetAdjustWriteMapper.selectList(
                Wrappers.lambdaQuery(BmsExecuteBudgetAdjustDto.class)
                        .eq(BmsExecuteBudgetAdjustDto::getBudgetApplyId, Long.valueOf(businessKey))
        );
        bmsExecuteBudgetAdjustDtos.forEach(item->{
            BmsExecuteBudgetResultsDto bmsExecuteBudgetResultsDto = bmsExecuteBudgetResultsWriteMapper.selectOne(
                    Wrappers.lambdaQuery(BmsExecuteBudgetResultsDto.class)
                            .eq(BmsExecuteBudgetResultsDto::getBudgetResultsId, item.getBudgetResultsId())
            );
            if (ObjUtil.isNotNull(bmsExecuteBudgetResultsDto)){
                bmsExecuteBudgetResultsDto.setBudgetAmount(item.getBudgetAmount());
                bmsExecuteBudgetResultsWriteMapper.updateById(bmsExecuteBudgetResultsDto);
            }
        });

    }

    /**
     * 处理审批不通过的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleRejected(BpmProcessInstanceStatus message) {
        String businessKey = message.getBusinessKey();
        //更新当前状态为拒绝
        LambdaUpdateWrapper<BmsExecuteBudgetApplyDto> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(BmsExecuteBudgetApplyDto::getStatus,"3")
                .eq(BmsExecuteBudgetApplyDto::getBudgetApplyId,Long.valueOf(businessKey));
        bmsExecuteBudgetApplyWriteMapper.update(null,updateWrapper);
    }

    /**
     * 处理审批中的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleRunning(BpmProcessInstanceStatus message) {

    }

    /**
     * 处理已取消的逻辑。
     *
     * @param message 包含 BPM 审批实例状态的消息对象
     */
    @Override
    protected void handleCancelled(BpmProcessInstanceStatus message) {

    }

}
