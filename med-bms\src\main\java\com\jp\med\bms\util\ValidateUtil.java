package com.jp.med.bms.util;


import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.Map;

public class ValidateUtil {
    public static final String module = ValidateUtil.class.getName();
    public static final boolean defaultEmptyOK = true;
    public static final String digits = "0123456789";
    public static final String lowercaseLetters = "abcdefghijklmnopqrstuvwxyz";
    public static final String uppercaseLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    public static final String letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    public static final String whitespace = " \t\n\r";
    public static final String decimalPointDelimiter = ".";
    public static final String phoneNumberDelimiters = "()- ";
    public static final String validUSPhoneChars = "0123456789()- ";
    public static final String validWorldPhoneChars = "0123456789()- +";
    public static final String SSNDelimiters = "- ";
    public static final String validSSNChars = "0123456789- ";
    public static final int digitsInSocialSecurityNumber = 9;
    public static final int digitsInUSPhoneNumber = 10;
    public static final int digitsInUSPhoneAreaCode = 3;
    public static final int digitsInUSPhoneMainNumber = 7;
    public static final String ZipCodeDelimiters = "-";
    public static final String ZipCodeDelimeter = "-";
    public static final String validZipCodeChars = "0123456789-";
    public static final int digitsInZipCode1 = 5;
    public static final int digitsInZipCode2 = 9;
    public static final String creditCardDelimiters = " -";
    private static final int[] daysInMonth = new int[]{31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    public static final String USStateCodeDelimiter = "|";
    public static final String USStateCodes = "AL|AK|AS|AZ|AR|CA|CO|CT|DE|DC|FM|FL|GA|GU|HI|ID|IL|IN|IA|KS|KY|LA|ME|MH|MD|MA|MI|MN|MS|MO|MT|NE|NV|NH|NJ|NM|NY|NC|ND|MP|OH|OK|OR|PW|PA|PR|RI|SC|SD|TN|TX|UT|VT|VI|VA|WA|WV|WI|WY|AE|AA|AE|AE|AP";
    public static final String ContiguousUSStateCodes = "AL|AZ|AR|CA|CO|CT|DE|DC|FL|GA|ID|IL|IN|IA|KS|KY|LA|ME|MD|MA|MI|MN|MS|MO|MT|NE|NV|NH|NJ|NM|NY|NC|ND|OH|OK|OR|PA|RI|SC|SD|TN|TX|UT|VT|VA|WA|WV|WI|WY";

    public static boolean areEqual(Object obj, Object obj2) {
        if (obj == null) {
            return obj2 == null;
        } else {
            return obj.equals(obj2);
        }
    }

    public static boolean areEqualIgnoreCase(String obj, String obj2) {
        if (obj == null) {
            return obj2 == null;
        } else {
            return obj.equalsIgnoreCase(obj2);
        }
    }

    public static boolean isEmpty(Object value) {
        if (value == null) {
            return defaultEmptyOK;
        } else {
            if (value instanceof String) {
                return ((String) value).length() == 0;
            } else if (value instanceof Collection) {
                return ((Collection) value).isEmpty();
            } else if (value instanceof Map) {
                return ((Map) value).size() == 0;
            } else return value instanceof String[] && ((String[]) ((String[]) value)).length == 0;

        }
    }

    public static boolean isEmpty(String s) {
        return s == null || s.length() == 0;
    }

    public static boolean isEmpty(Collection c) {
        return c == null || c.isEmpty();
    }

    public static boolean isNotEmpty(String s) {
        return s != null && s.length() > 0;
    }

    public static boolean isNotEmpty(Collection c) {
        return c != null && !c.isEmpty();
    }

    public static boolean isString(Object obj) {
        return obj instanceof String;
    }

    public static boolean isWhitespace(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            for(int i = 0; i < s.length(); ++i) {
                char c = s.charAt(i);
                if (whitespace.indexOf(c) == -1) {
                    return false;
                }
            }

            return true;
        }
    }

    public static String stripCharsInBag(String s, String bag) {
        StringBuilder returnString = new StringBuilder();

        for(int i = 0; i < s.length(); ++i) {
            char c = s.charAt(i);
            if (bag.indexOf(c) == -1) {
                returnString.append(c);
            }
        }

        return returnString.toString();
    }

    public static String stripCharsNotInBag(String s, String bag) {
        StringBuilder returnString = new StringBuilder("");

        for(int i = 0; i < s.length(); ++i) {
            char c = s.charAt(i);
            if (bag.indexOf(c) != -1) {
                returnString.append(c);
            }
        }

        return returnString.toString();
    }

    public static String stripWhitespace(String s) {
        return stripCharsInBag(s, whitespace);
    }

    public static boolean charInString(char c, String s) {
        return s.indexOf(c) != -1;
    }

    public static boolean isLetter(char c) {
        return Character.isLetter(c);
    }

    public static boolean isLetterOrDigit(char c) {
        return Character.isLetterOrDigit(c);
    }

    public static boolean isInteger(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            for(int i = 0; i < s.length(); ++i) {
                char c = s.charAt(i);
                if (!Character.isDigit(c)) {
                    return false;
                }
            }

            return true;
        }
    }

    public static boolean isSignedInteger(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            try {
                Integer.parseInt(s);
                return true;
            } catch (Exception var2) {
                return false;
            }
        }
    }

    public static boolean isSignedLong(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            try {
                Long.parseLong(s);
                return true;
            } catch (Exception var2) {
                return false;
            }
        }
    }

    public static boolean isPositiveInteger(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            try {
                long temp = Long.parseLong(s);
                return temp > 0L;
            } catch (Exception var3) {
                return false;
            }
        }
    }

    public static boolean isNonnegativeInteger(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            try {
                int temp = Integer.parseInt(s);
                return temp >= 0;
            } catch (Exception var2) {
                return false;
            }
        }
    }

    public static boolean isNegativeInteger(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            try {
                int temp = Integer.parseInt(s);
                return temp < 0;
            } catch (Exception var2) {
                return false;
            }
        }
    }

    public static boolean isNonpositiveInteger(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            try {
                int temp = Integer.parseInt(s);
                return temp <= 0;
            } catch (Exception var2) {
                return false;
            }
        }
    }

    public static boolean isFloat(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            boolean seenDecimalPoint = false;
            if (s.startsWith(decimalPointDelimiter)) {
                return false;
            } else {
                for(int i = 0; i < s.length(); ++i) {
                    char c = s.charAt(i);
                    if (c == decimalPointDelimiter.charAt(0)) {
                        if (seenDecimalPoint) {
                            return false;
                        }

                        seenDecimalPoint = true;
                    } else if (!Character.isDigit(c)) {
                        return false;
                    }
                }

                return true;
            }
        }
    }

    public static boolean isSignedFloat(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            try {
                float temp = Float.parseFloat(s);
                return temp <= 0.0F;
            } catch (Exception var2) {
                return false;
            }
        }
    }

    public static boolean isSignedDouble(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            try {
                Double.parseDouble(s);
                return true;
            } catch (Exception var2) {
                return false;
            }
        }
    }

    public static boolean isAlphabetic(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            for(int i = 0; i < s.length(); ++i) {
                char c = s.charAt(i);
                if (!isLetter(c)) {
                    return false;
                }
            }

            return true;
        }
    }

    public static boolean isAlphanumeric(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            for(int i = 0; i < s.length(); ++i) {
                char c = s.charAt(i);
                if (!isLetterOrDigit(c)) {
                    return false;
                }
            }

            return true;
        }
    }

    public static boolean isSSN(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            String normalizedSSN = stripCharsInBag(s, SSNDelimiters);
            if(isEmpty(normalizedSSN)){
                return defaultEmptyOK;
            }else
            return isInteger(normalizedSSN) && normalizedSSN.length() == digitsInSocialSecurityNumber;
        }
    }

    public static boolean isUSPhoneNumber(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            String normalizedPhone = stripCharsInBag(s, phoneNumberDelimiters);
            if(isEmpty(normalizedPhone)){
                return defaultEmptyOK;
            }else
                return isInteger(normalizedPhone) && normalizedPhone.length() == digitsInUSPhoneNumber;
        }
    }

    public static boolean isUSPhoneAreaCode(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            String normalizedPhone = stripCharsInBag(s, phoneNumberDelimiters);
            if(isEmpty(normalizedPhone)){
                return defaultEmptyOK;
            }else
            return isInteger(normalizedPhone) && normalizedPhone.length() == digitsInUSPhoneAreaCode;
        }
    }

    public static boolean isUSPhoneMainNumber(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            String normalizedPhone = stripCharsInBag(s, phoneNumberDelimiters);
            if(isEmpty(normalizedPhone)){
                return defaultEmptyOK;
            }else
            return isInteger(normalizedPhone) && normalizedPhone.length() == digitsInUSPhoneMainNumber;
        }
    }

    public static boolean isInternationalPhoneNumber(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            String normalizedPhone = stripCharsInBag(s, phoneNumberDelimiters);
            return isPositiveInteger(normalizedPhone);
        }
    }

    public static boolean isZipCode(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            String normalizedZip = stripCharsInBag(s, ZipCodeDelimeter);
            if(isEmpty(normalizedZip)){
                return defaultEmptyOK;
            }else
            return isInteger(normalizedZip) && (normalizedZip.length() == digitsInZipCode1 || normalizedZip.length() == digitsInZipCode2);
        }
    }

    public static boolean isContiguousZipCode(String s) {
        boolean retval = false;
        if (isZipCode(s)) {
            if (isEmpty(s)) {
                retval = defaultEmptyOK;
            } else {
                String normalizedZip = s.substring(0, 5);
                int iZip = Integer.parseInt(normalizedZip);
                retval = (iZip < 96701 || iZip > 96898) && (iZip < 99501 || iZip > 99950);
            }
        }

        return retval;
    }

    public static boolean isStateCode(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            return USStateCodes.contains(s) && !s.contains(USStateCodeDelimiter);
        }
    }

    public static boolean isContiguousStateCode(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            return ContiguousUSStateCodes.contains(s) && !s.contains(USStateCodeDelimiter);
        }
    }

    public static boolean isEmail(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else if (isWhitespace(s)) {
            return false;
        } else {
            int i = 1;

            int sLength;
            for(sLength = s.length(); i < sLength && s.charAt(i) != '@'; ++i) {
            }

            return i < sLength - 1 && s.charAt(i) == '@';
        }
    }

    public static boolean isUrl(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            return s.contains("://");
        }
    }

    public static boolean isYear(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else if (!isNonnegativeInteger(s)) {
            return false;
        } else {
            return s.length() == 2 || s.length() == 4;
        }
    }

    public static boolean isIntegerInRange(String s, int a, int b) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else if (!isSignedInteger(s)) {
            return false;
        } else {
            int num = Integer.parseInt(s);
            return num >= a && num <= b;
        }
    }

    public static boolean isMonth(String s) {
        return isEmpty(s) || isIntegerInRange(s, 1, 12);
    }

    public static boolean isDay(String s) {
        return isEmpty(s) || isIntegerInRange(s, 1, 31);
    }

    public static int daysInFebruary(int year) {
        return year % 4 != 0 || year % 100 == 0 && year % 400 != 0 ? 28 : 29;
    }

    public static boolean isHour(String s) {
        return isEmpty(s) || isIntegerInRange(s, 0, 23);
    }

    public static boolean isMinute(String s) {
        return isEmpty(s) || isIntegerInRange(s, 0, 59);
    }

    public static boolean isSecond(String s) {
        return isEmpty(s) || isIntegerInRange(s, 0, 59);
    }

    public static boolean isDate(String year, String month, String day) {
        if (isYear(year) && isMonth(month) && isDay(day)) {
            int intYear = Integer.parseInt(year);
            int intMonth = Integer.parseInt(month);
            int intDay = Integer.parseInt(day);
            if (intDay > daysInMonth[intMonth - 1]) {
                return false;
            } else {
                return intMonth != 2 || intDay <= daysInFebruary(intYear);
            }
        } else {
            return false;
        }
    }

    public static boolean isDate(String date) {
        if (isEmpty(date)) {
            return defaultEmptyOK;
        } else {
            int dateSlash1 = date.indexOf('-');
            int dateSlash2 = date.lastIndexOf('-');
            if (dateSlash1 > 0 && dateSlash1 != dateSlash2) {
                String year = date.substring(0, dateSlash1);
                String month = date.substring(dateSlash1 + 1, dateSlash2);
                String day = date.substring(dateSlash2 + 1);
                return isDate(year, month, day);
            } else {
                return false;
            }
        }
    }

    public static boolean isDateAfterToday(String date) {
        if (isEmpty(date)) {
            return defaultEmptyOK;
        } else {
            int dateSlash1 = date.indexOf('-');
            int dateSlash2 = date.lastIndexOf('-');
            if (dateSlash1 <= 0) {
                return false;
            } else {
                Date passed = null;
                String year;
                String month;
                if (dateSlash1 == dateSlash2) {
                    year = date.substring(0, dateSlash1);
                    month = date.substring(dateSlash1 + 1);
                    if (!isDate(year, month, month)) {
                        return false;
                    }

                    try {
                        int monthInt = Integer.parseInt(month);
                        int yearInt = Integer.parseInt(year);
                        Calendar calendar = Calendar.getInstance();
                        calendar.set(yearInt, monthInt - 1, 0, 0, 0, 0);
                        calendar.add(Calendar.MONTH, 1);
                        passed = new Date(calendar.getTime().getTime());
                    } catch (Exception var10) {
                        passed = null;
                    }
                } else {
                    year = date.substring(0, dateSlash1);
                    month = date.substring(dateSlash2 + 1);
                    if (!isDate(year, month, month)) {
                        return false;
                    }

                }

                Date now = DateUtilCore.nowDate();
                return passed != null && passed.after(now);
            }
        }
    }

    public static boolean isDateAfterDate(String date, java.sql.Date theDate) {
        if (isEmpty(date)) {
            return defaultEmptyOK;
        } else {
            int dateSlash1 = date.indexOf('-');
            int dateSlash2 = date.lastIndexOf('-');
            if (dateSlash1 <= 0) {
                return false;
            } else {
                Date passed = null;
                String year;
                String month;
                if (dateSlash1 == dateSlash2) {
                    year = date.substring(0, dateSlash1);
                    month = date.substring(dateSlash1 + 1);
                    if (!isDate(year, month, month)) {
                        return false;
                    }

                    try {
                        int monthInt = Integer.parseInt(month);
                        int yearInt = Integer.parseInt(year);
                        Calendar calendar = Calendar.getInstance();
                        calendar.set(yearInt, monthInt - 1, 0, 0, 0, 0);
                        calendar.add(Calendar.MONTH, 1);
                        passed = new Date(calendar.getTime().getTime());
                    } catch (Exception var11) {
                        return false;
                    }
                } else {
                    year = date.substring(0, dateSlash1);
                    month = date.substring(dateSlash2 + 1);
                    if (!isDate(year, month, month)) {
                        return false;
                    }

                    passed = DateUtilCore.getDate(year, month, month);
                }

                return ((Date) passed).after(theDate);
            }
        }
    }

    public static boolean isTime(String hour, String minute, String second) {
        return isHour(hour) && isMinute(minute) && isSecond(second);
    }

    public static boolean isTime(String time) {
        if (isEmpty(time)) {
            return defaultEmptyOK;
        } else {
            int timeColon1 = time.indexOf(':');
            int timeColon2 = time.lastIndexOf(':');
            if (timeColon1 <= 0) {
                return false;
            } else {
                String hour = time.substring(0, timeColon1);
                String minute;
                String second;
                if (timeColon1 == timeColon2) {
                    minute = time.substring(timeColon1 + 1);
                    second = "0";
                } else {
                    minute = time.substring(timeColon1 + 1, timeColon2);
                    second = time.substring(timeColon2 + 1);
                }

                return isTime(hour, minute, second);
            }
        }
    }

    public static boolean isValueLinkCard(String stPassed) {
        if (isEmpty(stPassed)) {
            return defaultEmptyOK;
        } else {
            String st = stripCharsInBag(stPassed, creditCardDelimiters);
            return st.length() == 16 && (st.startsWith("7") || st.startsWith("6"));
        }
    }

    public static boolean isGiftCard(String stPassed) {
        return isValueLinkCard(stPassed);
    }

    public static int getLuhnSum(String stPassed) {
        stPassed = stPassed.replaceAll("\\D", "");
        int len = stPassed.length();
        int sum = 0;
        int mul = 1;

        for(int i = len - 1; i >= 0; --i) {
            int digit = Character.digit(stPassed.charAt(i), 10);
            digit *= mul == 1 ? mul++ : mul--;
            sum += digit >= 10 ? digit % 10 + 1 : digit;
        }

        return sum;
    }

    public static int getLuhnCheckDigit(String stPassed) {
        int sum = getLuhnSum(stPassed);
        int mod = ((sum / 10 + 1) * 10 - sum) % 10;
        return 10 - mod;
    }

    public static boolean sumIsMod10(int sum) {
        return sum % 10 == 0;
    }

    public static String appendCheckDigit(String stPassed) {
        String checkDigit = Integer.valueOf(getLuhnCheckDigit(stPassed)).toString();
        return stPassed + checkDigit;
    }

    public static boolean isCreditCard(String stPassed) {
        if (isEmpty(stPassed)) {
            return defaultEmptyOK;
        } else {
            String st = stripCharsInBag(stPassed, creditCardDelimiters);
            return st.length() <= 19 && sumIsMod10(getLuhnSum(st));
        }
    }

    public static boolean isVisa(String cc) {
        return ((cc.length() == 16 || cc.length() == 13) && "4".equals(cc.substring(0, 1))) && isCreditCard(cc);
    }

    public static boolean isMasterCard(String cc) {
        int firstdig = Integer.parseInt(cc.substring(0, 1));
        int seconddig = Integer.parseInt(cc.substring(1, 2));
        return (cc.length() == 16 && firstdig == 5 && seconddig >= 1 && seconddig <= 5) && isCreditCard(cc);
    }

    public static boolean isAmericanExpress(String cc) {
        int firstdig = Integer.parseInt(cc.substring(0, 1));
        int seconddig = Integer.parseInt(cc.substring(1, 2));
        return cc.length() == 15 && firstdig == 3 && (seconddig == 4 || seconddig == 7) && isCreditCard(cc);
    }

    public static boolean isDinersClub(String cc) {
        int firstdig = Integer.parseInt(cc.substring(0, 1));
        int seconddig = Integer.parseInt(cc.substring(1, 2));
        return cc.length() == 14 && firstdig == 3 && (seconddig == 0 || seconddig == 6 || seconddig == 8) && isCreditCard(cc);
    }

    public static boolean isCarteBlanche(String cc) {
        return isDinersClub(cc);
    }

    public static boolean isDiscover(String cc) {
        String first4digs = cc.substring(0, 4);
        return (cc.length() == 16 && "6011".equals(first4digs)) && isCreditCard(cc);
    }

    public static boolean isEnRoute(String cc) {
        String first4digs = cc.substring(0, 4);
        return cc.length() == 15 && ("2014".equals(first4digs) || "2149".equals(first4digs)) && isCreditCard(cc);
    }

    public static boolean isJCB(String cc) {
        String first4digs = cc.substring(0, 4);
        return cc.length() == 16 && ("3088".equals(first4digs) || "3096".equals(first4digs) || "3112".equals(first4digs) || "3158".equals(first4digs) || "3337".equals(first4digs) || "3528".equals(first4digs)) && isCreditCard(cc);
    }

    public static boolean isAnyCard(String ccPassed) {
        if (isEmpty(ccPassed)) {
            return defaultEmptyOK;
        } else {
            String cc = stripCharsInBag(ccPassed, creditCardDelimiters);
            if(isNotEmpty(cc)) {
                if (!isCreditCard(cc)) {
                    return false;
                } else {
                    return isMasterCard(cc) || isVisa(cc) || isAmericanExpress(cc) || isDinersClub(cc) || isDiscover(cc) || isEnRoute(cc) || isJCB(cc);
                }
            }else {
                return false;
            }
        }
    }

    public static String getCardType(String ccPassed) {
        if (isEmpty(ccPassed)) {
            return "Unknown";
        } else {
            String cc = stripCharsInBag(ccPassed, creditCardDelimiters);
            if(isNotEmpty(cc)) {
                if (!isCreditCard(cc)) {
                    return "Unknown";
                } else if (isMasterCard(cc)) {
                    return "MasterCard";
                } else if (isVisa(cc)) {
                    return "Visa";
                } else if (isAmericanExpress(cc)) {
                    return "AmericanExpress";
                } else if (isDinersClub(cc)) {
                    return "DinersClub";
                } else if (isDiscover(cc)) {
                    return "Discover";
                } else if (isEnRoute(cc)) {
                    return "EnRoute";
                } else {
                    return isJCB(cc) ? "JCB" : "Unknown";
                }
            }else {
                return "Unknown";
            }
        }
    }

    public static boolean isCardMatch(String cardType, String cardNumberPassed) {
        if (isEmpty(cardType)) {
            return defaultEmptyOK;
        } else if (isEmpty(cardNumberPassed)) {
            return defaultEmptyOK;
        } else {
            String cardNumber = stripCharsInBag(cardNumberPassed, creditCardDelimiters);
            if ("VISA".equalsIgnoreCase(cardType) && isVisa(cardNumber)) {
                return true;
            } else if ("MASTERCARD".equalsIgnoreCase(cardType) && isMasterCard(cardNumber)) {
                return true;
            } else if (("AMERICANEXPRESS".equalsIgnoreCase(cardType) || "AMEX".equalsIgnoreCase(cardType)) && isAmericanExpress(cardNumber)) {
                return true;
            } else if ("DISCOVER".equalsIgnoreCase(cardType) && isDiscover(cardNumber)) {
                return true;
            } else if ("JCB".equalsIgnoreCase(cardType) && isJCB(cardNumber)) {
                return true;
            } else if (("DINERSCLUB".equalsIgnoreCase(cardType) || "DINERS".equalsIgnoreCase(cardType)) && isDinersClub(cardNumber)) {
                return true;
            } else if ("CARTEBLANCHE".equalsIgnoreCase(cardType) && isCarteBlanche(cardNumber)) {
                return true;
            } else {
                return "ENROUTE".equalsIgnoreCase(cardType) && isEnRoute(cardNumber);
            }
        }
    }

    public static boolean isNotPoBox(String s) {
        if (isEmpty(s)) {
            return defaultEmptyOK;
        } else {
            String sl = s.toLowerCase();
            if (sl.contains("p.o. b")) {
                return false;
            } else if (sl.contains("p.o.b")) {
                return false;
            } else if (sl.contains("p.o b")) {
                return false;
            } else if (sl.contains("p o b")) {
                return false;
            } else if (sl.contains("po b")) {
                return false;
            } else if (sl.contains("pobox")) {
                return false;
            } else if (sl.contains("po#")) {
                return false;
            } else if (sl.contains("po #")) {
                return false;
            } else if (sl.contains("p.0. b")) {
                return false;
            } else if (sl.contains("p.0.b")) {
                return false;
            } else if (sl.contains("p.0 b")) {
                return false;
            } else if (sl.contains("p 0 b")) {
                return false;
            } else if (sl.contains("p0 b")) {
                return false;
            } else if (sl.contains("p0box")) {
                return false;
            } else if (sl.contains("p0#")) {
                return false;
            } else {
                return !sl.contains("p0 #");
            }
        }
    }
}
