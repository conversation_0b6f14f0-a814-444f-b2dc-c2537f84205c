<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.analysis.mapper.read.BmsAnalysisReadMapper">


    <select id="queryQosAnalysis" resultType="com.jp.med.bms.modules.analysis.vo.BmsAnalysisVo">
        select x.*,
               y.totalZrc,
               y.avgZyts,
               y.avgRjfy,
               z.totalZrc
        from
        (select
             max(case when m.budget_code = 'PJZYR' then budget_amount else 0 end) as pjzyr,
             max(case when m.budget_code = 'CYRC' then budget_amount else 0 end) as cyrc,
             max(case when m.budget_code = 'MCRPJFY' then budget_amount else 0 end) as mcrpjfy,
             m.org_id as orgId,
             o.org_name as orgName
             from bms_budget_results m
             inner join bms_budget_task n
             on m.budet_task_code = n.budet_task_code
             inner join hrm_org o on m.org_id = o.org_id
          where n.year = substr(#{dateRage.[0],jdbcType=VARCHAR}, 1, 4)
          group by m.org_id,o.org_name
        ) x
        left join
        (select c.org_id AS orgId,
               c.org_name AS orgName,
               sum(cast(b.zje as numeric)) as totalZje,
               count(1) totalZrc,
               avg(b.zyts) as avgZyts,
               avg(cast(b.zje as numeric)) / avg(b.zyts) as avgRjfy
        from zy_brsyk a
            inner join zy_brjsk b
            on a.syxh = b.syxh
            left join sys_dept_mapping c on trim(b.ksdm) = c.org_id_orign
        where
            <![CDATA[
                substr(b.cyrq, 1, 8) >= #{dateRage.[0],jdbcType=VARCHAR} and substr(b.cyrq, 1, 8) <= #{dateRage.[1],jdbcType=VARCHAR}
            ]]>
        group by c.org_id,c.org_name
        ) y on x.orgId = y.orgId
        left join (
            select c.org_id AS orgId,
                   c.org_name AS orgName,
                   sum(cast(b.zje as numeric)) as totalZje,
                   count(1) totalZrc,
                   avg(b.zyts) as avgZyts，
                   avg(cast(b.zje as numeric)) / avg(b.zyts) as avgRjfy
            from zy_brsyk a
                inner join zy_brjsk b
                on a.syxh = b.syxh
                left join sys_dept_mapping c on trim(b.ksdm) = c.org_id_orign
            where
                <![CDATA[
                    substr(b.cyrq, 1, 8) >= #{dateRageYoy.[0],jdbcType=VARCHAR} and substr(b.cyrq, 1, 8) <= #{dateRageYoy.[1],jdbcType=VARCHAR}
                ]]>
            group by c.org_id,c.org_name
        ) z on y.orgId = z.orgId
    </select>

    <select id="queryIncomeAnalysis" resultType="com.jp.med.bms.modules.analysis.vo.BmsAnalysisVo">

    </select>

    <select id="queryMZQosAnalysis" resultType="com.jp.med.bms.modules.analysis.vo.BmsAnalysisVo">
        select x.*,
               y.totalZrc,
               y.avgZyts,
               y.avgRjfy,
               z.totalZrc
        from
            (select
                 max(case when m.budget_code = 'PJZYR' then budget_amount else 0 end) as pjzyr,
                 max(case when m.budget_code = 'CYRC' then budget_amount else 0 end) as cyrc,
                 max(case when m.budget_code = 'MCRPJFY' then budget_amount else 0 end) as mcrpjfy,
                 m.org_id as orgId,
                 o.org_name as orgName
             from bms_budget_results m
                      inner join bms_budget_task n
                                 on m.budet_task_code = n.budet_task_code
                      inner join hrm_org o on m.org_id = o.org_id
             where n.year = substr(#{dateRage.[0],jdbcType=VARCHAR}, 1, 4)
             group by m.org_id,o.org_name
            ) x
                left join
            (select c.org_id AS orgId,
                    c.org_name AS orgName,
                    sum(cast(b.zje as numeric)) as totalZje,
                    count(1) totalZrc,
                    avg(b.zyts) as avgZyts,
                    avg(cast(b.zje as numeric)) / avg(b.zyts) as avgRjfy
             from zy_brsyk a
                      inner join zy_brjsk b
                                 on a.syxh = b.syxh
                      left join sys_dept_mapping c on trim(b.ksdm) = c.org_id_orign
             where
            <![CDATA[
                 substr(b.cyrq, 1, 8) >= #{dateRage.[0],jdbcType=VARCHAR} and substr(b.cyrq, 1, 8) <= #{dateRage.[1],jdbcType=VARCHAR}
            ]]>
        group by c.org_id,c.org_name
            ) y on x.orgId = y.orgId
                left join (
                select c.org_id AS orgId,
                       c.org_name AS orgName,
                       sum(cast(b.zje as numeric)) as totalZje,
                       count(1) totalZrc,
                       avg(b.zyts) as avgZyts，
                    avg(cast(b.zje as numeric)) / avg(b.zyts) as avgRjfy
                from zy_brsyk a
                    inner join zy_brjsk b
                on a.syxh = b.syxh
                    left join sys_dept_mapping c on trim(b.ksdm) = c.org_id_orign
                where
                <![CDATA[
                    substr(b.cyrq, 1, 8) >= #{dateRageYoy.[0],jdbcType=VARCHAR} and substr(b.cyrq, 1, 8) <= #{dateRageYoy.[1],jdbcType=VARCHAR}
                ]]>
            group by c.org_id,c.org_name
            ) z on y.orgId = z.orgId
    </select>
</mapper>
