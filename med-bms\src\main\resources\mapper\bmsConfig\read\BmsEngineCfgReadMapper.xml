<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.bmsConfig.mapper.read.BmsEngineCfgReadMapper">

    <select id="queryList" resultType="com.jp.med.bms.modules.bmsConfig.vo.BmsEngineCfgVo">
        select
            a.id as id,
            a.engine_code as engineCode,
            a.engine_name as engineName,
            a.active_flag as activeFlag,
            a.hospital_id as hospitalId
        from bms_engine_cfg a
        <where>
            <if test="engineCode != '' and engineCode != null">
                and a.engine_code = #{engineCode,jdbcType=VARCHAR}
            </if>
            <if test="activeFlag != '' and activeFlag != null">
                and a.active_flag = #{activeFlag,jdbcType=VARCHAR}
            </if>
            <if test="type != '' and type != null">
                and a.type = #{type,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>
