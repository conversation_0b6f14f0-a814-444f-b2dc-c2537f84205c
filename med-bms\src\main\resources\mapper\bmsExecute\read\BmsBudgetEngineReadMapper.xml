<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.bmsExecute.mapper.read.BmsBudgetEngineReadMapper">

    <select id="queryList" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetEngineVo">
        select
            a.id as id,
            a.itemname as itemname,
            coalesce(d.engine_name,a.itemname) as engineName,
            a.purc_way as purcWay,
            a.budget_amount as budgetAmount,
            sum(a.budget_amount) over(partition by a.dept, a.task_code) as budgetAmountSum,
            a.dept as dept,
            c.org_name as orgName,
            a.memo as memo,
            a.task_code as taskCode,
            a.req_dscr as reqDscr,
            a.impl_efft as implEfft,
            a.price as price,
            a.cnt as cnt,
            a.type as type,
            b.budget_task_name as taskName,
            a.chk as chk,
            b.hospital_id as hospitalId
        from bms_budget_engine a left join bms_budget_task b on a.task_code = b.budget_task_code
        left join hrm_org c on a.dept = c.org_id
        left join bms_engine_cfg d on a.itemname = d.engine_code
        <where>
            <if test="curSysOrgId != '' and curSysOrgId != null">
                and a.dept = #{curSysOrgId,jdbcType=VARCHAR}
            </if>
            <if test="dept != '' and dept != null">
                and a.dept = #{dept,jdbcType=VARCHAR}
            </if>
            <if test="taskCode != '' and taskCode != null">
                and a.task_code = #{taskCode,jdbcType=VARCHAR}
            </if>
            <if test="chk != '' and chk != null">
                and a.chk = #{chk,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>
