<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.bmsExecute.mapper.read.BmsBudgetEquipmentReadMapper">

    <select id="queryList" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetEquipmentVo">
        select
            a.id as id,
            a.equipment_name as equipmentName,
            a.unit as unit,
            a.price as price,
            a.memo as memo,
            a.cnt as cnt,
            a.budget_amount as budgetAmount,
            sum(a.budget_amount) over(partition by a.dept, a.task_code) as budgetAmountSum,
            a.dept as dept,
            c.org_name as orgName,
            a.task_code as taskCode,
            b.budget_task_name as taskName,
            a.chk as chk,
            b.hospital_id as hospitalId
        from bms_budget_equipment a
        left join bms_budget_task b on a.task_code = b.budget_task_code
        left join hrm_org c on a.dept = c.org_id
        <where>
            <if test="curSysOrgId != '' and curSysOrgId != null">
                and a.dept = #{curSysOrgId,jdbcType=VARCHAR}
            </if>
            <if test="dept != '' and dept != null">
                and a.dept = #{dept,jdbcType=VARCHAR}
            </if>
            <if test="taskCode != '' and taskCode != null">
                and a.task_code = #{taskCode,jdbcType=VARCHAR}
            </if>
            <if test="chk != '' and chk != null">
                and a.chk = #{chk,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>
