<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.bmsExecute.mapper.read.BmsBudgetTaskReadMapper">

    <select id="queryList" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetTaskVo">
        select
            a.id as id,
            a.budget_task_code as budgetTaskCode,
            a.budget_task_name as budgetTaskName,
            a.create_time as createTime,
            a.active_flag as activeFlag,
            a.budget_flow_code as budgetFlowCode,
            a.budget_table_id as budgetTableId,
            b.budget_flow_name as budgetFlowName,
            c.budget_table_name as budgetTableName,
            a.status as status,
            a.remarks as remarks,
            a.year as year,
            a.hospital_id as hospitalId
        from bms_budget_task a
        left join bms_budget_flow b on a.budget_flow_code = b.budget_flow_code
        left join bms_budget_table c on a.budget_table_id = c.id
        <where>
            <if test="budgetTaskCode != '' and budgetTaskCode != null">
               and a.budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryTaskFlowDetail" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetTaskVo">
        select
            a.budget_task_code as budgetTaskCode,
            a.budget_task_name as budgetTaskName,
            b.flow_detail_code as flowDetailCdoe,
            b.flow_detail_name as flowDetailName,
            a.hospital_id as hospitalId
        from bms_budget_task a
            left join bms_flow_detail b
        on a.budget_flow_code =b.budget_flow_code
    </select>


</mapper>
