<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.bmsExecute.mapper.read.BmsBudgetWorkloadReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetWorkloadVo" id="budgetWorkloadMap">
        <result property="id" column="id"/>
        <result property="budgetTaskCode" column="budget_task_code"/>
        <result property="month" column="month"/>
        <result property="budgetCode" column="budget_code"/>
        <result property="budgetName" column="budget_name"/>
        <result property="budgetTypeCode" column="budget_type_code"/>
        <result property="actualAmt" column="actual_amt"/>
    </resultMap>

    <!--  业务指标执行情况月度值  -->
    <select id="queryList" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetWorkloadVo">
        select
        budgetTaskCode,
        month,
        budgetCode,
        budgetTypeCode,
        sum(actualAmt) as actualAmt,
        deptCode,
        deptName,
        budgetAmt
        from
        (
        select
        c.budget_task_code as budgetTaskCode,
        #{month,jdbcType=VARCHAR} as month,
        c.budget_code as budgetCode,
        coalesce(A.budget_type_code,
        'SR') as budgetTypeCode,
        ROUND(a.actual_amt,
        2) as actualAmt,
        coalesce(C.budget_amount,
        0.00) as budgetAmt,
        C.org_id as deptCode,
        b.org_name as deptName
        from
        bms_budget_results c
        left join
        hrm_org b
        on c.org_id = b.org_id
        left join
        bms_budget_workload a
        on
        C.budget_task_code = A.budget_task_code
        and C.budget_code = A.budget_code
        and C.org_id = A.dept_code
        and A.MONTH = #{month,jdbcType=VARCHAR}
        and A.budget_type_code = #{budgetTypeCode,jdbcType=VARCHAR}
        WHERE c.budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
        and  c.budget_code in ('MZRCKZFY', 'CYRC', 'CJFY', 'MZRC')
        <if test="deptQuery != null and deptQuery.size() > 0">
            and c.org_id in
            <foreach collection="deptQuery" item="code" open="(" separator="," close=")">
                #{code,jdbcType=VARCHAR}
            </foreach>
        </if>
        ) x
        group by budgetTaskCode,month,budgetCode,
        budgetTypeCode,deptCode,deptName,budgetAmt
    </select>

    <!--  月度累计  -->
    <select id="queryMonthCountList" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetSRVo">
        SELECT dept_code,#{month,jdbcType=VARCHAR} as month, round(sum(CASE WHEN budget_code = 'MZRC' THEN actual_amt END),2)  AS mzrc,
               round(sum(CASE WHEN budget_code = 'MZRCKZFY' THEN actual_amt END)/sum(CASE WHEN budget_code = 'MZRC' THEN actual_amt END),2) AS mzrckzfy,
               sum(CASE WHEN budget_code = 'MZRCKZFY' THEN actual_amt END) as mzTotalFee,
               round(sum(CASE WHEN budget_code = 'CYRC' THEN actual_amt END),2)  AS cyrc,
               round(sum(CASE WHEN budget_code = 'CJFY' THEN actual_amt END) /sum(CASE WHEN budget_code = 'CYRC' THEN actual_amt END),2) AS cjfy,
               sum(CASE WHEN budget_code = 'CJFY' THEN actual_amt END) as zyTotalFee
        FROM (
        SELECT dept_code,  budget_code,actual_amt FROM bms_budget_workload WHERE  budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
        <![CDATA[  and MONTH <= #{month,jdbcType=VARCHAR}]]>  and budget_code = 'MZRC'
        UNION ALL
        SELECT dept_code,  budget_code,actual_amt FROM bms_budget_workload WHERE budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
        <![CDATA[  and MONTH <= #{month,jdbcType=VARCHAR}]]>  and budget_code = 'MZRCKZFY'
        UNION ALL
        SELECT dept_code,  budget_code,  actual_amt FROM bms_budget_workload WHERE budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
        <![CDATA[  and MONTH <= #{month,jdbcType=VARCHAR}]]>  and  budget_code = 'CYRC'
        UNION ALL
        SELECT dept_code,  budget_code,  actual_amt FROM bms_budget_workload WHERE budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
        <![CDATA[  and MONTH <= #{month,jdbcType=VARCHAR}]]>  and  budget_code = 'CJFY'
        ) AS all_sales
        GROUP BY dept_code
        ORDER BY dept_code
    </select>

    <!-- 查询表头 -->
    <select id="queryTtile" resultType="com.jp.med.bms.modules.dispose.vo.TitleVo">
        <!--select distinct b.budget_code as key,
        b.budget_name as title,
        true as summary,
        'default' as sorter,
        '150' as width
        from
        bms_budget_table_proj b,
        bms_budget_task c
        where c.budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
        and c.active_flag = '1'
        and c.budget_table_id = b.budget_table_id
        AND b.budget_type_code =  #{budgetTypeCode,jdbcType=VARCHAR}
        group by B.budget_code, b.budget_name-->
        select budget_code AS key,
        budget_name as title,
        true as summary,
        'default' as sorter,
        '150' as width
        from
        (WITH RECURSIVE tpath AS (
        SELECT
        budget_code,
        budget_name AS budget_name_real,
        budget_parent_id,
        budget_name :: VARCHAR ( 500 )
        FROM
        bms_budget_table_proj
        WHERE
        (budget_parent_id IS NULL
        OR budget_parent_id = '')
        AND budget_table_id = (select budget_table_id from bms_budget_task where budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR} AND active_flag = '1')
        and budget_type_code = #{budgetTypeCode,jdbcType=VARCHAR}
        UNION ALL
        SELECT
        A.budget_code,
        A.budget_name,
        A.budget_parent_id,
        ( tpath.budget_name || '/' || A.budget_name ) :: VARCHAR ( 500 )
        FROM
        bms_budget_table_proj A,
        tpath
        WHERE
        A.budget_parent_id = tpath.budget_code AND a.budget_table_id = (select budget_table_id from bms_budget_task where budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR} AND active_flag = '1')
        and budget_type_code = #{budgetTypeCode,jdbcType=VARCHAR}
        ) SELECT
        *
        FROM
        tpath) x
    </select>

    <select id="queryExcelTitle" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetWorkloadVo">
        select budget_code as budgetCode,budget_name as budgetName from
            (WITH RECURSIVE tpath AS (
                SELECT
                    budget_code,
                    budget_name AS budget_name_real,
                    budget_parent_id,
                    budget_name :: VARCHAR ( 500 )
                FROM
                    bms_budget_table_proj
                WHERE
                    (budget_parent_id IS NULL
                        OR budget_parent_id = '')
                  AND budget_table_id = (select budget_table_id from bms_budget_task where budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR})
                  and budget_type_code = #{budgetTypeCode,jdbcType=VARCHAR}
                UNION ALL
                SELECT
                    A.budget_code,
                    A.budget_name,
                    A.budget_parent_id,
                    ( tpath.budget_name || '/' || A.budget_name ) :: VARCHAR ( 500 )
                FROM
                    bms_budget_table_proj A,
                    tpath
                WHERE
                    A.budget_parent_id = tpath.budget_code AND a.budget_table_id = (select budget_table_id from bms_budget_task where budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR})
                  and budget_type_code = #{budgetTypeCode,jdbcType=VARCHAR}
            ) SELECT
                 *
             FROM
                 tpath) x
    </select>
    <!--  查询科室对照  -->
    <select id="queryBudgetDeptCrspList" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsBudgDeptCrspVo">
        select
        budg_dept_crsp_id as budgdeptCrspId , <!-- 预算科室对照ID -->
        budg_rpt_dept_name as budgRptDeptName, <!-- 预算报表科室名称 -->
        dept_type as deptType, <!-- 科室类型 -->
        dept_name as deptName, <!-- HRP科室名称 -->
        dept_codg as deptCodg, <!-- HRP科室编码 -->
        budg_year as budgYear, <!-- 预算年度 -->
        vali_flag as valiFlag  <!-- 有效标志 -->
        from bms_budg_dept_crsp a
        where budg_year = #{budgYear,jdbcType=VARCHAR}
    </select>

    <select id="queryReimItemToBudget" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsReimItemToBudgCfgVo">
        SELECT A.ID,
               A.reim_item_code as reimItemCode,
               A.reim_item_name as reimItemName,
               A.budget_code as budgetCode,
               A.crter,
               A.create_time as createTime,
               A.modi_time as modiTime,
               A.hospital_id as hospitalId,
               A.active_flag as activeFlag,
               A."year",
               A.type,
               A.bgt_summary as bgtSummary
        FROM
            ecs_reim_item_to_budg_cfg A
        WHERE
            "year" = #{year,jdbcType=VARCHAR}
          AND A.active_flag = '1'

        <!--排除差旅费、培训费-->
         <!-- AND A.reim_item_code NOT IN ( '30216', '30211' ) -->
    </select>

    <select id="queryActualDetail" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetWorkloadVo">
        <choose>
            <when test='type == "1"'>
                <!-- 会计科目 -->
                SELECT A.actig_sub_code AS actigSubCode,
                    A.actig_sub_name AS actigSubName,
                    A.dept_code AS deptCode,
                    A.dept_name,
                    COALESCE ( ROUND( SUM ( A.actig_amt ), 2 ), 0 ) AS actualAmt
                FROM
                    ecs_reim_asst_detail A
                WHERE
                    A.actig_sys = '1'
                    AND actig_amt_type = '1'
                    AND a.create_time between #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
                    <choose>
                        <when test="actigCodeStr != null and actigCodeStr.size() > 0">
                            and a.actig_sub_code in
                            <foreach collection="actigCodeStr" item="item" separator="," open="(" close=")">
                                #{item,jdbcType=VARCHAR}
                            </foreach>
                        </when>
                        <otherwise>
                            and a.actig_sub_code = #{actigSubCode,jdbcType=VARCHAR}
                        </otherwise>
                    </choose>
                    <if test="deptCode != null and deptCode != ''">
                        AND A.dept_code  = #{deptCode,jdbcType=VARCHAR}
                    </if>
                GROUP BY
                    A.actig_sub_code,
                    A.actig_sub_name,
                    A.dept_code,
                    A.dept_name
            </when>
            <otherwise>
                <!-- 经济科目 -->
                SELECT A.econ_sub_code,
                A.econ_sub_name,
                A.dept_code,
                A.dept_name,
                COALESCE ( ROUND( SUM ( A.actig_amt ), 2 ), 0 ) AS actualAmt
                FROM
                ecs_reim_asst_detail A
                WHERE
                A.actig_sys = '1'
                AND actig_amt_type = '1'
                AND a.create_time between #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
                <choose>
                    <when test="econCodeStr != null and econCodeStr.size() != 0">
                        AND A.econ_sub_code IN
                        <foreach collection="econCodeStr" item="code" separator="," open="(" close=")">
                            #{code,jdbcType=VARCHAR}
                        </foreach>
                    </when>
                    <otherwise>
                        and A.econ_sub_code = #{econSubCode,jdbcType=VARCHAR}
                    </otherwise>
                </choose>
                <if test="deptCode != null and deptCode != ''">
                    AND A.dept_code = #{deptCode,jdbcType=VARCHAR}
                </if>
                GROUP BY
                A.econ_sub_code,
                A.econ_sub_name,
                A.dept_code,
                A.dept_name
            </otherwise>
        </choose>
    </select>

    <select id="queryActualSummary" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetWorkloadVo">
        <choose>
            <when test='type == "1"'>
                <!-- 会计科目 -->
                <!--SELECT A.actig_sub_code,
                        A.actig_sub_name,
                        COALESCE ( ROUND( SUM ( A.actig_amt ), 2 ), 0 ) AS actualAmt
                FROM
                    ecs_reim_asst_detail A
                WHERE
                    A.actig_sys = '1'
                    AND actig_amt_type = '1'
                    AND a.create_time between #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
                <if test="actigCodeStr != null and actigCodeStr.size() > 0">
                    AND A.actig_sub_code IN
                    <foreach collection="actigCodeStr" item="code" separator="," open="(" close=")">
                        #{code,jdbcType=VARCHAR}
                    </foreach>
                </if>
                GROUP BY
                    A.actig_sub_code,
                    A.actig_sub_name-->
                SELECT q.actig_sub_code,
                        q.actig_sub_name,
                        COALESCE ( ROUND( SUM ( q.actig_amt ), 2 ), 0 ) AS actualAmt from (
                    SELECT
                    a.*
                    FROM ecs_reim_asst_detail a
                    WHERE a.vpzh IS NULL
                      AND a.reim_detail_id IS NOT NULL
                      AND a.actig_sys = '1'
                      AND a.actig_amt_type = '1'
                      <if test="actigCodeStr != null and actigCodeStr.size() > 0">
                          AND A.actig_sub_code IN
                        <foreach collection="actigCodeStr" item="code" separator="," open="(" close=")">
                            #{code,jdbcType=VARCHAR}
                        </foreach>
                      </if>
                      AND EXISTS (
                        SELECT 1
                        FROM erp_vcr_apply m
                        JOIN erp_vcr_item_detail n ON m.idpzh = n.idpzh
                        WHERE m.certificate_date between #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
                          AND a.reim_detail_id::TEXT = n.pzid
                      )

                    union all

                    SELECT
                    a.*
                    FROM ecs_reim_asst_detail a
                    WHERE a.vpzh IS not NULL
                      AND a.reim_detail_id IS NULL
                      AND a.actig_sys = '1'
                      AND a.actig_amt_type = '1'
                      <if test="actigCodeStr != null and actigCodeStr.size() > 0">
                          AND A.actig_sub_code IN
                        <foreach collection="actigCodeStr" item="code" separator="," open="(" close=")">
                            #{code,jdbcType=VARCHAR}
                        </foreach>
                      </if>
                      AND EXISTS (
                        SELECT 1
                        FROM erp_vcr_apply m
                        JOIN erp_vcr_item_detail n ON m.idpzh = n.idpzh
                        WHERE m.certificate_date between #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
                          AND a.vpzh = n.pzid
                      )) q GROUP BY
                                        q.actig_sub_code,
                                        q.actig_sub_name
            </when>
            <otherwise>
                <!-- 经济科目 -->
                <!--SELECT A.econ_sub_code,
                        A.econ_sub_name,
                        COALESCE ( ROUND( SUM ( A.actig_amt ), 2 ), 0 ) AS actualAmt
                FROM
                    ecs_reim_asst_detail A
                WHERE
                    A.actig_sys = '1'
                    AND actig_amt_type = '1'
                    AND a.create_time between #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
                <if test="econCodeStr != null and econCodeStr.size() > 0">
                    AND A.econ_sub_code IN
                    <foreach collection="econCodeStr" item="code" separator="," open="(" close=")">
                        #{code,jdbcType=VARCHAR}
                    </foreach>
                </if>
                GROUP BY
                    A.econ_sub_code,
                    A.econ_sub_name-->
                select q.econ_sub_code,q.econ_sub_name,COALESCE ( ROUND( SUM ( q.actig_amt ), 2 ), 0 ) AS actualAmt from (
                    SELECT
                    a.*
                    FROM ecs_reim_asst_detail a
                    WHERE a.vpzh IS NULL
                      AND a.reim_detail_id IS NOT NULL
                      AND a.actig_sys = '1'
                      AND a.actig_amt_type = '1'
                      <if test="econCodeStr != null and econCodeStr.size() > 0">
                          AND A.econ_sub_code IN
                            <foreach collection="econCodeStr" item="code" separator="," open="(" close=")">
                                #{code,jdbcType=VARCHAR}
                            </foreach>
                      </if>
                      AND EXISTS (
                        SELECT 1
                        FROM erp_vcr_apply m
                        JOIN erp_vcr_item_detail n ON m.idpzh = n.idpzh
                        WHERE m.certificate_date between #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
                          AND a.reim_detail_id::TEXT = n.pzid
                      )

                    union all

                    SELECT
                    a.*
                    FROM ecs_reim_asst_detail a
                    WHERE a.vpzh IS NOT NULL
                      AND a.reim_detail_id IS NULL
                      AND a.actig_sys = '1'
                      AND a.actig_amt_type = '1'
                      <if test="econCodeStr != null and econCodeStr.size() > 0">
                          AND A.econ_sub_code IN
                        <foreach collection="econCodeStr" item="code" separator="," open="(" close=")">
                            #{code,jdbcType=VARCHAR}
                        </foreach>
                      </if>
                      AND EXISTS (
                        SELECT 1
                        FROM erp_vcr_apply m
                        JOIN erp_vcr_item_detail n ON m.idpzh = n.idpzh
                        WHERE m.certificate_date between #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
                          AND a.vpzh = n.pzid
                      )) q
                        group by q.econ_sub_code,q.econ_sub_name
            </otherwise>
        </choose>
    </select>

    <select id="queryCLPXActualDetail" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetWorkloadVo">
        SELECT
            A.bus_met AS budgetCode,
            b.dept AS deptCode,
            C.org_name AS deptName,
            COALESCE ( ROUND( SUM ( b.reim_amt ), 2 ), 0 ) AS actualAmt
        FROM
            ecs_reim_detail A,
            ecs_reim_psn_detail b,
            hrm_org C
        WHERE
            A.TYPE = #{type,jdbcType=VARCHAR}
          <if test="budgetCodes != null and budgetCodes.size() != 0">
              AND A.bus_met IN
                <foreach collection="budgetCodes" item="code" separator="," open="(" close=")">
                    #{code,jdbcType=VARCHAR}
                </foreach>
          </if>
          AND A.ID = b.reim_detail_id
          AND b.dept = C.org_id
          AND EXISTS (
            SELECT
                1
            FROM
                erp_vcr_apply
                    M JOIN erp_vcr_item_detail n ON M.idpzh = n.idpzh
            WHERE
                M.certificate_date &gt;= #{startDate,jdbcType=VARCHAR}
              AND M.certificate_date &lt;= #{endDate,jdbcType=VARCHAR}
		AND A.ID :: TEXT = n.pzid
	)
GROUP BY
	A.bus_met,
	b.dept,
	C.org_name
    </select>
</mapper>
