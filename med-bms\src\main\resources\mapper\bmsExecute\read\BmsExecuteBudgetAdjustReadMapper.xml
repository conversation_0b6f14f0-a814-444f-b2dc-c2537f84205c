<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.bmsExecute.mapper.read.BmsExecuteBudgetAdjustReadMapper">

    <select id="queryList" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetAdjustVo">
        select
            a.budget_adjust_id as budgetAdjustId,
            a.budget_apply_id as budgetApplyId,
            a.budget_results_id as budgetResultsId,
            a.attachment as attachment,
            a.remark as remark,
            a.budget_amount as budgetAmount,
            a.original_budget_amount as originalBudgetAmount,
            a.budget_amount - a.original_budget_amount as adjustBudgetAmount,
            c.budget_code as budgetCode,
            c.budget_name as budgetName,
            c.unit as unit,
            d.org_name as centralizedDeptName,
            e.org_name as orgName,
            b.hospital_id as hospitalId
        from bms_budget_adjust a
        inner join bms_budget_results b
        on a.budget_results_id = b.budget_results_id
        inner join (
            select
                   x.budget_code,
                   x.budget_name,
                   x.unit,
                   y.budget_task_code
            from bms_budget_table_proj x
            inner join bms_budget_task y
            on x.budget_table_id = y.budget_table_id
        ) c
        on b.budget_task_code = c.budget_task_code and b.budget_code = c.budget_code
        inner join hrm_org d
        on b.centralized_dept = d.org_id
        inner join hrm_org e
        on b.org_id = e.org_id
        <where>
            <if test="budgetApplyId != '' and budgetApplyId != null">
                and a.budget_apply_id = #{budgetApplyId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="queryListNew" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetAdjustVo">
        select
        a.budget_adjust_id as budgetAdjustId,
        a.budget_apply_id as budgetApplyId,
        a.budget_results_id as budgetResultsId,
        a.attachment as attachment,
        a.remark as remark,
        a.budget_amount as budgetAmount,
        a.original_budget_amount as originalBudgetAmount,
        a.budget_amount - a.original_budget_amount as adjustBudgetAmount,
        c.budget_code as budgetCode,
        c.budget_name as budgetName,
        c.unit as unit,
        d.org_name as centralizedDeptName,
        e.org_name as orgName,
        b.hospital_id as hospitalId
        from bms_budget_adjust a
        inner join bms_budget_results b
        on a.budget_results_id = b.budget_results_id
        inner join (
        select
        x.budget_code,
        x.budget_name,
        x.unit,
        y.budget_task_code
        from bms_budget_table_proj x
        inner join bms_budget_task y
        on x.budget_table_id = y.budget_table_id
        ) c
        on b.budget_task_code = c.budget_task_code and b.budget_code = c.budget_code
        inner join hrm_org d
        on b.centralized_dept = d.org_id
        inner join hrm_org e
        on b.org_id = e.org_id
        <where>
            <if test="budgetApplyId != '' and budgetApplyId != null">
                and a.budget_apply_id = #{budgetApplyId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="queryBudget" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetAdjustVo">
        select
            x.budget_results_id as budgetResultsId,
            x.budget_code as budgetCode,
            y.budget_name as budgetName,
            x.budget_amount as budgetAmount,
            x.budget_amount as originalBudgetAmount,
            x.budget_flow_name as budgetFlowName,
            y.unit as unit,
            x.hospital_id as hospitalId,
            x.org_id as orgId,
            z.org_name as orgName,
            y.centralized_dept as centralizedDept,
            m.org_name as centralizedDeptName
        from
            (
                select
                    a.*,
                    b.budget_table_id ,
                    b.budget_flow_name
                from
                    bms_budget_results a
                inner join bms_budget_task b on
                a.budget_task_code = b.budget_task_code
            <where>
                <if test="budgetFlowId != '' and budgetFlowId != null">
                    and a.budget_flow_id = #{budgetFlowId,jdbcType=INTEGER}
                </if>
                <if test="orgId != '' and orgId != null">
                    and a.org_id = #{orgId}
                </if>
            </where>
            ) x
        inner join bms_budget_table_proj y on x.budget_code = y.budget_code and x.budget_table_id = y.budget_table_id
        inner join hrm_org z on x.org_id = z.org_id
        inner join hrm_org m on y.centralized_dept = m.org_id
        <where>
            <if test="codes != null and codes.size > 0">
                and x.budget_code in
                <foreach collection="codes" item="code" separator="," open="(" close=")">
                    #{code}
                </foreach>
            </if>
        </where>
    </select>


    <select id="queryBudgetNew" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetAdjustVo">
        select
        x.budget_results_id as budgetResultsId,
        x.budget_code as budgetCode,
        y.budget_name as budgetName,
        x.budget_amount as budgetAmount,
        x.budget_amount as originalBudgetAmount,
        x.budget_task_name as budgetTakeName,
        y.unit as unit,
        x.hospital_id as hospitalId,
        x.org_id as orgId,
        z.org_name as orgName,
        y.centralized_dept as centralizedDept,
        m.org_name as centralizedDeptName
        from
        (
        select
        a.*,
        b.budget_table_id ,
        b.budget_task_name
        from
        bms_budget_results a
        inner join bms_budget_task b on
        a.budget_task_code = b.budget_task_code
        <where>
            <if test="budgetTakeCode != '' and budgetTakeCode != null">
                and a.budget_task_code = #{budgetTakeCode}
            </if>
        </where>
        ) x
        inner join bms_budget_table_proj y on x.budget_code = y.budget_code and x.budget_table_id = y.budget_table_id
        inner join hrm_org z on x.org_id = z.org_id
        inner join hrm_org m on y.centralized_dept = m.org_id
        <where>
            <if test="ids != null and ids.size > 0">
                and x.budget_results_id in
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="budgetName != '' and budgetName != null">
                and y.budget_name like CONCAT( '%', #{budgetName},'%')
            </if>
            <if test="budgetTypeCode != '' and budgetTypeCode != null">
                and y.budget_type_code like CONCAT( '%', #{budgetTypeCode},'%')
            </if>
            <if test="deptCode != '' and deptCode != null">
                and x.org_id = #{deptCode}
            </if>
        </where>
    </select>

</mapper>
