<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.bmsExecute.mapper.read.BmsExecuteBudgetApplyReadMapper">

    <!-- 查询预算调整申请-->
    <select id="queryList" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetApplyVo">
        select
            a.budget_apply_id as budgetApplyId,
            a.flow_model_id as flowModelId,
            a.create_time as createTime,
            a.budget_flow_id as budgetFlowId,
            a.business_key as businessKey,
            a.org_id as orgId,
            a.username as username,
            a.remark as remark,
            a.attachment as attachment,
            a.status as status,
            a.hospital_id as hospitalId,
            b.model_case as modelCase,
            b.flow_mode_name as flowModeName,
            a.process_instance_id as processInstanceId
        from bms_budget_apply a
        left join bms_flow_model b on a.flow_model_id = b.flow_model_id
        <where>
            <if test="status != '' and status != null">
                a.status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="businessKey != '' and businessKey != null">
                a.business_key = #{businessKey,jdbcType=VARCHAR}
            </if>
            <if test="curSysOrgId != '' and curSysOrgId != null">
                a.org_id = #{curSysOrgId,jdbcType=VARCHAR}
            </if>
        </where>
        order by a.create_time desc
    </select>

    <select id="selectByParam" resultType="com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetApplyDto">
        select a.*,b.org_name as orgName from bms_budget_apply a left join hrm_org b on a.org_id = b.org_id
        <where>
            <if test="budgetApplyId != '' and budgetApplyId != null">
                and a.budget_apply_id = #{budgetApplyId}
            </if>
            <if test="processInstanceId != '' and processInstanceId != null">
                and a.process_instance_id = #{processInstanceId}
            </if>

        </where>
    </select>

</mapper>
