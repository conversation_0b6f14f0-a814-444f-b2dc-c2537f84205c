<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.bmsExecute.mapper.read.BmsExecuteBudgetDataReadMapper">

    <select id="queryList" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetDataVo">
        select x.* from (
        SELECT
        m.budget_data_id as budgetDataId,
        m.flow_detail_code as flowDetailCode,
        m.budget_code as budgetCode,
        m.chk as chk,
        c.budget_name as budgetName,
        c.unit as unit,
        c.cont as cont,
        c.budget_type_code as budgetTypeCode,
        m.budget_amount as budgetAmount,
        m.org_id as orgId,
        d.org_name as orgName,
        m.budget_task_code as budgetTaskCode,
        m.hospital_id as hospitalId,
        m.execute_dept as executeDept,
        f.org_name as centralizedDeptName,
        e.flow_detail_name as flowDetailName,
        e.organization_order as organizationOrder,
        g.cal as cal,
        g.formula as formula,
        g.formula_label as formulaLabel,
        h.budget_statistics_lv2_code as budgetStatisticsLv2Code,
        h.budget_statistics_lv2_name as budgetStatisticsLv2Name,
        h.budget_statistics_lv1_code as budgetStatisticsLv1Code,
        h.budget_statistics_lv1_name as budgetStatisticsLv1Name,
        h.budget_order as budgetOrder
        FROM bms_budget_data m
        inner join bms_budget_task b on m.budget_task_code = b.budget_task_code
        inner join bms_budget_table_proj c on m.budget_code = c.budget_code and b.budget_table_id = c.budget_table_id
        inner join hrm_org d on m.org_id = d.org_id
        inner join bms_flow_detail e on m.flow_detail_code = e.flow_detail_code
        left join hrm_org f on c.centralized_dept = f.org_id
        left join bms_budget_allocation g
        on m.budget_code = g.budget_code and b.budget_table_id = g.budget_table_id and m.org_id = g.org_id
        left join bms_budget_statistics_cfg h on m.budget_code = h.budget_code
        <where>
            <if test="budgetTaskCode != '' and budgetTaskCode != null">
                and m.budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
            </if>
            <if test="budgetTypeCode != '' and budgetTypeCode != null">
                and c.budget_type_code = #{budgetTypeCode,jdbcType=VARCHAR}
            </if>
            <if test="flowDetailCode != '' and flowDetailCode != null">
                and m.flow_detail_code = #{flowDetailCode,jdbcType=VARCHAR}
            </if>
        </where>
        ) x
        right join ( SELECT a.budget_code,
        a.org_id
        FROM
        bms_budget_data a
        INNER JOIN bms_budget_task b ON a.budget_task_code = b.budget_task_code
        INNER JOIN bms_budget_table_proj c ON a.budget_code = c.budget_code
        AND b.budget_table_id = c.budget_table_id
        <where>
            <if test="flowDetailCode != '' and flowDetailCode != null">
                and a.flow_detail_code = #{flowDetailCode,jdbcType=VARCHAR}
            </if>
            <if test="budgetTaskCode != '' and budgetTaskCode != null">
                and a.budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
            </if>
            <if test='ptr == "1"'>
                and a.execute_dept = #{orgId,jdbcType=VARCHAR}
            </if>
            <if test='ptr == "2"'>
                and a.org_id = #{orgId,jdbcType=VARCHAR}
            </if>
            <if test='ptr == "3"'>
                and c.centralized_dept = #{orgId,jdbcType=VARCHAR}
            </if>
            <if test='curSysOrgId != "" and curSysOrgId != null and queryFlag == "1"'>
                and c.centralized_dept = #{curSysOrgId,jdbcType=VARCHAR}
            </if>
        </where>
        ) n
         on x.budgetCode = n.budget_code
        AND x.orgId = n.org_id
        where x.budgetDataId is not null
<!--        <if test="flowDetailCode != '' and flowDetailCode != null">-->
<!--            and m.flow_detail_code = #{flowDetailCode,jdbcType=VARCHAR}-->
<!--        </if>-->
        order by x.budgetOrder
    </select>


    <select id="queryListOld" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetDataVo">
        SELECT
        m.budget_data_id as budgetDataId,
        m.flow_detail_code as flowDetailCode,
        m.budget_code as budgetCode,
        m.chk as chk,
        c.budget_name as budgetName,
        c.unit as unit,
        c.cont as cont,
        m.budget_amount as budgetAmount,
        m.org_id as orgId,
        d.org_name as orgName,
        m.budget_task_code as budgetTaskCode,
        m.hospital_id as hospitalId,
        m.execute_dept as executeDept,
        f.org_name as centralizedDeptName,
        e.flow_detail_name as flowDetailName,
        e.organization_order as organizationOrder,
        g.cal as cal,
        g.formula as formula,
        g.formula_label as formulaLabel
        FROM bms_budget_data m
        inner join bms_budget_task b on m.budget_task_code = b.budget_task_code
        inner join bms_budget_table_proj c on m.budget_code = c.budget_code and b.budget_table_id = c.budget_table_id
        inner join hrm_org d on m.org_id = d.org_id
        inner join bms_flow_detail e on m.flow_detail_code = e.flow_detail_code
        left join hrm_org f on c.centralized_dept = f.org_id
        left join bms_budget_allocation g
        on m.budget_code = g.budget_code and b.budget_table_id = g.budget_table_id and m.org_id = g.org_id
        WHERE EXISTS (SELECT 1 FROM
        ( SELECT a.budget_code,
        a.org_id
        FROM
        bms_budget_data a
        INNER JOIN bms_budget_task b ON a.budget_task_code = b.budget_task_code
        INNER JOIN bms_budget_table_proj c ON a.budget_code = c.budget_code
        AND b.budget_table_id = c.budget_table_id
        <where>
            <if test="flowDetailCode != '' and flowDetailCode != null">
                and a.flow_detail_code = #{flowDetailCode,jdbcType=VARCHAR}
            </if>
            <if test="budgetTaskCode != '' and budgetTaskCode != null">
                and a.budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
            </if>
            <if test='ptr == "1"'>
                and a.execute_dept = #{orgId,jdbcType=VARCHAR}
            </if>
            <if test='ptr == "2"'>
                and a.org_id = #{orgId,jdbcType=VARCHAR}
            </if>
            <if test='ptr == "3"'>
                and c.centralized_dept = #{orgId,jdbcType=VARCHAR}
            </if>
            <if test='curSysOrgId != "" and curSysOrgId != null and queryFlag == "1"'>
                and c.centralized_dept = #{curSysOrgId,jdbcType=VARCHAR}
            </if>
        </where>
        ) n
        WHERE
        m.budget_code = n.budget_code
        AND m.org_id = n.org_id
        )
        <if test="budgetTaskCode != '' and budgetTaskCode != null">
            and m.budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
        </if>
        <if test="budgetTypeCode != '' and budgetTypeCode != null">
            and c.budget_type_code = #{budgetTypeCode,jdbcType=VARCHAR}
        </if>
        <!--        <if test="flowDetailCode != '' and flowDetailCode != null">-->
        <!--            and m.flow_detail_code = #{flowDetailCode,jdbcType=VARCHAR}-->
        <!--        </if>-->
        order by m.org_id
    </select>

    <select id="queryOrg" resultType="com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo">
       select distinct x.org_id as orgId
       from
       (select * from
            (WITH RECURSIVE temp AS
                    (SELECT *
                     FROM hrm_org r
                     where
                         org_id in
                        <foreach collection="deptQuery" separator="," item="item" open="(" close=")">
                            #{item,jdbcType=VARCHAR}
                        </foreach>
                     UNION
                         all SELECT b.*
                     FROM hrm_org b, temp t
                     WHERE b.org_parent_id = t.org_id )
             SELECT *
             FROM temp)z
            ) x
         where x.active_flag = '1'
    </select>

    <select id="queryAllAmount" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetDataVo">
        select x.* from
        (SELECT
            a.budget_code as budgetCode,
            a.org_id as orgId,
            a.budget_amount as budgetAmount,
            a.budget_data_id,
            max(a.budget_data_id) over(partition by a.org_id, a.budget_code) as maxId
        FROM
        bms_budget_data a
        INNER JOIN bms_budget_task b ON a.budget_task_code = b.budget_task_code
        INNER JOIN bms_budget_table_proj c ON a.budget_code = c.budget_code
        AND b.budget_table_id = c.budget_table_id
        <where>
            <if test="flowDetailCode != '' and flowDetailCode != null">
                and a.flow_detail_code = #{flowDetailCode,jdbcType=VARCHAR}
            </if>
            <if test="budgetTaskCode != '' and budgetTaskCode != null">
                and a.budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
            </if>
        </where>
        ) x
        where x.budget_data_id = x.maxId
    </select>

    <select id="queryDataDetail" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetDataVo">
        select
            a.budget_data_id as budgetDataId,
            a.flow_detail_code as flowDetailCode,
            a.budget_code as budgetCode,
            d.budget_name as budgetName,
            a.budget_amount as budgetAmount,
            a.org_id as orgId,
            b.org_name as orgName,
            a.budget_task_code as budgetTaskCode,
            a.hospital_id as hospitalId,
            a.execute_dept as executeDept,
            a.chk as chk
        from bms_budget_data a
        left join hrm_org b
        on a.org_id = b.org_id
        INNER JOIN bms_budget_task c ON a.budget_task_code = c.budget_task_code
        inner join bms_budget_table_proj d
        on a.budget_code = d.budget_code AND c.budget_table_id = d.budget_table_id
        <where>
            <if test="flowDetailCode != '' and flowDetailCode != null">
                and a.flow_detail_code = #{flowDetailCode,jdbcType=VARCHAR}
            </if>
            <if test="budgetTaskCode != '' and budgetTaskCode != null">
                and a.budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!--  查询编制项目排序规则  -->
    <select id="queryBudgetOrder" resultType="java.util.Map">
        select
            budget_code,
            budget_order
        from
            bms_budget_statistics_cfg
        where
            budget_order is not null
        group by
            budget_code,
            budget_order
    </select>

</mapper>
