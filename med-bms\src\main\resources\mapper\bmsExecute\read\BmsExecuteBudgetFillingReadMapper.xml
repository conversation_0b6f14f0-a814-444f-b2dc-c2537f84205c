<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.bmsExecute.mapper.read.BmsExecuteBudgetFillingReadMapper">

    <!--查询预算填报情况-->
    <select id="queryList" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetFillingVo">
        select a.budget_filling_id  as budgetFillingId,
               a.flow_detail_code   as flowDetailCode,
               a.budget_task_code   as budgetTaskCode,
               b.organization_order as organizationOrder,
               a.org_id             as orgId,
               a.status             as status,
               b.flow_detail_name   as flowDetailName,
               b.ptr                as ptr,
               c.hospital_id        as hospitalId,
               c.budget_task_name   as budgetTaskName,
               d.org_name           as orgName
        from bms_budget_filling a
                 inner join bms_flow_detail b
                            on a.flow_detail_code = b.flow_detail_code
                 inner join bms_budget_task c
                            on a.budget_task_code = c.budget_task_code
                 left join hrm_org d
                           on a.org_id = d.org_id
        where a.status is not null
        and a.budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
        <if test="budgetFillingId != '' and budgetFillingId != null">
            and a.budget_filling_id = #{budgetFillingId,jdbcType=INTEGER}
        </if>
        <if test="status != '' and status != null">
            and a.status = #{status,jdbcType=VARCHAR}
        </if>
        <if test="curSysOrgId != '' and curSysOrgId != null">
            and a.org_id = #{curSysOrgId,jdbcType=VARCHAR}
        </if>
        <if test="orgId != '' and orgId != null">
            and a.org_id = #{orgId,jdbcType=VARCHAR}
        </if>
        <if test="flowDetailCode != '' and flowDetailCode != null">
            and a.flow_detail_code = #{flowDetailCode,jdbcType=INTEGER}
        </if>
        order by b.flow_detail_id, b.organization_order
    </select>

    <!--查询编制执行情况-->
    <select id="queryImplementation"
            resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetFillingVo">
        SELECT budget_filling_id,
               flow_detail_code,
               org_id,
               status
        FROM bms_budget_filling
        WHERE flow_detail_code = #{flowDetailCode,jdbcType=VARCHAR}
          and budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
            and status != '3'
    </select>

</mapper>
