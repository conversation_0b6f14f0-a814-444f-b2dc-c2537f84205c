<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.bmsExecute.mapper.read.BmsExecuteBudgetResultsReadMapper">

    <select id="queryList" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetResultsVo">
        select x.*,
               y.budget_name as budgetName,
               y.unit
        from
        (select
            a.budget_code as budgetCode,
            a.budget_amount as budgetAmount,
            a.org_id as orgId,
            b.org_name as orgName,
            c.budget_table_id,
            c.hospital_id as hospitalId
        from bms_budget_results a
        inner join bms_org b
        on a.org_id = b.org_id
        inner join bms_budget_flow c
        on a.budget_flow_id = c.budget_flow_id
        where
        c.hospital_id = #{hospitalId}
        <if test="budgetFlowId != '' and budgetFlowId != null">
            and c.budget_flow_id = #{budgetFlowId,jdbcType=INTEGER}
        </if>
        <if test="orgId != '' and orgId != null">
            and a.org_id = #{orgId}
        </if>
        ) x left join bms_budget_table_proj y on x.budgetCode = y.budget_code and x.budget_table_id = y.budget_table_id
    </select>


    <select id="queryListNew" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetResultsVo">
        select x.*,
        y.budget_name as budgetName,
        y.unit,
        (case when y.budget_parent_id is not null and  y.budget_parent_id !='' then y.budget_parent_id else y.budget_code end) as budgetParentId
        from
        (select
        a.budget_results_id as budgetResultsId,
        a.budget_code as budgetCode,
        a.budget_amount as budgetAmount,
        a.org_id as orgId,
        b.org_name as orgName,
        c.budget_table_id,
        c.hospital_id as hospitalId
        from bms_budget_results a
        inner join hrm_org b
        on a.org_id = b.org_id
        inner join bms_budget_task c
        on a.budget_task_code = c.budget_task_code
        where
        c.hospital_id = #{hospitalId}
        <if test="budgetTakeCode != '' and budgetTakeCode != null">
            and c.budget_task_code = #{budgetTakeCode}
        </if>
        <if test="budgetCodes != null and budgetCodes.size() != 0">
            and a.budget_code in
                <foreach collection="budgetCodes" open="(" close=")" separator="," item="tid">
                    #{tid}
                </foreach>
        </if>
        ) x left join bms_budget_table_proj y on x.budgetCode = y.budget_code and x.budget_table_id = y.budget_table_id
        <where>
            <if test="budgetName != '' and budgetName != null">
                and y.budget_name like CONCAT( '%', #{budgetName},'%')
            </if>
            <if test="budgetTypeCode != '' and budgetTypeCode != null">
                and y.budget_type_code like CONCAT( '%', #{budgetTypeCode},'%')
            </if>
            <if test="budgetCode != '' and budgetCode != null">
                and y.budget_code like CONCAT( '%',#{budgetCode},'%')
            </if>
            <if test="deptCode != '' and deptCode != null">
                and x.orgid = #{deptCode}
            </if>
        </where>
    </select>

    <!-- 查询科室预算 -->
    <select id="queryDeptBudget"
            resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetResultsVo">
        WITH RECURSIVE budget_path AS (
            SELECT
                b.org_id AS target_org_id,
                b.org_id,
                b.org_name,
                b.org_parent_id,
                A.budget_code,
                A.budget_amount,
                0 AS depth
            FROM hrm_org b
            LEFT JOIN (
                SELECT c.org_id, c.budget_code, c.budget_amount
                FROM bms_budget_task a
                JOIN bms_budget_results c ON a.budget_task_code = c.budget_task_code
                WHERE a.year = #{year,jdbcType=VARCHAR}
                <if test="budgetCode != null and budgetCode != ''">
                    and c.budget_code = #{budgetCode,jdbcType=VARCHAR}
                </if>
                <if test="budgetCodes != null and budgetCodes.size() > 0">
                    AND c.budget_code IN
                    <foreach collection="budgetCodes" open="(" close=")" separator="," item="code">
                        #{code}
                    </foreach>
                </if>
            ) A ON b.org_id = A.org_id
            <where>
                <if test="orgIds != null and orgIds.size() > 0">
                    AND b.org_id IN
                    <foreach collection="orgIds" open="(" close=")" separator="," item="tid">
                        #{tid}
                    </foreach>
                </if>
            </where>

            UNION ALL

            SELECT
                child.target_org_id,
                parent.org_id,
                parent.org_name,
                parent.org_parent_id,
                parent_a.budget_code,
                parent_a.budget_amount,
                child.depth + 1
            FROM budget_path child
            JOIN hrm_org parent ON child.org_parent_id = parent.org_id
            LEFT JOIN (
                SELECT c.org_id, c.budget_code, c.budget_amount
                FROM bms_budget_task a
                JOIN bms_budget_results c ON a.budget_task_code = c.budget_task_code
                WHERE a.year = #{year,jdbcType=VARCHAR}
                <if test="budgetCode != null and budgetCode != ''">
                    and c.budget_code = #{budgetCode,jdbcType=VARCHAR}
                </if>
                <if test="budgetCodes != null and budgetCodes.size() > 0">
                    AND c.budget_code IN
                    <foreach collection="budgetCodes" open="(" close=")" separator="," item="code">
                        #{code}
                    </foreach>
                </if>
            ) parent_a ON parent.org_id = parent_a.org_id
            AND parent_a.budget_code = child.budget_code  -- 保持递归中预算代码一致
            WHERE child.budget_amount IS NULL
        )
        SELECT DISTINCT ON (target_org_id, budget_code)
            target_org_id AS orgId,
            org_id AS upOrgId,
            org_name AS orgName,
            org_parent_id,
            budget_code AS budgetCode,
            budget_amount AS budgetAmount,
            'zjxrmyy' AS hospitalId
        FROM budget_path
        WHERE budget_amount IS NOT NULL
        ORDER BY target_org_id, budget_code, depth
    </select>

    <select id="queryDeptBudget2" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetResultsVo">
        SELECT
            C.org_id AS orgId,
            C.budget_code AS budgetCode,
            C.budget_amount AS budgetAmount,
            A.hospital_id AS hospitalId,
            b.org_name AS orgName,
            'zjxrmyy' as hospitalId
        FROM
            bms_budget_task
                A INNER JOIN bms_budget_results C ON A.budget_task_code = C.budget_task_code
                  INNER JOIN hrm_org b ON C.org_id = b.org_id
        WHERE
            A.YEAR = #{year,jdbcType=VARCHAR}
          AND C.budget_code IN
        <foreach collection="budgetCodes" item="budgetCode" open="(" separator="," close=")">
            #{budgetCode,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!-- 查询： 预算执行-预算执行报表 -->
    <select id="queryBudgetReport" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetResultsVo">
        select m.org_id,
               m.org_name,
               m.budget_code,
               m.budget_amount,
               m.budget_name,
               COALESCE(n.actig_amt, 0.00) as actig_amt
        from (select b.org_id, d.org_name, b.budget_code, b.budget_amount, c.budget_name
              from bms_budget_task a,
                   bms_budget_results b,
                   bms_budget_table_proj c,
                   hrm_org d
              where a.budget_task_code = b.budget_task_code
                and a.budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
                and a.active_flag = '1'
                and a.budget_table_id = c.budget_table_id
                and b.budget_code = c.budget_code
                and b.org_id = d.org_id
                and c.budget_type_code = #{budgetTypeCode,jdbcType=VARCHAR}
                and c.econ_sub = '1') m
                 left join (
                            select b.dept_code, b.econ_sub_code,c.budget_code, sum(b.actig_amt) as actig_amt
                            from bms_budget_task a,
                            ecs_reim_asst_detail b,
                            ecs_reim_item_to_budg_cfg c
                            where
                            b.dept_code is not null and b.dept_code !=''
                            and a.active_flag = '1'
                            and a.budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
                            and b.actig_amt_type = '1'
                            and SUBSTRING(b.create_time, 1, 4) = a.year
                            and b.econ_sub_code = c.reim_item_code
                            and c."year" = a."year"
                            group by b.dept_code, b.econ_sub_code,c.budget_code
                            ) n
                           on m.org_id = n.dept_code
                           and m.budget_code = n.budget_code
    </select>

    <!-- 查询：预算执行-预算执行报表表头 -->
    <select id="queryTitle" resultType="com.jp.med.bms.modules.dispose.vo.TitleVo">
        select distinct a.budget_code as key,
            b.budget_name as title,
            true as summary,
            'default' as sorter,
            '150' as width
        from bms_budget_results a,
            bms_budget_table_proj b,
            bms_budget_task c
        where c.budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
          and c.active_flag = '1'
          and c.budget_table_id = b.budget_table_id
          and b.budget_code = a.budget_code
          AND b.budget_type_code =  #{budgetTypeCode,jdbcType=VARCHAR}
        group by a.budget_code, b.budget_name
    </select>

    <select id="queryBudgetSummary" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetResultsVo">
        SELECT
            A.budget_code,
            b.budget_name,
            CASE
                WHEN c.org_name IS NOT NULL THEN c.org_name
                ELSE A.centralized_dept
                END AS centralizedDeptName,
            COALESCE(ROUND(SUM(A.budget_amount), 2), 0) AS actigAmt
        FROM
            bms_budget_results A
                LEFT JOIN (
                SELECT
                    A.budget_task_code,
                    b.budget_code,
                    b.budget_name
                FROM
                    bms_budget_task A,
                    bms_budget_proj b
                WHERE
                    A."year" = b.budget_year
            ) b ON A.budget_task_code = b.budget_task_code
                AND A.budget_code = b.budget_code
                LEFT JOIN hrm_org C ON A.centralized_dept = C.org_id
        WHERE
            a.budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
          AND a.hospital_id = 'zjxrmyy'
        GROUP BY
            A.budget_code,
            b.budget_name,
            A.centralized_dept,
            C.org_name
    </select>

    <select id="queryBudgetResList" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetResultsVo">
        select
            a.budget_code,
            a.budget_amount,
            a.org_id,
            a.centralized_dept,
            a.budget_task_code,
            b.org_name
        from bms_budget_results a left join hrm_org b on a.org_id = b.org_id
        where budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
          and budget_code in 
        <foreach collection="budgetCodes" item="budgetCode" open="(" separator="," close=")">
            #{budgetCode,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!-- 查询进修经费(JXJF)剩余预算 -->
    <select id="queryJXJFRemainingBudget" resultType="com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetResultsVo">
        SELECT 
            r.org_id AS orgId,
            o.org_name AS orgName,
            r.budget_code AS budgetCode,
            p.budget_name AS budgetName,
            r.budget_amount AS budgetAmount,
            COALESCE(SUM(a.actig_amt), 0) AS actigAmt,
            (r.budget_amount - COALESCE(SUM(a.actig_amt), 0)) AS remainingBudget,
            t.hospital_id AS hospitalId
        FROM 
            bms_budget_results r
        JOIN 
            hrm_org o ON r.org_id = o.org_id
        JOIN 
            bms_budget_task t ON r.budget_task_code = t.budget_task_code
        LEFT JOIN 
            bms_budget_table_proj p ON r.budget_code = p.budget_code AND t.budget_table_id = p.budget_table_id
        LEFT JOIN 
            ecs_reim_asst_detail a ON r.org_id = a.dept_code 
            AND a.actig_amt_type = '1'
            AND SUBSTRING(a.create_time, 1, 4) = t.year
            AND EXISTS (
                SELECT 1 FROM ecs_reim_item_to_budg_cfg c 
                WHERE a.econ_sub_code = c.reim_item_code 
                AND c.budget_code = r.budget_code
                AND c.year = t.year
            )
        WHERE 
            t.year = #{year,jdbcType=VARCHAR}
            AND r.budget_code = 'JXJF'  <!-- 固定查询进修经费预算代码 -->
            <if test="orgIds != null and orgIds.size() > 0">
                AND r.org_id IN
                <foreach collection="orgIds" open="(" close=")" separator="," item="tid">
                    #{tid}
                </foreach>
            </if>
        GROUP BY 
            r.org_id, o.org_name, r.budget_code, p.budget_name, r.budget_amount, t.hospital_id
        ORDER BY 
            r.org_id
    </select>
</mapper>
