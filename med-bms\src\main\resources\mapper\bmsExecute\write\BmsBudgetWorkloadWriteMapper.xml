<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.bmsExecute.mapper.write.BmsBudgetWorkloadWriteMapper">

    <insert id="insertWorkLoad">
        INSERT INTO bms_budget_workload(
            budget_task_code,
            month,
            budget_code,
            budget_name,
            budget_type_code,
            actual_amt,
            dept_code
        ) VALUES (
            #{budgetTaskCode,jdbcType=VARCHAR},
            #{month,jdbcType=VARCHAR},
            #{budgetCode,jdbcType=VARCHAR},
            #{budgetName,jdbcType=VARCHAR},
            #{budgetTypeCode,jdbcType=VARCHAR},
            #{actualAmt,jdbcType=DOUBLE},
            #{deptCode,jdbcType=VARCHAR}
        )
    </insert>

    <delete id="deleteWorkLoads">
         DELETE from bms_budget_workload
         where budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
           and month = substring(#{uploadMonth,jdbcType=VARCHAR},6,2)
           and budget_type_code = #{budgetTypeCode,jdbcType=VARCHAR}
    </delete>
</mapper>
