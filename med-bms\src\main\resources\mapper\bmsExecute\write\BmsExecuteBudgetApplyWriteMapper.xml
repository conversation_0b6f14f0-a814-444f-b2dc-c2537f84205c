<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.bmsExecute.mapper.write.BmsExecuteBudgetApplyWriteMapper">

    <update id="updateAdjustResult">
        update bms_budget_results a
        set budget_amount = (select budget_amount from bms_budget_adjust b where a.budget_results_id = b.budget_results_id and b.budget_apply_id = #{budgetApplyId})
        where a.budget_results_id
        in (select budget_results_id from bms_budget_adjust where budget_apply_id = #{budgetApplyId} )
    </update>
</mapper>
