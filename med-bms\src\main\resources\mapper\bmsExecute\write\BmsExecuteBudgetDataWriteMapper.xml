<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.bmsExecute.mapper.write.BmsExecuteBudgetDataWriteMapper">
    <insert id="insertNextNode">
        insert into bms_budget_data(
            flow_detail_code,
            budget_code,
            budget_amount,
            org_id,
            budget_task_code,
            hospital_id,
            execute_dept
        )
        select #{flowDetailCodeNext,jdbcType=VARCHAR},
               budget_code,
               budget_amount,
               org_id,
               budget_task_code,
               hospital_id,
               execute_dept
        from bms_budget_data
        where budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR} and flow_detail_code = #{flowDetailCode,jdbcType=VARCHAR}
    </insert>

    <update id="updateData">
        UPDATE bms_budget_data
        SET budget_amount = COALESCE(#{budgetAmount,jdbcType=INTEGER}, 0)
        WHERE budget_data_id = #{budgetDataId};
    </update>

    <update id="updateFillingType">
        UPDATE bms_budget_filling
        SET status = #{status},
            username = #{username}
        WHERE budget_filling_id = #{budgetFillingId}
    </update>
</mapper>
