<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.bmsExecute.mapper.write.apper">
     <insert id="insertResult">
         /*insert into bms_budget_results
         select
             nextval('bms_budget_results_budget_results_id_seq'::regclass),
             b.budget_code,
             a.budget_amount,
             a.org_id,
             b.centralized_dept,
             a.budget_flow_id,
             a.hospital_id
         from bms_budget_data a
            left join bms_budget_table_proj b
            on a.budget_proj_id = b.budget_proj_id and a.budget_table_id = b.budget_table_id
         where a.budget_flow_id = #{budgetFlowId,jdbcType=INTEGER}*/

         INSERT INTO bms_budget_results
         SELECT
            nextval( 'bms_budget_results_budget_results_id_seq' :: regclass ),
            b.budget_code,
            A.budget_amount,
            A.org_id,
            b.centralized_dept,
            A.budget_task_code,
            A.hospital_id
         FROM
             bms_budget_data A
         LEFT JOIN bms_budget_task C ON A.budget_task_code = C.budget_task_code
         LEFT JOIN bms_budget_table_proj b ON C.budget_table_id = b.budget_table_id
         WHERE
             A.flow_detail_code = #{flowDetailCode,jdbcType=VARCHAR}
           AND A.budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR}
           AND A.budget_code = b.budget_code
     </insert>
</mapper>
