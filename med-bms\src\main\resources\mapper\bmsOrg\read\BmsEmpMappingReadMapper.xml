<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.bmsOrg.mapper.read.BmsEmpMappingReadMapper">

    <select id="queryList" resultType="com.jp.med.bms.modules.bmsOrg.vo.BmsEmpMappingVo">
        select
            a.id as id,
            a.emp_code as empCode,
            a.hrm_org_id as hrmOrgId,
            b.org_name as hrmOrgName,
            a.bms_org_id as bmsOrgId,
            c.org_name as bmsOrgName,
            a.hospital_id as hospitalId
        from bms_emp_mapping a
        left join hrm_org b on a.hrm_org_id = b.org_id
        left join hrm_org c on a.bms_org_id = c.org_id
    </select>

</mapper>
