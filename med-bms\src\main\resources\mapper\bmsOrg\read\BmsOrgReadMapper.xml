<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.bmsOrg.mapper.read.BmsOrgReadMapper">

    <select id="queryList" resultType="com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo">
        SELECT DISTINCT
            c.org_id AS orgId,
            c.org_name AS orgName,
            c.org_parent_id AS orgParentId,
            c.hospital_id AS hospitalId,
            c.dept_type AS deptType,
            d.org_name AS orgParentName
            FROM
            <choose>
                <when test="queryOrgName != null and queryOrgName != ''">
                    (
                    SELECT h.*
                    FROM (
                    WITH RECURSIVE temp AS
                    (SELECT *
                    FROM bms_org r
                    <where>
                        <if test="queryOrgName != null and queryOrgName != ''">
                            AND org_name LIKE CONCAT('%',#{queryOrgName},'%')
                        </if>
                    </where>
                    UNION
                    ALL SELECT b.*
                    FROM bms_org b, temp t
                    WHERE b.org_id = t.org_parent_id )
                    SELECT *
                    FROM temp ) h
                    UNION ALL
                    SELECT e.*
                    FROM (
                    WITH RECURSIVE temp AS
                    (SELECT *
                    FROM bms_org r
                    <where>
                        <if test="queryOrgName != null and queryOrgName != ''">
                            AND org_name LIKE CONCAT('%',#{queryOrgName},'%')
                        </if>
                    </where>
                    UNION
                    ALL SELECT b.*
                    FROM bms_org b, temp t
                    WHERE b.org_parent_id = t.org_id )
                    SELECT *
                    FROM temp ) e
                    ) c
                </when>
                <otherwise> bms_org c</otherwise>
            </choose>
        LEFT JOIN bms_org d ON c.org_parent_id = d.org_id
        <where>
            <if test="orgId != '' and orgId != null">
                and c.org_id = #{orgId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryOrgTree" resultType="com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo">
        SELECT
        x.org_id as orgId, <!--组织ID-->
        x.org_parent_id as 	orgParentId, <!--父组织id-->
        x.org_name as orgName, <!---->
        x.hospital_id as hospitalId <!---->
        <if test="orgId != '' and orgId != null">
            ,case when y.org_id is not null then true else false end as disabled
        </if>
        from bms_org x
        <if test="orgId != '' and orgId != null">
            left join
            (SELECT
            a.*
            from (
            with RECURSIVE temp AS (
            select * from bms_org r
            where org_id = #{orgId}
            UNION ALL
            SELECT b.* from bms_org b, temp t where b.org_parent_id = t.org_id
            )
            select * from temp
            ) a) y
            on x.org_id = y.org_id
        </if>
    </select>

    <!-- 查询组织架构用户 -->
    <select id="queryOrgUser" resultType="com.jp.med.common.entity.emp.EmpEmployeeInfoEntity">
        select a.emp_code AS empCode,
               a.hospital_id AS hospitalId,
               a.org_id AS orgId,
               c.org_name as orgName
        from bms_user a
        inner join bms_org c
        on a.org_id = c.org_id
    </select>

    <!-- 通过员工编号查询组织信息 -->
    <select id="queryOrgByEmpCode" resultType="com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo">
        select a.emp_code AS empCode,
               a.hrm_org_id AS hrmOrgId,
               a.bms_org_id AS bmsOrgId,
               a.hospital_id AS hospitalId
        from bms_emp_mapping a
        where a.emp_code = #{empCode,jdbcType=VARCHAR}
    </select>

    <!--查询可做预算的科室-->
    <select id="queryDept" resultType="com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo">
        SELECT
            x.org_id as orgId, <!--组织ID-->
            x.org_parent_id as 	orgParentId, <!--父组织id-->
            x.org_name as orgName,
            x.hospital_id as hospitalId,
            x.dept_type as deptType
        from bms_org x
        where dept_type is not null
        <if test="orgId != '' and orgId != null">
           and x.org_parent_id = #{orgId}
        </if>
        <if test="deptType != '' and deptType != null">
            and x.dept_type = #{deptType}
        </if>
    </select>

    <select id="queryUserOption" resultType="com.jp.med.common.vo.SelectOptionVo">
        select b.emp_code AS value,
               b.emp_name AS label,
               b.hospital_id AS hospitalId
        from hrm_employee_info b
            inner join hrm_org c
            on b.org_id = c.org_id
        <where>
            <if test="userInfo != '' and userInfo != null">
                and (b.emp_code like concat('%', #{userInfo,jdbcType=VARCHAR}, '%') or b.emp_name like concat('%', #{userInfo,jdbcType=VARCHAR}, '%'))
            </if>
        </where>
    </select>

    <select id="queryAllDept" resultType="com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo">
        select
            org_id as orgId,
            org_id as value,
            org_parent_id as orgParentId,
            org_name as orgName,
            org_name as label,
            hospital_id as hospitalId
        from
        hrm_org
        where active_flag = '1'
    </select>

    <select id="queryParentOrg" resultType="com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo">
        select
            org_id as orgId,
            org_id as value,
            org_parent_id as orgParentId,
            org_name as orgName,
            org_name as label,
            hospital_id as hospitalId
        from
            hrm_org a where EXISTS (select 1 from hrm_org b where a.org_id = b.org_parent_id)
    </select>


    <select id="queryOrgOnly" resultType="com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo">
        select org_id as orgId,
               hospital_id as hospitalId
        from bms_org
        where org_id = #{orgId,jdbcType=VARCHAR}
    </select>


</mapper>
