<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.bmsOrg.mapper.write.BmsOrgWriteMapper">

    <!-- 保存组织架构用户 -->
    <insert id="saveOrgUser">
        INSERT INTO bms_user(emp_code, org_id, hospital_id)
        SELECT *
        FROM (
            <foreach collection="empCodes" item="empCode" separator="UNION ALL">
                SELECT #{empCode}, #{orgId}, #{hospitalId}
            </foreach>
        ) a
    </insert>
    <update id="updateOrg">
        update bms_org
        <set>
            <if test="orgId != null and orgId != ''">
                org_id = #{orgId,jdbcType=VARCHAR},
            </if>
            <if test="orgParentId != null and orgParentId != ''">
                org_parent_id = #{orgParentId,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null and orgName != ''">
                org_name = #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                hospital_id = #{hospitalId,jdbcType=VARCHAR},
            </if>
            <if test="deptType != null and deptType != ''">
                dept_type = #{deptType,jdbcType=VARCHAR}
            </if>
        </set>
        where org_id = #{orgId}
    </update>


    <update id="saveOrg">
        insert into bms_org (org_id,
                             org_parent_id,
                             org_name,
                             hospital_id,
                             dept_type)
        values (#{orgId},
                #{orgParentId},
                #{orgName},
                #{hospitalId},
                #{deptType}
                )
    </update>

    <delete id="deleteBmsOrg">
        delete
        from bms_org
        where org_id in (SELECT a.org_id
                         from (with RECURSIVE temp AS (select *
                                                       from bms_org r
                                                       where org_id = #{orgId}
                                                       UNION ALL
                                                       SELECT b.*
                                                       from bms_org b,
                                                            temp t
                                                       where b.org_parent_id = t.org_id)
                               select *
                               from temp) a)
    </delete>

    <!--通过ID删除组织架构-->
    <delete id="deleteBmsOrgById">
        delete from bms_org where org_id = #{orgId,jdbcType=VARCHAR}
    </delete>

    <!-- 删除组织架构用户 -->
    <delete id="deleteOrgUser">
        delete from bms_user where emp_code in
        <foreach collection="oriEmpCodes" separator="," open="(" close=")" item="code">
            #{code}
        </foreach>
    </delete>

</mapper>
