<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.config.mapper.read.BmsBudgetDeptMappingReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.bms.modules.config.vo.BmsBudgetDeptMappingVo" id="budgetDeptMappingMap">
        <result property="id" column="id"/>
        <result property="sourceDept" column="source_dept"/>
        <result property="targetDept" column="target_dept"/>
        <result property="year" column="year"/>
        <result property="flag" column="flag"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
        <result property="deptType" column="dept_type"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.bms.modules.config.vo.BmsBudgetDeptMappingVo">
        select
            a.id as id,
            a.source_dept as sourceDept,
			b.org_name as sourceDeptName,
            a.target_dept as targetDept,
			c.org_name as targetDeptName,
            a.year as year,
            a.flag as flag,
            a.crter as crter,
            a.create_time as createTime,
            a.dept_type as deptType,
            a.source_bgt_code as sourceBgtCode,
            a.target_bgt_code as targetBgtCode
        from bms_budget_dept_mapping a
        left join  hrm_org b on source_dept = b.org_id
		left join  hrm_org c on target_dept = c.org_id
        <where>
            <if test="year != null and year != ''">
                and year = #{year,jdbcType=VARCHAR}
            </if>
            <if test="flag != null and flag != ''">
                and flag =#{flag,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>
