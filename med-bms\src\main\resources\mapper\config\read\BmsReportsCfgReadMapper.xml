<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.config.mapper.read.BmsReportsCfgReadMapper">

    <select id="queryList" resultType="com.jp.med.bms.modules.config.vo.BmsReportsCfgVo">
        select
            id as id,
            reports_name as reportsName,
            col_title_code as colTitleCode,
            col_title as colTitle,
            col_val as colVal,
            cal as cal,
            creat_time as creatTime,
            active_flag as activeFlag,
            hospital_id as hospitalId
        from bms_reports_cfg
    </select>

</mapper>
