<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetAllocationReadMapper">

    <select id="queryList" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetAllocationVo">
        select a.budget_proj_id as key,
            a.budget_name AS budgetName,
            a.budget_code AS budgetCode,
            a.budget_table_id AS budgetTableId,
            a.budget_parent_id AS budgetParentId,
            b.org_id AS orgId,
            b.org_name AS centralizedDept,
            d.flag AS flag,
            c.ct as deptCount
        from
        <choose>
            <when test="budgetName != '' and budgetName != null">
                (select distinct * from (select
                    x.*
                    from
                    (with RECURSIVE temp AS (
                    select * from bms_budget_table_proj r
                    where budget_name = #{budgetName}
                    and r.budget_table_id = #{budgetTableId,jdbcType=INTEGER}
                    UNION ALL
                    SELECT b.* from bms_budget_table_proj b, temp t where b.budget_parent_id = t.budget_code
                    and b.budget_table_id = #{budgetTableId,jdbcType=INTEGER}
                    )
                    select * from temp) x
                    union all
                    select
                    y.* from
                    (with RECURSIVE temp AS (
                    select * from bms_budget_table_proj r
                    where budget_name = #{budgetName}
                    and r.budget_table_id = #{budgetTableId,jdbcType=INTEGER}
                    UNION ALL
                    SELECT b.* from bms_budget_table_proj b, temp t where b.budget_code = t.budget_parent_id
                    and b.budget_table_id = #{budgetTableId,jdbcType=INTEGER}
                    )
                    select * from temp) y) xz) a
            </when>
            <when test="orgId != '' and orgId != null">
                (select distinct * from (select
                x.*
                from
                (with RECURSIVE temp AS (
                select * from bms_budget_table_proj r
                where centralized_dept =  #{orgId,jdbcType=VARCHAR}
                and r.budget_table_id = #{budgetTableId,jdbcType=INTEGER}
                UNION ALL
                SELECT b.* from bms_budget_table_proj b, temp t where b.budget_parent_id = t.budget_code
                and b.budget_table_id = #{budgetTableId,jdbcType=INTEGER}
                )
                select * from temp) x
                union all
                select
                y.* from
                (with RECURSIVE temp AS (
                select * from bms_budget_table_proj r
                where centralized_dept =  #{orgId,jdbcType=VARCHAR}
                and r.budget_table_id = #{budgetTableId,jdbcType=INTEGER}
                UNION ALL
                SELECT b.* from bms_budget_table_proj b, temp t where b.budget_code = t.budget_parent_id
                and b.budget_table_id = #{budgetTableId,jdbcType=INTEGER}
                )
                select * from temp) y) xz) a
            </when>
            <otherwise>
                bms_budget_table_proj a
            </otherwise>
        </choose>
        left join hrm_org b
        on a.centralized_dept = b.org_id
            left join
            ( select budget_code, count (1) as ct
            from bms_budget_allocation
            where budget_table_id = #{budgetTableId,jdbcType=INTEGER}
            group by budget_code
            ) c on a.budget_code = c.budget_code
            left join bms_budget_table d on a.budget_table_id = d.id
        where a.budget_table_id = #{budgetTableId,jdbcType=INTEGER}
    </select>

    <select id="queryAllocation" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetAllocationVo">
        select
            a.budget_code as budgetCode,
            a.budget_table_id as budgetTableId,
            a.org_id as orgId,
            a.execute_dept as executeDept,
            b.hospital_id as hospitalId,
            c.centralized_dept as centralizedDept,
            c.budget_type_code as budgetTypeCode,
            b.flag as flag,
            a.cal as cal,
            a.formula as formula,
            a.formula_label as formulaLabel,
            d.org_name as orgName
        from
            bms_budget_allocation a
            inner join bms_budget_table b
            on a.budget_table_id = b.id
            inner join bms_budget_table_proj c
            on a.budget_code = c.budget_code and a.budget_table_id = c.budget_table_id
            left join hrm_org d on a.org_id = d.org_id
        <where>
            a.budget_table_id = #{budgetTableId}
            <if test="budgetCode != '' and budgetCode != null">
                and a.budget_code = #{budgetCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>
