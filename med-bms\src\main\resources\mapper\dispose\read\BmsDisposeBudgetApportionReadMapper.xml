<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetApportionReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetApportionVo" id="budgetApportionMap">
        <result property="id" column="id"/>
        <result property="budgetApportionName" column="budget_apportion_name"/>
        <result property="remark" column="remark"/>
        <result property="flag" column="flag"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="createtime" column="createtime"/>
        <result property="defaultFlag" column="default_flag"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetApportionVo">
        select
            id as id,
            budget_apportion_name as budgetApportionName,
            remark as remark,
            flag as flag,
            hospital_id as hospitalId,
            createtime as createtime,
            default_flag as defaultFlag
        from bms_budget_apportion
    </select>

</mapper>
