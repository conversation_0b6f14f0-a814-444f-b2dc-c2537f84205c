<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetFlowReadMapper">

    <select id="queryList" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetFlowVo">
        select
            a.budget_flow_id as budgetFlowId,
            a.budget_flow_code as budgetFlowCode,
            a.budget_flow_name as budgetFlowName,
            a.hospital_id as hospitalId,
            a.remarks as remarks,
            a.create_time as createTime,
            a.flag as flag
        from bms_budget_flow a
        <where>
            <if test="budgetFlowName != '' and budgetFlowName != null">
                and budget_flow_name like CONCAT('%',#{budgetFlowName},'%')
            </if>
            <if test="flag != '' and flag != null">
                and a.flag = #{flag,jdbcType=VARCHAR}
            </if>
            <if test="budgetFlowId != '' and budgetFlowId != null">
                and a.budget_flow_id = #{budgetFlowId,jdbcType=VARCHAR}
            </if>
        </where>
        order by a.budget_flow_id
    </select>

    <select id="queryListTask" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetFlowVo">
        select
        a.id as budgetTaskId,
        a.budget_task_code as budgetTaskCode,
        a.budget_task_name as budgetTaskName,
        a.hospital_id as hospitalId,
        a.remarks as remarks,
        a.create_time as createTime,
        a.active_flag as flag
        from bms_budget_task a
        <where>
            and a.active_flag = '1'
            <if test="budgetFlowName != '' and budgetFlowName != null">
                and budget_task_name like CONCAT('%',#{budgetFlowName},'%')
            </if>
        </where>
        order by a.id
    </select>

    <!--查询组织架构-->
    <select id="queryBmsOrg" resultType="com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo">
        select
            a.org_id AS value, <!--组织编码-->
            a.org_name AS label, <!--组织名称-->
            a.org_parent_id AS orgParentId, <!--上级组织编码-->
            a.hospital_id AS hospitalId <!--医疗机构编码-->
        from
        bms_org a
        where dept_type is null
    </select>

</mapper>