<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetProjReadMapper">

    <!--查询预算编制项-->
    <select id="queryList" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetProjVo">
        select
            DISTINCT
            a.budget_proj_id as id,
            a.budget_proj_id as budgetProjId, <!--预算编制项ID-->
            a.budget_code as budgetCode, <!--预算编制项编码-->
            a.budget_name as budgetName, <!--预算编制项名称-->
            a.budget_year as budgetYear,<!--预算年度-->
            a.budget_code as value, <!--预算编制项编码-->
            a.budget_name as label, <!--预算编制项名称-->
            a.budget_code as key, <!--预算编制项编码-->
            a.budget_name as title, <!--预算编制项名称-->
            a.budget_type_code as budgetTypeCode, <!--预算编制项类型编码-->
            c.budget_type_name as budgetTypeName, <!--预算编制类别名称-->
            a.budget_parent_id as budgetParentId, <!--上级编制项编码-->
            d.budget_name as parentBudgetName, <!--上级编制项编码-->
            a.centralized_dept as orgId, <!--归口科室-->
            a.centralized_dept as centralizedDept, <!--归口科室-->
            a.hospital_id as hospitalId, <!--医疗机构ID-->
            a.cont as cont, <!--主要内容-->
            a.dscr as dscr, <!--说明-->
            b.org_name as deptName, <!--归口科室名称-->
            a.unit as unit, <!--计量单位-->
            a.flag as flag <!--启用状态-->
        from
        <choose>
            <when test="budgetName != '' and budgetName != null">
                (select
                x.*
                from
                (with RECURSIVE temp AS (
                select * from bms_budget_proj r
                where budget_name like CONCAT('%',#{budgetName},'%')
                UNION ALL
                SELECT b.* from bms_budget_proj b, temp t where b.budget_parent_id = t.budget_code
                )
                select * from temp) x
                union all
                select
                y.* from
                (with RECURSIVE temp AS (
                select * from bms_budget_proj r
                where budget_name like CONCAT('%',#{budgetName},'%')
                UNION ALL
                SELECT b.* from bms_budget_proj b, temp t where b.budget_code = t.budget_parent_id
                )
                select * from temp) y) a
            </when>
            <otherwise>
                bms_budget_proj a
            </otherwise>
        </choose>
        left join hrm_org b
        on a.centralized_dept = b.org_id
        left join bms_budget_type c
        on a.budget_type_code = c.budget_type_code
        left join bms_budget_proj d on a.budget_parent_id = d.budget_code
        <where>
        <if test="budgetCode != '' and budgetCode != null">
            and a.budget_code = #{budgetCode,jdbcType=VARCHAR}
        </if>
        <if test="budgetYear != '' and budgetYear != null">
            and a.budget_year = #{budgetYear,jdbcType=VARCHAR}
        </if>
        <if test="flag != '' and flag != null">
            and a.flag = #{flag}
        </if>
        </where>
        order by a.budget_proj_id
    </select>
    <!--查询归口科室-->
    <select id="queryCentralizedDept" resultType="com.jp.med.bms.modules.bmsOrg.vo.BmsOrgVo">
        select
            a.org_id AS orgId, <!--组织编码-->
            a.org_name AS orgName, <!--组织名称-->
            a.org_parent_id AS orgParentId, <!--上级组织编码-->
            a.hospital_id AS hospitalId <!--医疗机构编码-->
            from
        hrm_org a
    </select>

    <!--查询上级编制项下拉树-->
    <select id="queryProjTree" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetProjVo">
        select
        a.budget_proj_id as budgetProjId, <!--预算编制项ID-->
        a.budget_code as budgetCode, <!--预算编制项编码-->
        a.budget_name as budgetName, <!--预算编制项名称-->
        a.centralized_dept as centralizedDept, <!--归口科室-->
        a.budget_type_code as budgetTypeCode,
        a.unit as unit,
        a.cont as cont,
        a.dscr as dscr,
        a.budget_parent_id as budgetParentId, <!--上级编制项编码-->
        a.hospital_id as hospitalId <!--医疗机构ID-->
        <if test="budgetCode != '' and budgetCode != null">
            ,case when b.budget_proj_id is not null then true else false end as disabled <!--是否可选-->
        </if>
        from
        bms_budget_proj a
        <if test="budgetCode != '' and budgetCode != null">
            left join
                (select
                y.* from
                (with RECURSIVE temp AS (
                select * from bms_budget_proj r
                where budget_code = #{budgetCode,jdbcType=VARCHAR}
                UNION ALL
                SELECT b.* from bms_budget_proj b, temp t where b.budget_parent_id = t.budget_code
                )
                select * from temp) y) b
            on a.budget_proj_id = b.budget_proj_id
        </if>
        <where>
            <if test="flag != '' and flag != null">
                and a.flag = #{flag}
            </if>
            <if test="budgetYear != '' and budgetYear != null">
                and a.budget_year = #{budgetYear,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!--查询所有叶子节点中的数据-->
    <select id="queryLeafProj" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetProjVo">
        select
        x.budget_proj_id as budgetProjId,
        x.budget_code as budgetCode,
        x.budget_name as budgetName,
        x.budget_code as value,
        x.budget_name as label,
        x.budget_parent_id as budgetParentId,
        x.budget_type_code as budgetTypeCode,
        x.unit as unit,
        x.flag as flag,
        x.centralized_dept as centralizedDept,
        x.hospital_id as hospitalId,
        y.org_name as orgName
        from
        (select
            a.budget_proj_id,
            a.budget_code,
            a.budget_name,
            a.budget_parent_id,
            a.budget_type_code,
            a.centralized_dept,
            a.unit,
            a.flag,
            a.hospital_id,
            a.budget_year
            from bms_budget_proj a
        where not exists (
                select 1 from bms_budget_proj b
                where a.budget_code = b.budget_parent_id
                <if test="budgetYear != '' and budgetYear != null">
                    and b.budget_year = #{budgetYear,jdbcType=VARCHAR}
                </if>
            )
        <if test="flag != null and flag != ''">
            and a.flag = #{flag}
        </if>
        ) x
        left join bms_org y
        on x.centralized_dept = y.org_id
        <where>
            <if test="budgetYear != '' and budgetYear != null">
                and x.budget_year = #{budgetYear,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <!--查询非叶子节点数据-->
    <select id="queryParentProj" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetProjVo">
        select
            x.budget_proj_id,
            x.budget_code,
            x.budget_name,
            x.budget_parent_id,
            x.budget_type_id,
            x.flag,
            x.hospital_id
            from
        (select
             a.budget_proj_id,
             a.budget_code,
             a.budget_name,
             a.budget_parent_id,
             a.budget_type_id,
             a.centralized_dept,
             a.unit,
             a.flag,
             a.hospital_id
             from bms_budget_proj a
        where exists (
                select 1 from bms_budget_proj b
                where a.budget_code = b.budget_parent_id
            )
            ) x
        left join bms_org y
        on x.centralized_dept = y.org_id
    </select>


    <select id="queryTitle" resultType="com.jp.med.bms.modules.dispose.vo.TitleVo">
        select
            x.budget_code as key,
            x.budget_name as title,
            y.budget_proj_id as order,
            'default' as sorter,
            true as summary,
            '150' as width
        from
        (WITH RECURSIVE tpath AS (
        SELECT
        budget_code,
        budget_name AS budget_name_real,
        budget_parent_id,
        "order" :: VARCHAR ( 500 ),
        budget_name :: VARCHAR ( 500 )
        FROM
        bms_budget_table_proj
        WHERE
        (budget_parent_id IS NULL
        OR budget_parent_id = '')
        AND budget_table_id = (select budget_table_id from bms_budget_task where budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR})
        UNION ALL
        SELECT
        A.budget_code,
        A.budget_name,
        A.budget_parent_id,
        ( tpath.order || '/' || A.order ) :: VARCHAR ( 500 ),
        ( tpath.budget_name || '/' || A.budget_name ) :: VARCHAR ( 500 )
        FROM
        bms_budget_table_proj A,
        tpath
        WHERE
        A.budget_parent_id = tpath.budget_code AND a.budget_table_id = (select budget_table_id from bms_budget_task where budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR})
        ) SELECT
        *
        FROM
        tpath) x
        INNER JOIN
        (select distinct c.budget_code,c.budget_proj_id from bms_budget_allocation a right join
        (
            select * from bms_budget_table_proj x
            where x.budget_table_id = (select budget_table_id from bms_budget_task where budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR})
            and not exists (select 1 from
               (select * from bms_budget_table_proj
                where budget_table_id = (select budget_table_id from bms_budget_task where budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR})
                <if test='econSub == "1"'>
                    and econ_sub = #{econSub,jdbcType=VARCHAR}
                </if>)
                y where x.budget_code = y.budget_parent_id
            )
        <if test='econSub == "1"'>
            and x.econ_sub = #{econSub,jdbcType=VARCHAR}
        </if>
        ) c
        on a.budget_table_id = c.budget_table_id and a.budget_code = c.budget_code
        <where>
            <if test='ptr == "1"'>
                and a.execute_dept = #{orgId,jdbcType=VARCHAR}
            </if>
            <if test='ptr == "2"'>
                and a.org_id = #{orgId,jdbcType=VARCHAR}
            </if>
            <if test='ptr == "3"'>
                and c.centralized_dept = #{orgId,jdbcType=VARCHAR}
            </if>
            <if test='curSysOrgId != "" and curSysOrgId != null and queryFlag == "1"'>
                and c.centralized_dept = #{curSysOrgId,jdbcType=VARCHAR}
            </if>
            <if test="budgetTypeCode != '' and budgetTypeCode != null">
                and c.budget_type_code = #{budgetTypeCode,jdbcType=VARCHAR}
            </if>
        </where>
            ) y
        on x.budget_code = y.budget_code
        order by x.order
    </select>


    <select id="queryBudgetProj" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetProjVo">
        select
            budget_code AS budgetCode,
            hospital_id AS hospitalId
        from bms_budget_proj where budget_code = #{budgetCode,jdbcType=VARCHAR}
    </select>

    <!-- 查询预算编制项 -->
    <select id="queryByYear" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetProjVo">
        select a.budget_code as budgetCode, <!--预算编制项编码-->
               a.budget_name as budgetName, <!--预算编制项名称-->
               a.budget_parent_id as budgetParentId, <!--上级编制项编码-->
               b.hospital_id as hospitalId
        from bms_budget_table_proj a
        inner join bms_budget_task b
        on a.budget_table_id = b.budget_table_id
        where b.year = #{year,jdbcType=VARCHAR}
    </select>

    <select id="queryNormalTitle" resultType="com.jp.med.bms.modules.dispose.vo.TitleVo">
        select
            a.budget_code as key,
            a.budget_name as title,
            a.budget_parent_id as parentId,
            true as summary,
            coalesce(a.order, 9999) as order,
            '150' as width from
            bms_budget_table_proj a
        where a.budget_table_id = (select budget_table_id from bms_budget_task where budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR})
        <if test='econSub == "1"'>
            and a.econ_sub = #{econSub,jdbcType=VARCHAR}
        </if>
        <if test="budgetTypeCode != null and budgetTypeCode != ''">
            and a.budget_type_code = #{budgetTypeCode,jdbcType=VARCHAR}
        </if>
        order by a.order
    </select>

    <select id="queryExcelTitle" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetProjVo">
        select budget_code as budgetCode,budget_name as budgetName from
        (WITH RECURSIVE tpath AS (
            SELECT
                budget_code,
                budget_name AS budget_name_real,
                budget_parent_id,
                budget_name :: VARCHAR ( 500 )
            FROM
                bms_budget_table_proj
            WHERE
                (budget_parent_id IS NULL
                    OR budget_parent_id = '')
              AND budget_table_id = (select budget_table_id from bms_budget_task where budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR})
            UNION ALL
            SELECT
                A.budget_code,
                A.budget_name,
                A.budget_parent_id,
                ( tpath.budget_name || '/' || A.budget_name ) :: VARCHAR ( 500 )
            FROM
                bms_budget_table_proj A,
                tpath
            WHERE
                A.budget_parent_id = tpath.budget_code AND a.budget_table_id = (select budget_table_id from bms_budget_task where budget_task_code = #{budgetTaskCode,jdbcType=VARCHAR})
        ) SELECT
            *
        FROM
            tpath) x
    </select>
</mapper>
