<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetTableProjReadMapper">

    <select id="queryParent" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTableProjVo">
        select
        a.budget_code,
        a.budget_name,
        a.budget_parent_id,
        a.budget_type_id,
        a.budget_table_id
        from bms_budget_table_proj a
        where exists (
        select 1 from bms_budget_table_proj b
        where a.budget_code = b.budget_parent_id
        )
        <if test="budgetTableId != '' and budgetTableId != null">
            and a.budget_table_id = #{budgetTableId,jdbcType=INTEGER}
        </if>
        <if test="budgetTableIds != null and budgetTableIds.size > 0">
            and a.budget_table_id in
            <foreach collection="budgetTableIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="queryList" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTableProjVo">
        select
            budget_code as budgetCode,
            budget_name as budgetName,
            budget_parent_id as budgetParentId,
            budget_type_id as budgetTypeId,
            centralized_dept as centralizedDept,
            unit as unit,
            budget_table_id as budgetTableId
        from bms_budget_table_proj
    </select>

    <select id="queryById" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTableProjVo">
        (WITH RECURSIVE tpath AS (
            SELECT
                budget_code,
                budget_name AS x,
                budget_parent_id,
                budget_name :: VARCHAR ( 500 )
            FROM
                bms_budget_table_proj
            WHERE
                (budget_parent_id IS NULL
                    OR budget_parent_id = '')
             AND budget_table_id = #{budgetTableId,jdbcType=VARCHAR}
         UNION ALL
         SELECT
             A.budget_code,
             A.budget_name,
             A.budget_parent_id,
             ( tpath.budget_name || '/' || A.budget_name ) :: VARCHAR ( 500 )
         FROM
             bms_budget_table_proj A,
             tpath
         WHERE
             A.budget_parent_id = tpath.budget_code AND a.budget_table_id = #{budgetTableId,jdbcType=VARCHAR}
        ) SELECT
            *
        FROM
        tpath)
    </select>

    <select id="queryLeafById" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTableProjVo">
        SELECT
            x.budget_proj_id AS budgetProjId,
            x.budget_code AS budgetCode,
            x.budget_name AS budgetName
        FROM
            (
                WITH RECURSIVE tpath AS (
                    SELECT
                        budget_proj_id,
                        budget_code,
                        budget_name AS budget_name_real,
                        budget_parent_id,
                        budget_name :: VARCHAR ( 500 )
                    FROM
                        bms_budget_table_proj
                    WHERE
                        ( budget_parent_id IS NULL OR budget_parent_id = '' )
                      AND budget_table_id = #{budgetTableId,jdbcType=INTEGER}
                    UNION ALL
                    SELECT
                        a.budget_proj_id,
                        A.budget_code,
                        A.budget_name,
                        A.budget_parent_id,
                        ( tpath.budget_name || '/' || A.budget_name ) :: VARCHAR ( 500 )
                    FROM
                        bms_budget_table_proj A,
                        tpath
                    WHERE
                        A.budget_parent_id = tpath.budget_code
                      AND A.budget_table_id = #{budgetTableId,jdbcType=INTEGER}
                ) SELECT
                    *
                FROM
                    tpath
            ) x
        where not EXISTS
                  ( select 1 from bms_budget_table_proj y
                    where
                        y.budget_table_id = #{budgetTableId,jdbcType=INTEGER}
                      and x.budget_code = y.budget_parent_id)
        ORDER BY x.budget_name
    </select>

</mapper>
