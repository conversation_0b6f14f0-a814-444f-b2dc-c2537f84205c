<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetTableReadMapper">

    <select id="queryList" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTableVo">
        select
            id as id,
            budget_table_name as budgetTableName,
            remark as remark,
            flag as flag,
            hospital_id as hospitalId,
            budget_year as budgetYear,
            to_char(create_time,'yyyy-mm-dd HH24:mi:ss') as createTime
        from bms_budget_table
        <where>
            <if test="budgetTableName != null and budgetTableName != ''">
                AND budget_table_name like concat('%', #{budgetTableName,jdbcType=VARCHAR} ,'%')
            </if>
            <if test="budgetYear != null and budgetYear != ''">
                AND budget_year = #{budgetYear}
            </if>
            <if test="flag != '' and flag != null">
                AND flag = #{flag}
            </if>
        </where>
    </select>

</mapper>
