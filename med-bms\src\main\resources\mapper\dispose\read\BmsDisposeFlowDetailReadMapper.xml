<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeFlowDetailReadMapper">

    <select id="queryFirstOrder" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeFlowDetailVo">
        select
            a.flow_detail_id as flowDetailId,
            a.flow_detail_name as flowDetailName,
            c.budget_filling_id as budgetFillingId,
            a.organization_order as organizationOrder,
            a.org_id as orgId,
            a.budget_flow_id as budgetFlowId,
            a.status as status,
            b.hospital_id as hospitalId
        from bms_flow_detail a
        left join bms_budget_flow b
        on a.budget_flow_id = b.budget_flow_id
        left join bms_budget_filling c
        on a.flow_detail_id = c.flow_detail_id
        where
            a.budget_flow_id = #{budgetFlowId}
          <if test="status != '' and status != null">
              and a.status = #{status,jdbcType=VARCHAR}
          </if>
          <if test="organizationOrder != '' and organizationOrder != null">
              and organization_order = #{organizationOrder,jdbcType=INTEGER}
          </if>
        order by a.organization_order
    </select>

    <select id="queryFlowDetail" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeFlowDetailVo">
        select
        a.flow_detail_id as flowDetailId,
        a.flow_detail_code as flowDetailCode,
        a.flow_detail_name as flowDetailName,
        a.ptr as ptr,
        a.exestd as exestd,
        a.abst as abst,
        a.organization_order as organizationOrder,
        a.budget_flow_code as budgetFlowCode,
        b.hospital_id as hospitalId
        from bms_flow_detail a
        left join bms_budget_flow b
        on a.budget_flow_code = b.budget_flow_code
        where
        a.budget_flow_code = #{budgetFlowCode,jdbcType=VARCHAR}
        <if test="organizationOrder != '' and organizationOrder != null">
            and organization_order = #{organizationOrder,jdbcType=INTEGER}
        </if>
        order by a.organization_order
    </select>

    <select id="queryByCode" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeFlowDetailVo">
        select  a.flow_detail_id as flowDetailId,
                a.flow_detail_code as flowDetailCode,
                a.flow_detail_name as flowDetailName,
                a.econ_sub as econSub
        from bms_flow_detail a
        where flow_detail_code = #{flowDetailCode,jdbcType=VARCHAR}
    </select>

</mapper>
