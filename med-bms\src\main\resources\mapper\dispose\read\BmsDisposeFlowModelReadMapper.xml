<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeFlowModelReadMapper">

    <select id="queryList" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeFlowModelVo">
        select
            a.flow_model_id as flowModelId,
            a.flow_mode_code as flowModeCode,
            a.flow_mode_name as flowModeName,
            a.username as username,
            to_char(a.create_time,'yyyy-mm-dd HH24:mi:ss') as createTime,
            a.model_case as modelCase,
            a.hospital_id as hospitalId,
            a.flag as flag
        from bms_flow_model a
        <where>
            <if test="flowModelId != '' and flowModelId != null">
                and a.flow_model_id = #{flowModelId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

</mapper>
