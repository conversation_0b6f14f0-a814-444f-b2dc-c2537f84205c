<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.dispose.mapper.write.BmsDisposeBudgetAllocationWriteMapper">
    <insert id="insertBySelect">
        insert into bms_budget_allocation(
            budget_code,
            org_id,
            budget_table_id,
            execute_dept,
            cal,
            formula,
            formula_label
        )
        select
            budget_code,
            org_id,
            #{budgetTableIdNew,jdbcType=INTEGER},
            execute_dept,
            cal,
            formula,
            formula_label
        from bms_budget_allocation
        where budget_table_id = #{budgetTableId,jdbcType=INTEGER}
    </insert>

    <delete id="deleteBudgetDept">
        delete from bms_budget_allocation
               where budget_table_id = #{budgetTableId}
                    and budget_code = #{budgetCode}
    </delete>

    <!--通过预算编制表删除预算分配信息-->
    <delete id="deleteByTableId">
        delete from bms_budget_allocation where budget_table_id = #{budgetTableId,jdbcType=INTEGER}
    </delete>
</mapper>
