<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.dispose.mapper.write.BmsDisposeBudgetFlowWriteMapper">
    <update id="updateBudgetFlow">
        update bms_budget_flow
        <set>
            <if test="budgetFlowName != null and budgetFlowName != ''">
                budget_flow_name = #{budgetFlowName,jdbcType=VARCHAR},
            </if>
            <if test="flag != null and flag != ''">
                flag = #{flag,jdbcType=VARCHAR},
            </if>
        </set>
        where budget_flow_code = #{budgetFlowCode,jdbcType=VARCHAR}
    </update>


    <update id="saveBudgetFlow">
        <selectKey keyProperty="budgetFlowId" resultType="Integer" order="AFTER">
            SELECT currval('bms_budget_flow_budget_flow_id_seq'::regclass)
        </selectKey>
        insert into bms_budget_flow (
            budget_flow_name,
            hospital_id,
            remarks,
            flow_data,
            flag
        )
        values(
            #{budgetFlowName},
            #{hospitalId},
            #{remarks},
            #{flowData},
            #{flag}
        )
    </update>

</mapper>