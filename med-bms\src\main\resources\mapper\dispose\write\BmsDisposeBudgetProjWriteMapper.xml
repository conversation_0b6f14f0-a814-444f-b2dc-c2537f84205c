<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.dispose.mapper.write.BmsDisposeBudgetProjWriteMapper">
    <update id="updateBudgetProj">
        update bms_budget_proj
            set
                budget_code = #{budgetCode,jdbcType=VARCHAR},
                budget_name = #{budgetName,jdbcType=VARCHAR},
                budget_parent_id = #{budgetParentId,jdbcType=VARCHAR},
                centralized_dept = #{centralizedDept,jdbcType=VARCHAR},
                budget_type_code = #{budgetTypeCode,jdbcType=VARCHAR},
                unit = #{unit,jdbcType=VARCHAR},
                flag = #{flag,jdbcType=VARCHAR},
                hospital_id = #{hospitalId,jdbcType=VARCHAR},
                cont = #{cont,jdbcType=VARCHAR},
                dscr = #{dscr,jdbcType=VARCHAR}
        where budget_proj_id = #{budgetProjId}
    </update>

    <update id="saveBudgetProj">
        insert into bms_budget_proj (
            budget_code,
            budget_name,
            budget_parent_id,
            budget_type_code,
            centralized_dept,
            unit,
            flag,
            hospital_id,
            cont,
            dscr,
            budget_year
        )
        values(
            #{budgetCode},
            #{budgetName},
            #{budgetParentId},
            #{budgetTypeCode},
            #{centralizedDept},
            #{unit},
            #{flag},
            #{hospitalId},
            #{cont},
            #{dscr},
            #{budgetYear}
        )
    </update>
    <delete id="deleteBudgetProj">
        delete from bms_budget_proj where
                budget_proj_id in (
                select a.budget_proj_id from
                    (with RECURSIVE temp AS (
                        select * from bms_budget_proj r where budget_proj_id = #{budgetProjId}
                        UNION ALL
                        SELECT b.* from bms_budget_proj b, temp t where b.budget_parent_id = t.budget_code
                    )
                     select * from temp) a
            )
    </delete>

</mapper>
