<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.dispose.mapper.write.BmsDisposeBudgetTableProjWriteMapper">

    <insert id="insertTableProjBySelect">
        insert into bms_budget_table_proj (
            budget_proj_id,
            budget_code,
            budget_name,
            budget_parent_id,
            budget_type_code,
            centralized_dept,
            unit,
            budget_table_id,
            cont,
            dscr
        )
        select
            budget_proj_id,
            budget_code,
            budget_name,
            budget_parent_id,
            budget_type_code,
            centralized_dept,
            unit,
            #{budgetTableIdNew,jdbcType=INTEGER},
            cont,
            dscr
        from bms_budget_table_proj
        where budget_table_id = #{budgetTableId,jdbcType=INTEGER}
    </insert>

    <update id="updateTableProj">
        update bms_budget_table_proj set econ_sub = #{econSub,jdbcType=VARCHAR}
        where budget_code = #{budgetTypeCode,jdbcType=VARCHAR}
        and budget_table_id = #{budgetTableId,jdbcType=INTEGER}
    </update>
</mapper>
