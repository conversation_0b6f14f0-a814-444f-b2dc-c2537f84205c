<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.dispose.mapper.write.BmsDisposeBudgetTypeWriteMapper">
    <update id="updateBmsBudgetType">
        update bms_budget_type
        <set>
            <if test="budgetTypeId != null and budgetTypeId != ''">
                budget_type_id = #{budgetTypeId,jdbcType=VARCHAR},
            </if>
            <if test="budgetTypeCode != null and budgetTypeCode != ''">
                budget_type_code = #{budgetTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="budgetTypeName != null and budgetTypeName != ''">
                budget_type_name = #{budgetTypeName,jdbcType=VARCHAR},
            </if>
            <if test="budgetTypeParentId != null and budgetTypeParentId != ''">
                budget_type_parent_id = #{budgetTypeParentId,jdbcType=VARCHAR},
            </if>
            <if test="flag != null and flag != ''">
                flag = #{flag,jdbcType=VARCHAR},
            </if>
        </set>
        where budget_type_id = #{budgetTypeId}
    </update>


    <update id="saveBmsBudgetType">
        insert into bms_budget_type (
            budget_type_code,
            budget_type_name,
            budget_type_parent_id,
            hospital_id,
            flag
        )
        values(
            #{budgetTypeCode,jdbcType=VARCHAR},
            #{budgetTypeName,jdbcType=VARCHAR},
            #{budgetTypeParentId,jdbcType=VARCHAR},
            #{hospitalId,jdbcType=VARCHAR},
            #{flag,jdbcType=VARCHAR}
        )
    </update>

    <!--删除当前节点以及其子节点-->
    <delete id="deleteBmsBudgetType">
        delete from bms_budget_type where
        budget_type_id in (
            select a.budget_type_id from
                (with RECURSIVE temp AS (
                    select * from bms_budget_type r where budget_type_code = #{budgetTypeCode}
                    UNION ALL
                    SELECT b.* from bms_budget_type b, temp t where b.budget_type_parent_id = t.budget_type_code
                )
                select * from temp) a
                )
    </delete>

</mapper>