<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.dispose.mapper.write.BmsDisposeFlowDetailWriteMapper">


    <delete id="deleteByFlow">
        delete from bms_flow_detail where budget_flow_code = #{budgetFlowCode}
    </delete>

    <update id="updateByFlow">
        update bms_flow_detail set status = 0 where budget_flow_id = #{budgetFlowId}
    </update>

    <update id="updateFlowDetailStatus">
        update bms_flow_detail
        set status = #{status}
        where flow_detail_id = #{flowDetailId}
    </update>
</mapper>